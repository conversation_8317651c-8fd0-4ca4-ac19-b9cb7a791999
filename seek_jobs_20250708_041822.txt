================================================================================
SEEK.CO.NZ JOB LISTINGS EXTRACTION
Generated: 2025-07-08 04:18:22
Total Jobs Found: 3
================================================================================

JOB #1
----------------------------------------
Title: Senior Full Stack Software Engineer
Company: Freelancer.com
Location: Location not found
Posted: Work for a global fintech industry leader in secure online payments
Job ID: 85538825
Link: https://www.seek.co.nz/job/85538825?ref=recom-homepage&pos=1#sol=c4246550bc6dd645e88e35c7750265a1619201b3
Quick Apply Available: True
Description:
Escrow.com is the world’s largest and most trusted online escrow service. Buyers
and sellers rely on us to hold their funds while multi-million-dollar deals are
completed. Domain names, vehicles,, even entire businesses… you name it, we help
people buy and sell it.Now we're looking for a Senior Full Stack Software
Engineer to join us in Sydney.If you join a mega-cap technology company as the
10,000th hire you will struggle to figure out the impact you're making. If you
join a startup, you might get to work on the latest fad, but you'll likely have
few mentors to learn from, and your time will be spent working on toy problems
that never change the world. Freelancer Group (that Escrow.com is part of),
isn't like that - we're a just-right-sized business. We're small enough you'll
get to really know the people you work with, but big enough you don't have to
worry that the next order of whiteboard markers will empty the company bank
account.Working for us isn't your typical cog-in-the-machine type of job. We
work on dynamic and interesting real world problems every day: this isn't a
boring career in corporate, every step bound by rules and heavy process. Here
you’ll get to work on a highly diverse, global set of internet-scale challenges
making a meaningful difference. Plus you get to rapidly build your own skills:
the nature of the work requires cross-functional knowledge.Working with us
you'll not only build (and test, and deploy!) features that scale, but you'll
have a direct impact on tens of millions of users worldwide. You'll architect
web applications, write optimised SQL for massive data sets, structure
extensible data and application logic, and design high-quality APIs. And you'll
do all that using a mix of open-source tools and best-in-class commercial
software.Our tech stack includes Python-based Flask (and some Golang)
microservices, React.js front-end code, and traditional SQL RDBMS systems.
Everything runs in Amazon Web Services. You can choose your own OS for your
company laptop - most of our devs use Ubuntu Linux, but it's all good!In order
to succeed in this role you will have: The ability to write code in a few web
development languages, such as Python, Golang, PHP, Node.js or Ruby Experience
with relational databases and SQL, preferably MySQL A background or interest in
web engineering at scale The ability to work in an agile team environment,
continuously shipping to customers Linux experience Experience building SaaS
products Cloud computing experience, preferably AWS Strong written and verbal
communication skills A strong sense of ownership and the ability to self-manage
work Strong troubleshooting skills and creative problem-solving ability
Experience with designing and implementing complex software systems Experience
with mentoring/training junior engineers To thrive you'll also bring: Experience
with SOA, microservice and containerised architectures Familiarity with
development of public-facing APIs Experience with a current-day Javascript
framework, such as Angular, React, or Vue Experience with TypeScript A strong
understanding of computer networking concepts and practices A sound
understanding of OWASP and modern web security practices A passion for open-
source tooling and libraries We'd consider it a bonus if you also brought:
Strong academic achievements MS or PhD degrees A Github/Gitlab page with
contributions to open source software Experience leading an engineering team
What we offer in return: A meritocratic culture with the ability to take
ownership and fast track your career growth. We promote from within! Weekly Town
Halls with the all-important CEO Q&A session (you really can ask him anything,
and people do)! Catered Friday lunches - We finish each week with a catered
lunch. Every week comes from a different local restaurant. Fully stocked
kitchens - yes, we do have beer taps (and the bar has a killer view!) Hack-a-
thons - Each quarter, teams compete company-wide hacking out solutions to their
own selection of customer problems and win prizes. The 2-day event is filled
with games, events, shows, food and more. Fun events. Once, we built a soccer
stadium in the office with two tonnes of turf… Our office at Grosvenor Place -
Home of Freelancer (and Escrow) HQ, this iconic building has harbour views and
offers weekly (complimentary!) classes/activities/events But wait, there's more:
Working with Freelancer Group, our parent company, you get to change lives every
day – everything we do as part of our jobs contributes to improving the lives of
our users on a global scale Plenty of opportunities for growth - you get to work
with different tech stacks, on different problems all the time. You won't get
bored, and you will get plenty of chances to learn how things work!

================================================================================

JOB #5
----------------------------------------
Title: Software Engineer (Developer)
Company: Peoplebank Australia ACT
Location: Location not found
Posted: Software Engineer/Developer - initial 12 months contract + extension
Job ID: ********
Link: https://www.seek.co.nz/job/********?ref=recom-homepage&pos=7#sol=d1c77266f683a0f0c2938d4c882256687ae7e58e
Quick Apply Available: True
Description:
About Peoplebank: Peoplebank, part of RGF Staffing ANZ, is a leading talent
solutions provider. We place the brightest IT & digital professionals into some
of the region’s top employers. We are expert recruiters who have been placing IT
& digital specialists with some of the most dynamic and recognised organisations
in the Asia Pacific region for over 34 years. About the Role: Our Federal
Government Client is seeking a Software Engineer (APS6 Equivalent). This is a
long-term contract role for 12 months with a 12-month extension term (1
extension total), located at our client’s locations across ACT, NSW, NT, QLD,
SA, TAS, and WA with hybrid working arrangements. As the selected candidate, you
will: • Design and implement software systems and integrations based on best
practices and company architecture • Develop and deploy APIs using languages
like Golang and JavaScript/TypeScript • Build and manage CI/CD pipelines using
tools such as GitHub/GitLab, Artifactory, and Vault • Deploy containerised
applications using Docker, Kubernetes, and Helm • Write Infrastructure as Code
using Terraform on AWS • Build accessible, standards-compliant web applications
using React and TypeScript • Engage in regular code reviews to ensure high
standards and quality • Work closely with Business Analysts, Project Leads, QA,
and IT teams to ensure solutions are robust, testable, and secure • Apply agile
methodologies and DevOps practices throughout the software development lifecycle
• Contribute to documentation and continuous improvement processes To be
successful in this role you should have: • Experience building complex APIs
using Golang and JavaScript/TypeScript with REST and GraphQL patterns • Solid
experience in AWS infrastructure and Terraform • Experience deploying
applications into Kubernetes with Docker and Helm • Strong skills in developing
accessible web applications using React and TypeScript • Hands-on expertise in
CI/CD practices and tooling including GitHub/GitLab, Artifactory, and Vault •
Knowledge of WCAG 2.1 web accessibility standards It is desirable if you have: •
Experience in mobile application development using React Native or similar
frameworks • Skills in database deployment and management (Postgres, MySQL) •
End-to-end SDLC experience from design through deployment and long-term support
• Proficiency in writing unit, component, integration, and functional tests •
Experience collaborating closely with QA teams for integration and end-to-end
testing Due to security clearance requirements for this role, candidates must be
Australian citizens with the ability to obtain Negative Vetting Level 1
clearance. Benefits of being a Peoplebank candidate: • Personalised Attention:
You’ll be assigned a dedicated Account Manager to support your unique needs •
User-Friendly Services and Ongoing Support: Easy online timesheet lodgement,
plus full access to contractor care, payroll, and admin support throughout your
contract • Choice and Flexibility: A wide variety of contracting options
tailored to your preferences and circumstances • Industry-Leading Payroll
Services: Enjoy the reliability of accurate, on-time weekly payments •
Contractor Satisfaction Focus: Our goal is to be your top choice for IT
contracting and permanent roles—with your success at the centre of everything we
do Apply now for immediate consideration – contact Mariz Arroyo on 02 6245 1741
/ <EMAIL> quoting Job Reference: # 267214. Applications
close: Friday, 11 July 2025 Please note: Only candidates that meet the above
criteria will be contacted. Thank you for your interest in the position.

================================================================================

JOB #7
----------------------------------------
Title: Program Lead – Digital Transformation
Company: Ormond College
Location: Location not found
Posted: Join a welcoming team that values diversity and collaboration.
Job ID: 85426933
Link: https://www.seek.co.nz/job/85426933?ref=recom-homepage&pos=10#sol=eabd8c66a929c424ed5becef8be37a8c4316efcc
Quick Apply Available: True
Description:
Program Lead – Digital TransformationOrmond College, University of
MelbournePart-time 3 days per week (0.6 FTE) | Fixed-term: 18 monthsJoin one of
Australia's most distinguished academic communities. Ormond College is the
largest residential college at the University of Melbourne and home to the Wade
Institute of Entrepreneurship. We are seeking a strategic and experienced
Program Lead to drive our digital transformation agenda.About the RoleReporting
to the Director of Technology, this part-time position leads a portfolio of key
digital initiatives that enhance the student, staff and donor experience. You
will coordinate cross-functional teams, vendors, and senior stakeholders to
deliver projects such as CRM and LMS implementations, student mobile Apps and
data analytics platforms.What You'll Bring 5+ years' experience in project
leadership, ideally in education or not-for-profit sectors Strong stakeholder
engagement and communication skills Proven ability to manage budgets, risks and
governance Knowledge of systems such as CRM, FMS, LMS and Microsoft 365 PMP,
PRINCE2 or Agile certification is highly regarded Key Project Includes CRM for
student/donor engagement LMS and student App rollout Finance system upgrade Data
and analytics framework development Why OrmondJoin a values-driven organisation
that fosters leadership, learning and community. Our alumni include Nobel
Laureates, Prime Ministers and Olympians. Help shape the digital future of a
historic and forward-thinking institution.<NAME_EMAIL>
for a copy of the position Description.We value diversity and welcome applicants
from all backgrounds. When you apply, please share your pronouns. Our hiring
decisions are based on your experience, skills and your potential contributions
to Ormond College and our community. We are committed to providing a fair and
inclusive interview process and will make reasonable adjustments as needed.
Please inform is if you have any specific needs to ensure your full
participation.

================================================================================

================================================================================
END OF REPORT
================================================================================
