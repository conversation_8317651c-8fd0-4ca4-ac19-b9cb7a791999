================================================================================
SEEK.CO.NZ JOB LISTINGS EXTRACTION
Generated: 2025-07-07 21:16:20
Total Jobs Found: 38
================================================================================

JOB #2
----------------------------------------
Title: Solutions Architect
Company: Talent – Specialists in tech, transformation & beyond
Location: Location not found
Posted: Engaging digital transformation initiatives
Job ID: 85298122
Link: https://www.seek.co.nz/job/85298122?ref=recom-homepage&pos=3#sol=d275eec4c7c60eed296152d2ad1e97696bec8389
Quick Apply Available: True
Description:
Opportunity knocks: Are you a Solutions Architect looking for your next
contracting challenge? We're on the lookout for a hands-on Solutions Architect
to join an exciting integration project focusing on building digital customer
portal on a 6-month contract. Our client are transforming their customer
experience and need your expertise to make it happen. About you?Proven
experience in building and architecting digital customer portalsDeep expertise
in designing and integrating RESTful APIsExperience building in Kubernetes,
.Net, and ReactExcellent communication and collaboration abilitiesA background
in Insurance or financial services will be highly advantageousComfortable
working across teams to deliver, scalable, customer-centric solutions Next
Steps: If this sounds like you and you want to know more then APPLY! Today and
let's chat further!

================================================================================

JOB #6
----------------------------------------
Title: Technology Experience Specialist (Networking & Streaming)
Company: Rhema Media Inc.
Location: Eden Terrace, Auckland
Posted: Christian media broadcaster
Job ID: 85221483
Link: https://www.seek.co.nz/job/85221483?ref=recom-homepage&pos=8#sol=b968b79cb949904dbb0c00813c112d3093461aaa
Quick Apply Available: True
Description:
Technology Experience Specialist (Networking & Streaming) Rhema Media | Auckland
CBD (Eden Terrace) Are you technically curious, administratively sharp and
passionate about advancing God’s kingdom through media? Join our Network
Operations team and play a pivotal role in delivering seamless, high-quality
audio and video content that connects New Zealanders to Christ. About Us Rhema
Media has been a trusted Christian media voice since 1968, growing from one
radio station to a multi-platform ministry of radio, TV, print and online. We
live and breathe our Christian ethos and values—serving our community with
integrity, compassion and excellence. The Role As a Technology Experience
Specialist, you’ll support Rhema Media’s broadcast and digital reach as well as
content-creation platforms—ensuring our audiences enjoy uninterrupted live and
on-demand experiences. Your core purposes will be: Delivery & Reach: Support the
end-to-end delivery of our services over IP networks and CDNs, maintaining
industry best-practice availability and performance. Content-Creation Platforms:
Provide hands-on support for our AoIP studios, radio/TV and podcasting
systems—enabling smooth production workflows. Operational Infrastructure: Act as
secondary ICT support—helping triage and resolve Tier 1 requests and liaising
with external tech partners. Rapid Learning & Innovation: Master new tools,
protocols and streaming methods quickly—so Rhema Media can adopt emerging
technologies without disruption. You’ll thrive in a varied, hands-on role—with
occasional early starts or event support—where every day brings new challenges
and learning opportunities. Who You Are Essential Demonstrated ability to
troubleshoot IP networks, CDNs and streaming protocols (HLS, DASH, RTP/SRT)
Experience supporting AoIP/studio environments or broadcast-adjacent systems
Strong administrative discipline: clear documentation, change-control and run-
book maintenance Excellent communicator: able to explain technical concepts to
non-technical teammates Desirable Degree or equivalent in IT, broadcast
engineering or related field Familiarity with monitoring/automation tools (e.g.
Zabbix, Power BI) Previous exposure to RCS Zetta, Wheatnet/Dante, NDI or Cinegy
platforms Attributes Curious learner | Creative problem-solver | Meticulous &
organised | Team-oriented | High integrity We especially encourage applications
from recent graduates and early-career professionals. What We Offer Growth &
Mentorship in a future-focused media environment—learn on the job alongside
seasoned technical leaders Competitive package & perks: salary plus health
insurance, life cover, birthday leave and free onsite parking (first come, first
served). Culture & connection: An upbeat and supportive workplace culture that
includes various staff functions and events throughout the year. Ready to shape
the future of digital ministry? Click Apply on Seek to submit your CV and a
cover letter—tell us how your technical strengths and faith align with Rhema
Media’s mission and values. Only shortlisted candidates will be contacted.

================================================================================

JOB #7
----------------------------------------
Title: Salesforce Architect
Company: RWA People
Location: Auckland CBD, Auckland
Posted: 6 month contract
Job ID: 84804419
Link: https://www.seek.co.nz/job/84804419?ref=recom-homepage&pos=9#sol=9d97cf142c461733be7ba77d9ccc20f4a28f03b0
Quick Apply Available: True
Description:
We are seeking a skilled Salesforce Architect/Developer to support the
AgentForce project by supplementing the current Salesforce team’s capacity. This
role is critical in ensuring the successful delivery of multiple strategic
initiatives, including internal training, testing frameworks, cost attribution
modelling, and Einstein integration. Key Responsibilities: Act as a key
contributor to Salesforce architecture and development activities across the
AgentForce project. Supplement and support existing team members, particularly
to free up the internal Salesforce SME for upskilling and high-priority focus
areas. Assist in the development and testing of new AgentForce functionality
within Salesforce, including the use of CRM data and Einstein features. Co-
design testing frameworks and guardrails to ensure scalable and observable
platform usage. Participate in regular (daily/weekly) collaboration calls with
the Salesforce platform team. Engage with stakeholders to align on roadmap
expectations, delivery timelines, and dynamic work prioritisation. Help
troubleshoot and refine use cases related to CRM-integrated bot functionality.
Provide technical guidance and best practice recommendations around Salesforce
functional engineering and Einstein layer understanding. Key Skills &
Experience: Proven experience as a Salesforce Developer and/or Architect. Strong
understanding of Salesforce CRM, including platform capabilities and
customisation. Experience with Salesforce Einstein or AI-driven tools within the
Salesforce ecosystem. Comfortable with testing methodologies, framework design,
and observability principles. Strong communication skills with experience in
stakeholder engagement and working within cross-functional teams. Ability to
adapt in a fast-paced, evolving environment with shifting priorities. Nice to
Have: Experience with cost modelling and attribution frameworks. Familiarity
with chatbot or virtual agent development within Salesforce. Previous
involvement in agile project delivery or dynamic workloads. Telco Experience You
must the legal right to work in New Zealand. Unfortunately we cannot consider
overseas applications as this is an ASAP start If this sounds like the
opportunity you have been waiting for and you have the skills listed above,
please apply today!!!Click Apply Now or call RWA on 09 579 7929 for more
information.Only people with the right to live and work in New Zealand may apply
for this role.Don't forget to visit our website for more vacancies and job
opportunities: www.rwa.co.nzJob ref: DD3928807

================================================================================

JOB #8
----------------------------------------
Title: Full Stack Developer
Company: Amtrax Ltd
Location: Howick, Auckland
Posted: 5d ago
Job ID: 85313980
Link: https://www.seek.co.nz/job/85313980?ref=recom-homepage&pos=10#sol=628c543d1de8abbcf2c4da7d018be71b46449639
Quick Apply Available: True
Description:
import { Component } from '@angular/core';@Component({selector:
'fullStackDeveloper',template: '{{ roleTitle }}About Us{{ companyName }} is a
development company that provides a variety of solutions for various companies
in many different Industry sectors. We excel in our ability to produce clever
technical solutions that help them grow their business and improve their
efficiency.The RoleThis is an opportunity to work in a small friendly team. Most
of our projects are in the Finance sector. You must be a self-starter capable of
handling many concurrent projects, as well as building new applications. Someone
who is able to focus and get the job done with minimal supervision. You will be
working on up-to-date technologies.Location and Hours Full-time 40 hours per
week. Knowledge & Experience Excellent English and communication skills Be
living in NZ already and be able to commute to {{ location }}, so would suit
someone based in East Auckland Be eligible to work in New Zealand (i.e.
citizenship, work visa or resident visa) At least <%= minAngular/ReactExperience
%> years Angular/React programming C# experience is essential DevExpress MSSQL &
good general MSSQL query skills Rewards Rate based on your experience. There
will also be ample opportunity to expand your skill set.@if (isEligible()) {We
look forward to receiving your application!} @else {Unfortunately, you do not
meet the eligibility criteria for this role.}',})export class
FullStackDeveloperComponent {companyName: string = "Amtrax Ltd";roleTitle:
string = "Full Stack Developer";location: string = "Howick, NZ";javaExperience =
4;minFullStackDevelopmentExperience: number = 4;canReadThis: boolean =
true;isLivingInNZ: boolean = true;isEligibleToWorkInNZ: boolean =
true;has.NetExperience: boolean = true;hasAngularExperience: boolean =
true;hasC#Experience: boolean = true;hasVisualStudioExperience: boolean =
true;hasMSSQLExperience: boolean = true;hasGeneralMSSQLQuerySkills: boolean =
true;canWorkWithoutFullSpecification: boolean = true;canFocusOnDeliverables:
boolean = true;isEligible(): boolean {return canReadThis&& javaExperience >=
minFullStackDevelopmentExperience&& isLivingInNZ&& isEligibleToWorkInNZ&&
has.NetExperience&& hasAngularExperience&& hasC#Experience&&
hasVisualStudioExperience&& hasMSSQLExperience&& hasGeneralMSSQLQuerySkills&&
canWorkWithoutFullSpecification&& canFocusOnDeliverables;}}

================================================================================

JOB #9
----------------------------------------
Title: Senior Golang Engineer
Company: Bidfood Limited
Location: Epsom, Auckland
Posted: Shape Go adoption in a global business
Job ID: 85021650
Link: https://www.seek.co.nz/job/85021650?ref=recom-homepage&pos=11#sol=a567e95bcd0013689455906ea0cba67b3fb5aded
Quick Apply Available: True
Description:
About BidOneBidOne is a local New Zealand company with a truly global presence.
Every day, thousands of businesses around the world place orders with their
wholesale suppliers through our ecommerce and CRM platforms, spanning five
continents.At BidOne, we care about building solid software – and strong teams.
We have a collaborative, supportive culture with a shared sense of
responsibility for delivering technology that matters. Our software reflects the
real needs of the foodservice businesses we support, and because we sit at the
heart of their operations, reliability and trust are core to everything we
do.“Never lose an order” isn’t just a slogan – it’s how we work, together.The
RoleWe’re looking for a Senior Golang Engineer to join us on contract as we
modernise and replatform our ecommerce system. This is a 24-month fixed term
contract with potential to extend, based in Auckland with a hybrid working
model.You’ll be part of a friendly and capable engineering team working on
something that really matters: rebuilding our backend architecture using Go,
modern cloud-native tooling, and a clean, scalable microservices design.While
this is a contract role, you’ll have a voice in shaping how Go is adopted across
the business – from patterns and practices to mentoring others new to the
language. This role would suit someone who enjoys both writing quality code and
helping others do the same.Key ResponsibilitiesTechnical LeadershipContribute to
defining Go standards and patterns as part of our broader replatforming
journeyShare your experience to help other engineers grow – particularly those
new to GoLead by example in code quality, collaboration, and deliverySupport
architecture and design discussions with practical, Go-specific
insightsMicroservices DevelopmentDesign and build Go-based microservices that
are secure, scalable, and easy to understandBuild APIs and asynchronous
components in line with best practicesEnsure observability, performance, and
maintainability are baked in from the startEngineering PracticesSupport modern
practices like test-driven development, domain-driven design, and CI/CDUse AI-
assisted tools (Cursor AI, GitHub Copilot, ChatGPT) to enhance quality and
productivityParticipate in code reviews, documentation, and continuous
improvement conversationsCollaboration & DeliveryWork closely with product
owners, QA, DevOps, and other engineers to deliver customer valueProvide
technical guidance in a supportive and inclusive wayHelp build a positive,
respectful culture of learning and engineering craftWhat We’re Looking ForMust-
Have5+ years of professional Go development experienceExperience designing and
building distributed systems and APIsUnderstanding of microservices, REST, and
event-driven architecture (Kafka, NATS, Pub/Sub)Hands-on experience with Docker,
Kubernetes, CI/CD workflows, and automated testingA collaborative working style
and a willingness to mentor othersAbility to support engineering standards in a
growing teamNice-to-HaveExperience with GraphQL or Search as a
ServiceFamiliarity with observability tools (OpenTelemetry, Prometheus, Grafana,
Datadog)Experience with SQL and NoSQL databasesPrevious experience in ecommerce,
foodservice, or supply chain platformsWho You AreA thoughtful engineer who
values clean, maintainable codeA system thinker who enjoys breaking down
complexityA clear communicator who builds trust through knowledge-sharing and
empathyComfortable mentoring others, and open to learning from them tooFocused
on outcomes and collaboration over ego or hierarchyWe strongly encourage
applications from anyone who is underrepresented in tech. If this role sounds
like you but you’re not sure you tick every box — we’d still love to hear from
you.What We OfferA high-impact role in a major replatforming initiativeLong-term
contract stability with a modern tech stackHybrid working environment (3 days
in-office time in Auckland required)A respectful, collaborative team with a no-
blame cultureThe opportunity to help shape how Go is used across a global
businessA company that values people just as much as codeHow to ApplyPlease
submit your application through the following URL:
https://bidone.bamboohr.com/careers/71?source=aWQ9Mg%3D%3DOnly applications
received via this link will be considered.

================================================================================

JOB #10
----------------------------------------
Title: Senior Business Analyst
Company: Younity
Location: Auckland CBD, Auckland
Posted: 6 Month Contract
Job ID: 85360100
Link: https://www.seek.co.nz/job/85360100?ref=recom-homepage&pos=12#sol=622c8d6326ef1cb0c3eca93a014da4b55303bab7
Quick Apply Available: True
Description:
Business Analyst – Payments & Merchant Onboarding ProjectWe’re looking for a
proactive and detail-oriented Business Analyst to deliver a key integration
project within the Payments and Merchants space, supporting the onboarding of a
new subsidiary into an enterprise environment. You’ll work across multiple
business and technology teams to deliver clear, actionable analysis that
supports solution design and successful project delivery. What you’ll be doing:
Lead current and future state process mapping, identifying gaps and integration
opportunities. Gather, validate, and document business and technical
requirements (functional and non-functional). Complete impact assessments and
provide well-evidenced options and recommendations. Maintain high-quality
artefacts using established frameworks, templates, and governance processes.
Collaborate with stakeholders across business units, technology, and 3rd
parties. Support prioritisation, testing, business readiness, and implementation
planning. Manage Jira boards, including user stories, acceptance criteria, and
decision registers. What you’ll bring: Proven experience as a Business Analyst
in payments, financial services, or onboarding/merger projects. Strong
facilitation and stakeholder engagement skills. Excellent analytical thinking
and problem-solving capabilities. Ability to clearly communicate and influence
across diverse teams. Comfortable navigating enterprise governance and change
control processes. Experience supporting testing and business readiness
activities. Why apply? Join a business-critical project with impact across
payments and merchant platforms. Work with a highly collaborative team in a
fast-paced and complex enterprise setting. Use your analytical, communication,
and problem-solving skills to make a real difference. If you're a confident
communicator with strong BA fundamentals and payments experience, we want to
<NAME_EMAIL>***********

================================================================================

JOB #11
----------------------------------------
Title: Python Developer (Junior to Mid Level)
Company: OneReg
Location: Auckland CBD, Auckland
Posted: 4d ago
Job ID: 85422763
Link: https://www.seek.co.nz/job/85422763?ref=recom-homepage&pos=13#sol=5699a1b126a4c658b24b643431dac7c56576ffa6
Quick Apply Available: True
Description:
Python Developer (Junior to Mid-Level)📍 Location: Auckland⏳ Hours: Full-
timeAbout OneRegAt OneReg, we are transforming aviation compliance with cutting-
edge software that streamlines regulatory processes. Our platform provides real-
time oversight and unified information management, helping aviation
organisations operate more efficiently.We are looking for a Python Developer to
join our team and contribute to building and maintaining our software. If you
have experience with Python and a passion for developing scalable web
applications, we want to hear from you!What You’ll DoDevelop and maintain web
applications using Python (Django preferred).Design and build RESTful APIs and
integrate third-party services.Work with relational databases, including
database design and query optimization.Collaborate with front-end developers to
create intuitive and user-friendly interfaces.Troubleshoot and debug
applications to ensure high performance and security.Participate in Agile
development, working in a fast-paced, collaborative environment.Use Git for
version control and follow best coding practices.Contribute ideas to improve our
platform and explore innovative development opportunities.What You Need✅ Python
experience is a must – we will not consider candidates without Python skills.✅
Familiarity with Django (or willingness to learn it).✅ Understanding of
relational databases (PostgreSQL or similar).✅ Some knowledge of front-end
technologies (JavaScript, HTML, CSS) is a plus.✅ Strong problem-solving skills
and ability to work in a team.Must Have SkillsAt least 2 years commercial
experience with Python.Experience with asynchronous programming in
Python.Experience with Python web frameworks (Django, Flask) Experience with
code optimisation & efficiencyAdditional SkillsExposure to DevOps practices,
including CI/CD, Docker, and AWS.Familiarity with Agile methodologies
(Scrum/Kanban).Experience with data visualization tools.Why Join Us?Work on an
innovative product making an impact in aviation.Grow your skills with mentorship
and learning opportunities.Flexible working hours and hybrid options.Competitive
salary and benefits package.If you’re a junior to mid-level Python Developer
looking for an exciting challenge, apply now!

================================================================================

JOB #12
----------------------------------------
Title: Senior DevOps Engineer – Cloud Native (Contract)
Company: The Adviser Platform
Location: Auckland CBD, Auckland
Posted: 11d ago
Job ID: 85177777
Link: https://www.seek.co.nz/job/85177777?ref=recom-homepage&pos=14#sol=80dd486e85455ea0720587ef06a496f27f1d9d49
Quick Apply Available: True
Description:
Join a high-performing tech team driving a full cloud-native transformation.This
is a hands-on role for a seasoned DevOps professional who thrives on solving
complex problems and enabling modern engineering teams through smart
infrastructure, automation, and best practice DevOps.What’s in it for you?Work
onsite in a fast-paced environment with cutting-edge Azure techDeliver impactful
change in a business-critical transformationBe part of a collaborative and
capable engineering group6-month contract with potential extensions and
competitve rateWhat you’ll be doing:Help design and implement scalable cloud-
native solutions on Azure, focusing on modern deployment practicesDriving
infrastructure-as-code maturity using Terraform or Bicep, enabling version-
controlled, repeatable and reliable deploymentsOwning and modernising CI/CD
pipelines using GitHub Actions to support fully automated delivery
workflowsChampioning Kubernetes (AKS) across the environment, building secure
and efficient container platformsImplementing monitoring, logging, and alerting
via Datadog, and ensuring production systems are visible and resilientAdvocating
for DevOps best practices across the org – automation, continuous improvement,
collaboration, and “you build it, you run it”Working alongside security teams to
embed secure practices across infrastructure and codeTroubleshooting and
resolving complex issues across infrastructure and cloud-native appsWhat we’re
looking for:5+ years in DevOps / SRE / Cloud Engineering rolesExperience in
Azure services, including AKS, App Services, Networking, AAD, and
StorageAdvanced skills with Terraform or BicepExpert-level Docker & Kubernetes
(AKS preferred)Proven success implementing and improving CI/CD pipelinesStrong
scripting ability (e.g. PowerShell, Bash, Python)Confident with Git-based
workflows and branching strategiesAbility to communicate complex ideas clearly
and collaborate with engineers and stakeholdersNice to haves:Background in .NET
/ C# developmentExperience with Azure SQLFamiliarity with microservices
architectureAny Azure Security certifications (e.g. AZ-500)Apply now to join a
business investing heavily in engineering and automation. You’ll be trusted,
supported, and challenged in equal measure.

================================================================================

JOB #13
----------------------------------------
Title: Data & AI Consultant
Company: Potentia
Location: Location not found
Posted: Lead cutting-edge AI and ML initiatives.
Job ID: 85531034
Link: https://www.seek.co.nz/job/85531034?ref=recom-homepage&pos=15#sol=35641a2ba17e1cd34c8e68d7f7d180d55eb9f05a
Quick Apply Available: True
Description:
Deliverables/Purpose:You'll spearhead the technical design and delivery of
scalable AI and data solutions that drive real business impact. Working closely
with the Portfolio Manager, you'll triage and prioritise the team's backlog
whilst ensuring technical requirements are crystal clear. This role sits at the
sweet spot between strategy and implementation – you'll architect solutions,
mentor talented engineers, and get your hands dirty with coding when needed.The
Skills and Experience We Need You to Bring:7+ years in technical solution
design, data architecture, and database designStrong expertise in data
engineering, analytics, and BI tools (SQL, Python, Tableau, Power BI)Solid
DevOps experience with MLOps knowledge highly valuedHands-on experience with
cloud platforms (Google Cloud, AWS, or Azure)Production-grade deployment
experience with data pipelines and modelsProven leadership and mentorship
abilities with cross-functional teamsUnderstanding of data governance and
security best practicesAgile working methodologies experienceContract Details:
Initial contract term with strong potential for extensionLeading a diverse team
of data engineers, ML engineers, and data scientistsEstablished data programme
with significant enterprise backingHybrid working arrangements availableLooking
to start as soon as possibleContractor Benefits:Wellbeing Discovery Sessions$200
Health ContributionWelcome Packs & Birthday GiftsExclusive InvitesOptional Pay-
As-You-Go PI/PL InsuranceIf this sounds like the technical leadership challenge
you've been looking for, let's talk. Send me your CV and we'll explore how this
role could be your next career move.REF:18931

================================================================================

JOB #14
----------------------------------------
Title: Software Developer
Company: Acclaim Group Limited
Location: Rodney & North Shore, Auckland
Posted: Well established North Shore based company.
Job ID: ********
Link: https://www.seek.co.nz/job/********?ref=recom-homepage&pos=16#sol=335d7df10ec7a14154d98df327500823ed590d2b
Quick Apply Available: True
Description:
We have an opportunity for a customer serviced focused candidate with a software
development degree to join our successful team. With this role you will be part
of a development team, responsible for developing innovative solutions for our
clients. You must have the ability to:Engage well with clientsStrong
understanding of accounting principlesCode and test software based on
specification and designPresent ideas for system improvementsDocument changes
and update any relevant project management softwareThe ideal candidate will also
possess the ability to troubleshoot and deliver exceptional business solutions
for our customers. To be successful you will require a Computer Science degree
or similar and sound knowledge of:Accounting principlesMicrosoft SQL Studio and
Power BI, Microsoft reportsVisual StudioWeb application experience using
Microsoft technologies (.NET, C#, MS Sql Server)Have a high level of competency
/ conceptualised understanding in Databases, particularly SQL.Application
development experience Report writing tools.API/EDI development experience Based
in Albany, this role will require an individual with exceptional communication
skills, both written and verbal. We are a well-established IT company
specialising in Accounting software sales and support, software development and
computer network sales and support.Candidates will have superb verbal
communication skills and an excellent command of spoken and written English. If
you are interested in applying you need to have a NZ Permanent Residency or
Citizenship.

================================================================================

JOB #17
----------------------------------------
Title: Machine Learning Engineer - Pasture
Company: Halter
Location: Auckland CBD, Auckland
Posted: Work for a kiwi startup creating innovative, world first technology
Job ID: ********
Link: https://www.seek.co.nz/job/********?ref=recom-homepage&pos=20#sol=6991091aab4e72d8c5c9dfefcb8fb1b55cacee01
Quick Apply Available: True
Description:
About the RoleHalter’s Pasture team is dedicated to helping farmers better
manage their most valuable resource—pasture—for greater productivity, profit,
and sustainability. To provide the highest quality insights to farmers, we’re
building upon the bleeding edge of scientific advancements with unmatched access
to rich, novel data to develop a cutting edge agricultural modelling
system.You’ll play a pivotal role on the machine learning team within Pasture,
building advanced models of pasture growth and land properties that power
intelligent, farmer-facing tools. Applications could range from remote sensing
of pasture biomass, pasture quality estimation from photo and drone imagery, to
grazing interval simulation for advanced recommendations. You will be owning end
to end development of machine learning pipelines, advanced contemporary
modelling, to deployment and monitoring. You will work with great team-mates in
engineering, product and design to achieve outsized results.The impact of our
work is profound, and we take that responsibility seriously. As a member of the
Pasture team, you'll share our passion for technology and our commitment to
optimizing the management of agricultural land.You will be responsible
for:🌱Modeling, from biological processes to farm operations: You will drive
development of predictive systems to solve real-world challenges faced by
farmers.📚Research, from technical to agriculture: you will be required to
conduct extensive literature reviews, and stay ahead of the curve with respect
to both machine-learning systems, and pasture science.🛠️Model development:
design, dataset construction, training and optimization: Rarely do we utilize a
model off the shelf. You will own the architectural design, and all phases of
model development.👀Supporting your work in production: Halter engineers
demonstrate a high level of ownership. We support what we build, and we learn
from our systems behavior in the field.🧠Proactive innovation: As a member of the
data discipline it is expected that you will exercise a high degree of
creativity to proactively deliver innovative ideas.Who are we looking for?You
are a high-output generalist: You will be required to work with autonomy across
infrastructure, databases, simulation, and ML development. You will be restless
with respect to advancing your own impact across the stack, leveraging AI
companion tools and agents.You are a machine learning expert: You will be
joining a highly capable team of technologists with deep experience in machine
learning and complex systems. You will be required to provide extra strength in
machine-learning theory, and its application to maximizing pasture
efficiency.You have experience delivering highly complex predictive systems at
the bleeding edge of research. Experience translating academic research into
practical applications that add value to the world. You may be required to work
on a range of problem-sets, from multi modal time-series modelling, to
simulation systems.You are deeply curious and willing to become an expert in
pasture-based farming. Our farmers expect us to know farming as well as they do,
understand why they do what they do, and identify where they can do it better.
Curiosity beyond technology and into the agricultural domain will be a strong
motivation for you.You are a product-minded, strategic thinker: Halter is in the
enviable position of possessing an immense depth of farm-data. You will be adept
at mapping opportunities across our data-lake to systems that add positive value
to farmers and ranchers.You embrace uncertainty and ambiguity. Uncertainty is
intrinsic in the ambition of our team and company. We take big, calculated
strides, and we embrace the challenge to do so.You are a highly effective
collaborator and incredible teammateMore concretely:You have a deep academic
and/or professional background applying machine learning to solve real-world
problems (e.g. a research oriented postgraduate degree in mathematics or
computer science, with relevance to modelling with deep neural
networks)Demonstrated experience designing, deploying, and monitoring machine
learning systems to productionStrong causal reasoning and an ability to design
models that reflect real-world dynamicsStrong data intuition and ability to work
with noisy, high volume, high-dimensional datasetsFluency in Python and
experience contributing to or working on complex, collaborative
codebasesProficiency in contemporary AI companions and tooling for software and
engineering productivity (e.g., Copilot, ChatGPT, Cursor)Excellent communication
skills and a collaborative working style across disciplinesSome experience in
advanced ML architectures (Transformers or SSM), agent-based systems or
recommendation systems is a plus, but not required.Why our team loves working at
HalterEvery now and again a company comes along that disrupts and transforms an
entire industry, leaving society in a better place. Few people get the
opportunity to work at companies like these. Halter is an incredible technology
transforming an essential industry. We have a genuine and shared connection to
our mission to make a difference in the world.Our product changes the lives of
our customers, directly improving their livelihoods and their lifestyle. Your
work matters.Dedicate yourself to solving real-world problems alongside an epic
team in a high-growth culture. The excitement, risk and reward of a high-growth
technology scale-up on the global stage. Our ideas are truly valued, we are able
to move fast, and our impact is real.State-of-the-art, dog-friendly office
that’s been thoughtfully designed right in the heart of Auckland city. Did we
mention dogs and cows?Healthy body, healthy mind. We’re partnered with Southern
Cross Health Insurance to support your well-being.We offer 6 months of fully
paid parental leave for primary caregivers, 4 weeks of fully paid secondary
caregiver leave and many other parental benefits that support you and your
family.Our personal growth is important. Halter offers an annual $1000 self-
development budget to be used for anything that fuels personal growth.Our time
to recharge is valued, we’re offered wellness leave and unlimited paid annual
leave.We offer an inclusive and attractive remuneration package made up of
salary, benefits and an employee stock ownership plan.Our Office First
ApproachThere’s a reason you visit your friends in person, live with your family
and don’t do dinners over Zoom. Humans are wired for connection. We believe a
world-class, in-person office culture is the best way for high-performing teams.
Being office first is a core pillar of our culture. We believe in-person
connections are key to driving your own growth, learning, impact, and building
genuine long-lasting relationships. Strong relationships make it easier to
disagree, give feedback, and do meaningful and aligned work. We don’t like
having heaps of rules or policies, but this means having strong, trusted
relationships is critical.We’re office first, not office only. This means
working from the office everyday is our default setting, but we flex when we
need to. We have a high-trust culture, so everyone is trusted to do what’s best
for Halter. Our office vibe is something special, it’s hard to describe until
you’re here, but people at Halter who have come from fully remote or hybrid
companies say they could never go back - the high energy and spectacular people
they are now surrounded by everyday makes work so enjoyable. Your growth, your
learning and your impact is truly unlimited here, and a big part of that comes
from being together solving problems, innovating, building context, and
constantly learning from each other.About HalterAt Halter, we’re on a mission to
enable farmers and graziers to run the most productive and sustainable
operations. Our customers are using Halter to break free from the time-intensive
constraints of conventional practices. Imagine watching 500 cattle stand up and
walk calmly towards their next break? No quad bikes, no dogs, no fences. Just a
group of cattle walking at their own pace. People say it looks like magic. Our
customers are revolutionizing grazing with Halter. It's changing lives and
transforming an industry. People join Halter to do meaningful work. By joining
us you’ll be solving challenging problems within a talented team and a culture
built for high performance. Our team out-think, out-work and out-care. We’re
committed to delivering real change in the world - this isn’t easy, and in
truth, we love that it’s hard. We’re backed to deliver on a mission that matters
by Tier 1 investors including Bessemer Venture Partners, DCVC, Blackbird, Promus
Ventures, Rocket Lab’s Peter Beck and Icehouse ventures.To find out more, visit
our careers website (www.halterhq.com/careers), LinkedIn & Instagram
(@lifeathalter).Join our teamHalter is committed to promoting a diverse and
inclusive workplace — a place where we can each be ourselves and do the best
work of our lives. Research shows that while men apply to jobs when they meet an
average of 60% of the requirements, women and under-represented groups of
candidates tend to only apply when they meet every requirement. If you think you
have what it takes but don’t necessarily tick every requirement on this job
description, please still get in touch and apply to Halter. We’d love to chat to
see if you’ll be an epic fit!If this opportunity sounds like you, please apply
below by sending through your cover letter explaining why you’re excited about
this role and working at Halter, along with your CV, and we’ll be in
touch!Please also feel free to check out the careers and culture page for more
information on working at Halter and don't forget to follow us on LinkedIn &
Instagram (@lifeathalter).

================================================================================

JOB #19
----------------------------------------
Title: Software Developer
Company: Sandfield Associates
Location: Auckland CBD, Auckland
Posted: Have direct interaction with clients
Job ID: 85450611
Link: https://www.seek.co.nz/job/85450611?ref=recom-homepage&pos=22#sol=bd54887b1bec9a1a0feb8819a3dcb16f815ee3a4
Quick Apply Available: True
Description:
Do you want to work on projects that make a real difference for leading
companies across Australasia? Do you want to work in a place where you can call
home, where you can get ahead in your career without having to move on? At
Sandfield, we build smart software solutions for top brands, and we're looking
for talented developers to join our team.About SandfieldWe're a Kiwi-owned
company that's been around since 1989. Our work facilitates high-performance
supply chains for Mainfreight, Qube, HW Richardson, and Booths, and keeps
millions of orders and data points flowing for Foodstuffs. It also manages
business finances for Team Global Express and Green Acres, helps fill seats on
the Interislander for KiwiRail, looks after security services for First Security
and books parking spaces through ParkMate, plus so much more.We pride ourselves
on being different from your typical tech company. At Sandfield you will: Work
on interesting projects: We guarantee your ‘every day’ will be interesting,
varied, challenging, and fun! You’ll get to work with a range of other smart
developers of all levels, who also happen to be really great people. Direct
client interaction: Our developers deal directly with our clients, and we spend
just as much time nurturing soft skills, such as communication and business
acumen, as we do helping our developers become even more ‘ninja’ at their
coding. A supportive team: Our collaborative culture is at the centre of
everything we do at Sandfield, and it makes it a pretty special place to work.
We know this because people also don’t tend to leave, the average length of stay
is more than five years. Check out what our people say about working at
Sandfield.Our tech stackWe primarily use the Microsoft stack to build web-based
applications, with some opportunities for mobile development. Full-stack
experience is ideal, but not required. We'll support you in developing your
skills.Requirements: Experience building web applications using .NET - WebAPI,
MVC or WebForms HTML, CSS and JavaScript. Working knowledge of functional React,
in either 18.x or 19 Fluent in Typescript Familiar with Git, including standard
development lifecycle Experience working with C# and SQL databases An interest
in all aspects of the SDLC, including talking to clients, is ideal Ideally, you
will have some of these skills: Demonstrated experience with Azure environments,
especially Azure DevOps (ADO) pipelines and GitHub Actions for CI/CD Hands-on
experience with Infrastructure as Code (IaC) with Bicep or Terraform Familiarity
with Azure services such as Azure Static Web Apps, Azure App Services, Azure API
Management, Azure Keyvault, Azure B2C, or Azure Application Insights Experience
with integrating cloud applications with on-premise resources Familiarity with
React Native What's in it for you? Fast-track your career: We reward initiative
and performance with rapid advancement opportunities. Never a dull moment: Enjoy
diverse projects and challenges. Positive vibes only: Our flat team structure
fosters a collaborative and energetic atmosphere. Work your way: Benefit from
flexible working arrangements. Grow beyond coding: Develop valuable soft skills
through dedicated training. Have some fun: Enjoy regular social events, "Fun
Food Evenings," and a well-stocked fridge. We offer a rewarding and engaging
work experience where you can grow professionally and personally.Make your next
move with us!If you’re happy with just being a number at a big brand tech
company so your Mum can brag at Bridge Club...we’re probably not for you.But if
you want to join a great team and be part of the driving force behind innovative
tech solutions for a range of top companies, let’s talk.You can check out more
about Sandfield and what it’s like working for us here PLEASE DO NOT APPLY if
you are not in NZ (unless you are NZ citizen)

================================================================================

JOB #20
----------------------------------------
Title: Lead Frontend Engineer
Company: Halter
Location: Auckland CBD, Auckland
Posted: Work for a kiwi startup creating innovative, world first technology
Job ID: 84869155
Link: https://www.seek.co.nz/job/84869155?ref=recom-homepage&pos=23#sol=27b7510303879fa5709471918bafb1fce8e30f11
Quick Apply Available: True
Description:
At Halter, we know how much farmers care - for their animals and their pasture.
But the tools they rely on haven’t kept up, and we’re on a mission to change
that. Halter helps farmers make smarter decisions - fast. We reckon good pasture
management is the key to more productive and sustainable farms. More grass.
Healthier soil. Happier animals. And more time back for the people doing the
work.About the roleWe’re looking for an epic frontend engineer with a product-
obsessed mindset and the ability to lead - someone who cares about what we
build, and why we’re building it. This is a rare chance to solve hard problems
no one’s tackled before. As a lead, you’ll work across our mobile and web apps,
writing clean, reliable code that makes a real difference. We’re forward-looking
and embracing what AI means for how we build. You’ll be hands-on and
collaborative; shipping features, improving performance, and shaping how we
work. We care about how you think and act: curious, thoughtful, practical, and
someone who takes absolute ownership.What you’ll doLead the architecture and
implementation of key web and mobile features.Bring a strong product lens -
you’ll work closely with design and product to make smart trade-offs and ship
high-impact features fast.Use AI tools (like Copilot or Cursor) without cutting
corners on readability or quality.Set the standard for how we build: thoughtful
code, great performance, clean design, and systems that scale.Help evolve our
design system and frontend patterns - Tailwind and Storybook are part of the
picture.Support and guide other engineers, reviewing code and sharing context
freely.Stay hands-on. You’ll be close to the work, helping shape the
product.Keep frontend experiences fast and reliable, including when things go
sideways. We support our systems end-to-end.You’ll be great if youAre
experienced with React and have strong opinions on frontend architecture and
design systems.Think like a product engineer: you care about the customer
experience, not just clean code, and have the experience to back this.Know how
to use AI tools well - not just as shortcuts, but to lift team output and focus
on the harder stuff.Have a track record of shipping software in cross-functional
teams.Are comfortable in fast-moving, ambiguous environments where adaptability
beats perfection. We work hard.Understand how to build for performance,
accessibility, and maintainability.Bonus points if you’ve worked in React
Native, scaled design systems, worked with native mobile products, or mentored
other engineers.A degree in software engineering or equivalent is a bonus, but
not a must-have.Why our team loves working at HalterEvery now and again a
company comes along that disrupts and transforms an entire industry, leaving
society in a better place. Few people get the opportunity to work at companies
like these. Halter is an incredible technology transforming an essential
industry. We have a genuine and shared connection to our mission to make a
difference in the world.Our product changes the lives of our customers, directly
improving their livelihoods and their lifestyle. Your work matters.Dedicate
yourself to solving real-world problems alongside an epic team in a high-growth
culture. The excitement, risk and reward of a high-growth technology scale-up on
the global stage. Our ideas are truly valued, we are able to move fast, and our
impact is real.State-of-the-art, dog-friendly office that’s been thoughtfully
designed right in the heart of Auckland city. Did we mention dogs and
cows?Healthy body, healthy mind. We’re partnered with Southern Cross Health
Insurance to support your well-being.We offer 6 months of fully paid parental
leave for primary caregivers, 4 weeks of fully paid secondary caregiver leave
and many other parental benefits that support you and your family.Our personal
growth is important. Halter offers an annual $1000 self-development budget to be
used for anything that fuels personal growth.Our time to recharge is valued,
we’re offered wellness leave and unlimited paid annual leave.We offer an
inclusive and attractive remuneration package made up of salary, benefits and an
employee stock ownership plan.Our Office First ApproachThere’s a reason you
visit your friends in person, live with your family and don’t do dinners over
Zoom. Humans are wired for connection. We believe a world-class, in-person
office culture is the best way for high-performing teams. Being office first is
a core pillar of our culture. We believe in-person connections are key to
driving your own growth, learning, impact, and building genuine long-lasting
relationships. Strong relationships make it easier to disagree, give feedback,
and do meaningful and aligned work. We don’t like having heaps of rules or
policies, but this means having strong, trusted relationships is critical.We’re
office first, not office only. This means working from the office everyday is
our default setting, but we flex when we need to. We have a high-trust culture,
so everyone is trusted to do what’s best for Halter. Our office vibe is
something special, it’s hard to describe until you’re here, but people at Halter
who have come from fully remote or hybrid companies say they could never go back
- the high energy and spectacular people they are now surrounded by everyday
makes work so enjoyable. Your growth, your learning and your impact is truly
unlimited here, and a big part of that comes from being together solving
problems, innovating, building context, and constantly learning from each
other.About HalterAt Halter, we’re on a mission to enable farmers and graziers
to run the most productive and sustainable operations. Our customers are using
Halter to break free from the time-intensive constraints of conventional
practices. Imagine watching 500 cattle stand up and walk calmly towards their
next break? No quad bikes, no dogs, no fences. Just a group of cattle walking at
their own pace. People say it looks like magic. Our customers are
revolutionizing grazing with Halter. It's changing lives and transforming an
industry. People join Halter to do meaningful work. By joining us you’ll be
solving challenging problems within a talented team and a culture built for high
performance. Our team out-think, out-work and out-care. We’re committed to
delivering real change in the world - this isn’t easy, and in truth, we love
that it’s hard. We’re backed to deliver on a mission that matters by Tier 1
investors including Bessemer Venture Partners, DCVC, Blackbird, Promus Ventures,
Rocket Lab’s Peter Beck and Icehouse ventures.To find out more, visit our
careers website (www.halterhq.com/careers), LinkedIn & Instagram
(@lifeathalter).Join our teamHalter is committed to promoting a diverse and
inclusive workplace — a place where we can each be ourselves and do the best
work of our lives. Research shows that while men apply to jobs when they meet an
average of 60% of the requirements, women and under-represented groups of
candidates tend to only apply when they meet every requirement. If you think you
have what it takes but don’t necessarily tick every requirement on this job
description, please still get in touch and apply to Halter. We’d love to chat to
see if you’ll be an epic fit!If this opportunity sounds like you, please apply
below by sending through your cover letter explaining why you’re excited about
this role and working at Halter, along with your CV, and we’ll be in
touch!Please also feel free to check out the careers and culture page for more
information on working at Halter and don't forget to follow us on LinkedIn &
Instagram (@lifeathalter).

================================================================================

JOB #21
----------------------------------------
Title: Senior Cloud Engineer - Azure - Multiple roles
Company: Beyond Recruitment - NZ’s best career opportunities – jobs all over Aotearoa
Location: Location not found
Posted: Remote working options within NZ available
Job ID: ********
Link: https://www.seek.co.nz/job/********?ref=recom-homepage&pos=24#sol=a5daa275b7e1bb4ae7bff1485b85686b1e671a10
Quick Apply Available: True
Description:
Senior Azure Cloud Engineers – Multiple Roles Remote based in NZ 6 month
contract Banking Industry Join a well-recognised enterprise organisation as part
of a high-performing cloud engineering team delivering scalable, secure, and
modern cloud solutions. These are hands-on engineering roles suited to
experienced professionals with deep Azure and infrastructure-as-code expertise.
You’ll work on mission-critical platforms, applying your skills with Terraform,
AKS, and/or Kafka in an environment that values innovation, reliability, and
collaboration. We’re hiring for two types of roles: Senior Azure Cloud Engineer
– AKS Focus Advanced experience with Azure services: networking, compute,
storage, AAD, monitoring Expert-level Terraform skills: module development,
state management Strong hands-on experience with AKS: deployments, scaling,
ingress controllers, Helm Solid understanding of Docker and containerization
Familiarity with CI/CD tools: GitHub Actions, Azure DevOps, Jenkins Senior Azure
Cloud Engineer – Kafka Focus: Proven experience with Azure infrastructure: VM,
networking, Event Hubs, monitoring Strong Terraform capabilities: managing
modules, Azure provider features Solid experience with Apache Kafka in cloud
environments Deep understanding of Kafka architecture: topics, partitions,
brokers, replication Experience integrating Kafka with databases, microservices,
analytics platforms Ref: #130339 Please click APPLY now with your cover letter
and CV to be considered. Please note that candidates need to be currently in NZ
and have the legal right to work in NZ. Beyond Recruitment Technology,
Transformation & Digital, connecting IT talent with IT opportunities. Visit
www.beyond.co.nz for more roles and information.

================================================================================

JOB #22
----------------------------------------
Title: Senior Network Systems Engineer
Company: Honda New Zealand Limited
Location: Newmarket, Auckland
Posted: Trusted Global Brand
Job ID: 85300847
Link: https://www.seek.co.nz/job/85300847?ref=recom-homepage&pos=25#sol=e4e3e67953bcd76db83fb3e3703d4314ac58c5db
Quick Apply Available: True
Description:
About usAt Honda New Zealand, our vision is to be the brand with the most loyal
fans. We’re committed to spreading joy through innovative mobility solutions
that enrich everyday life. By choosing Honda New Zealand, you join a dynamic
team that thrives on success and exceeding expectations. We believe in constant
growth, learning from one another, and emerging stronger with each challenge.
Join us and take pride in being part of something extraordinary.About the roleWe
are looking for a technically capable and proactive Senior Network Systems
Engineer to support the strength, reliability, and security of Honda New
Zealand’s IT infrastructure.This role offers the opportunity to make a real
impact across our enterprise and dealer networks. You will be responsible for
maintaining and improving Honda’s networking, cloud, and server environments.
From daily operations to supporting global projects and cyber security controls,
this is a role that combines technical depth with meaningful business
outcomes.You will work closely with the wider IT team and third-party vendors to
ensure systems remain compliant, current, and performing at their best. This is
an office-based role located in Newmarket.Key responsibilities:Ensure high
levels of service availability, performance, and security across our
infrastructureConfigure and manage Layer 2 and 3 technologies, including VLANs,
OSPF, BGP, and moreMonitor, manage, and maintain firewall rules, VPNs, and
wireless LAN environmentsSupport on-site and cloud infrastructure (AWS, M365, G
Suite)Administer DNS, IP address management, patching, backup (Veeam), and
scripting (PowerShell, Python)Lead troubleshooting and resolution of complex
network issues across multiple sitesTake ownership of network-related support
tickets and manage service desk processesEnsure compliance with IT security
controls, including threat monitoring and firmware updatesParticipate in
business-as-usual changes and contribute to system documentationLiaise with
Honda Global, vendors, and contractors on systems projects and upgradesThe
successful candidate will have the following:Minimum 5 years’ experience in
senior-level network or systems engineeringProven expertise in enterprise
network support, security, and cloud platformsStrong knowledge of Windows
Server, Ubuntu, VMware, Group Policy, and VeeamSkilled in PowerShell and Python
scripting for automation and admin tasksHands-on experience with Extreme
Switches, XIQ, WLC, firewalls, and VPNsFamiliarity with SEP, CrowdStrike, Falcon
Forensics, and McAfeeExperience with AWS, G Suite, and Microsoft 365
environmentsStrong troubleshooting, problem-solving, and documentation
skillsExcellent time management, communication, and stakeholder
engagementWorking knowledge of ITIL frameworks and best practicesA proactive,
team-oriented mindset with a strong customer service focusRelevant tertiary
qualifications or certifications in IT, Networking, or similarAbout the
rewards:Competitive SalaryAnnual Performance Bonus Career growth
opportunitiesDiscounted health insuranceCompetitive superannuation scheme,
including life and disability insurancePaid Parental Leave - Honda Happy
FamiliesPreferential banking package with ANZHonda Happy wellness
programEmployee Assistance Program with EAP Services Annual staff tree planting
dayFlu shots and annual health checksDouble your donation fundraisingLong
service leaveIf you are ready to step into a role that offers impact, challenge,
and the opportunity to work closely with a passionate leadership team in a
globally respected organisation – apply now or start a conversation with Denver
D’<NAME_EMAIL> Please Note: Only people with valid work
permits for New Zealand are requested to apply for this position

================================================================================

JOB #23
----------------------------------------
Title: Level 2.5 Support Engineer
Company: Lucidity Cloud Services
Location: Penrose, Auckland
Posted: 28d ago
Job ID: ********
Link: https://www.seek.co.nz/job/********?ref=recom-homepage&pos=26#sol=e99cb2cbb2915fbfc429d3050123eebd9e1001e3
Quick Apply Available: True
Description:
About LucidityLucidity is a business guided by values. We are large enough to
deliver and small enough to care. We believe in doing the right thing by our
customers and giving them the confidence we are in it for them. Our culture of
transparency, innovation and driving outcomes, is of the utmost importance to
us. Lucidity was established in 2003, and we have been in business of ‘cloud’
for over 20 years. We were born fanatical innovators and still are. We are a
cloud focussed IT provider, primarily leveraging M365 and Azure, and we take our
customers security seriously - we are proud to be ISO27001 certified.We are
focused on delivering affordable solutions that don't skimp on quality and are
backed by amazing customer services. How do we do this?Our Professional Services
team have strong consulting experience in Microsoft 365, Azure infrastructure
and Azure Virtual Desktop projects. We are capable of servicing small businesses
all the way to large corporates with complex requirements. We are listed on the
government marketplace and work with other IT services companies looking to
augment themselves with our specialist skills.Actively collaborating with our
Professional Services team, our Managed IT team provides services to small and
medium sized businesses (typically 30-150 users) looking for a true partnership
that is engaged and delivers evolving IT services. Our core service offerings
are security focused and provide customers the tools and systems found in large
mature businesses. About the RoleWe’re looking for someone sharp, proactive, and
ready to step up, not just technically, but as a future leader.This is a great
opportunity for a Level 2.5 Support Engineer who’s confident in their technical
ability, enjoys solving problems properly (not just applying a quick fix), and
wants to be part of a team that genuinely cares about delivering great
service.Lucidity is a cloud-first business with a strong Microsoft stack,
including Azure, Microsoft 365, Entra ID, and more.Our clients typically range
from 30 to 150 users, and they rely on us as their IT partner, not just a
helpdesk.This role plays a key part in delivering high-quality remote support
and getting hands-on with Lucidity’s cloud platforms, automation tools, and
monitoring systems.But more than that, it suits someone who sees the bigger
picture, someone who’s always asking, “How could this be done better?” Whether
through scripting, automation, or mentoring others, there’s plenty of
opportunity to improve how things are done.There’s also a clear pathway into a
Team Lead position for the right person.We’re looking for someone who is not
only technically strong but also brings a calm, collaborative approach to their
work and decision-making.If you’re looking for a place where your input matters,
your growth is supported, and your work helps Kiwi businesses succeed, keep
reading.This could be the next step you’ve been looking for.Important
ResponsibilitiesAdminister maintain and upgrade core IT infrastructure,
including Microsoft Windows Server, Microsoft Exchange, Microsoft 365, Entra ID,
Azure Azure IaaS / PaasS Services (e.g. Storage Accounts), Azure Virtual
Desktop, Azure Virtual Machines (VMs), Active Directory, VMware, and
Hyper-V.Deliver end-to-end remote and onsite IT support, effectively resolving
hardware, software, server, and network-related LAN/WAN issues to maximize
client satisfaction and system performance.Lead patching, vulnerability
management and proactive threat mitigation.Maintain strong IT administration,
compliance, and audit processes to reduce security and technology risk.Support
and implement the IT development and service management processes, as required,
including Incident, Change, Release, Problem and Capacity and Availability
Management.Owning and managing automation initiatives and addressing technical
inefficiencies across platforms and processes to drive operational efficiency
and scalability.Discussing technical solutions with our Professional Services
team and providing innovative new ideas to solutions.Work with first and second
level support to diagnose issues, implement solutions, and document findings in
the knowledge base, including being an escalation point for our Support
team.Manage relationships with vendors, service providers, and clients,
including proactive and timely escalation to ensure contractual obligations are
met and a high quality of service is consistently delivered.Administer and
maintain backup solutions using Veeam and Azure Backup, ensuring data integrity
and availability. Continuously assess and implement improvements to enhance
backup performance, reliability, and compliance with business continuity
requirements.Contributing to and setting the standard for high quality
documentation.On-call duty as an escalation point, 24/7 critical cover for IT
Infrastructure.Continually keep up to date with advancements in enterprise and
SMB Information technology, particularly products we leverage (such as Microsoft
Azure, Fortinet, Access4, S1, BlackPoint and Nerdio) and their competition.
Required ExperienceMust possess a Bachelor of Science in Computer Information
Systems or equivalent qualification or display equivalent experience.Must have
7-10 years’ experience in a technical customer support environment.1-2 years of
leadership experience or demonstrable aptitude to lead.Must have experience in
on-premises solutions as well as Cloud solutions built in Microsoft 365 and
Azure in particular.Must have a demonstrable curiosity and drive for continuous
improvement and want to champion fanatical innovation. Must have intermediate
experience with PowerShell scripting and automation platforms (such as RMM,
Nerdio and Pia.AI).Azure Certification – Lucidity is seeking candidates who hold
one or more of the following Microsoft Azure certifications - AZ104, AZ140,
AZ305, AZ500, SC100, MD102, MS102 etc.ITIL and ITSM certification,
desirable.Your skills and experience are important, but what matters more is
your alignment to our values, your attitude and willingness to learn, and the
way you treat people.BenefitsMobile phone allowance.Remote work
allowance.Birthday Paid Day Off.MyBoost - retail discounts app
(https://www.myboost.co.nz/offers/).Team building and social events.Hybrid
working environment with 1 office day.Pool car available. Our ValuesValues we
want staff to embody to ensure customers get exceptional results:Proactive
Leadership: Drivers of IT OutcomesWe are in the driver’s seat. Lucidity staff
are not just participants in the IT journey; we actively take control, steering
towards success. Our proactive approach means identifying and solving problems
before they arise, ensuring seamless operations for our clients.Innovative &
Forward-ThinkingAt Lucidity, we challenge the status quo. We embrace innovation
and stay ahead of technological advancements, integrating the best practices and
latest tools into everything we do. We are never stuck in our ways; our
curiosity and drive push us to seek out new solutions.Transparency & Tough
ConversationsWe value clear and open communication with our clients and within
our teams. Honesty is paramount, even when it involves difficult discussions.
Our transparency builds trust and ensures that everyone is aligned and informed
about our processes and goals.Security-First MindsetEvery decision we make,
every action we take, has our customers' security and privacy at its core.
Lucidity staff are guardians of our clients' IT environments, ensuring that we
stay compliant, vigilant, and committed to the highest cybersecurity
standards.Curiosity & Continuous LearningWe never stop learning. Our curiosity
drives us to explore, understand, and improve. By continually seeking knowledge,
we deliver better solutions and elevate our expertise. Curiosity isn't just
about asking questions—it's about uncovering opportunities for growth and
improvement.Collaborative Success: Partners in ProgressWe are an extension of
our clients' teams. We don't work for Lucidity—we work for our clients. Every
decision, recommendation, and solution we provide is with their success in mind.
We consult, collaborate, and ensure that their goals become our goals. We strive
to be as invested as their internal IT department.Our Hiring ProcessApplicants
that are successful at each stage will progress through the following
process:Applicants that match our requirements may be contacted for further
information. Shortlisted candidates will be offered a short remote interview.A
second longer remote or in-person interview with relevant Lucidity staff may
then be offered.We will conduct psychometric testing as part of our assessment
process. Then finally, an informal catch up in a social setting (such as our
work drinks) to help determine cultural fit and how you gel with the team. How
to ApplyClick the APPLY button and include your resume AND cover letter
describing how your skills and experience will meet our requirements
(applications without a cover letter will not be considered).Please note, we
will only consider applicants that are legally entitled to work in New Zealand
at the time of application.

================================================================================

JOB #24
----------------------------------------
Title: Senior Data Engineer
Company: Techspace Consulting Limited
Location: Location not found
Posted: 10d ago
Job ID: 85213407
Link: https://www.seek.co.nz/job/85213407?ref=recom-homepage&pos=27#sol=f774648b97fe3c26145d4ecedaa801d8ab65261f
Quick Apply Available: True
Description:
Senior Data Engineer About the Role We are seeking a Senior Data Engineer for an
exciting opportunity within a dynamic consulting environment. This role is ideal
for someone who thrives on turning data into business value. You'll be
responsible for architecting and building cloud-native data solutions,
collaborating closely with internal teams and stakeholders to deliver high-
impact, scalable outcomes across diverse projects. Key Responsibilities Design,
build, and maintain data pipelines, data models, and advanced analytical
solutions on cloud platforms (particularly Azure and AWS). Work closely with
engagement teams to understand business requirements and deliver tailored,
innovative solutions. Provide technical guidance and share best practices with
broader consulting teams to ensure consistent, high-quality delivery. Support
account growth by identifying new opportunities and building awareness of
clients' evolving needs. Drive continuous improvement of data engineering
processes, tools, and frameworks with a focus on innovation and scalability.
Requirements Strong experience in developing data and digital solutions using
programming languages such as SQL and Python. Familiarity with Java, Scala, or
C# is advantageous. Proficiency in cloud platforms such as Azure, AWS, or Google
Cloud. Solid understanding of version control (Git) and CI/CD workflows. Hands-
on experience with data tools like Snowflake, Google BigQuery, Databricks, Azure
Synapse, and visualization tools such as PowerBI or Tableau. Knowledge of data
modelling, API development, containerization (Docker), and Infrastructure as
Code tools such as Terraform, Pulumi, CloudFormation, or Bicep. Ability to
collaborate across multidisciplinary teams and translate business challenges
into effective data solutions. A proactive, agile mindset with strong
communication skills and a passion for continuous learning and team success.
Benefits Opportunity to work on high-impact, cross-industry projects Career
growth through access to cutting-edge technologies and leadership development
Hybrid work: 2 days remote, 3 days in-office How to Apply: Submit your resume to
Elaine <NAME_EMAIL>

================================================================================

JOB #25
----------------------------------------
Title: Full Stack Developer
Company: Upper Echelon Limited
Location: Auckland CBD, Auckland
Posted: 6d ago
Job ID: 85291546
Link: https://www.seek.co.nz/job/85291546?ref=recom-homepage&pos=28#sol=ab1465033b8f394e6cdc520df1516b21d0701812
Quick Apply Available: True
Description:
We are looking for a Senior Developer for a short term contract with an imediate
start. Working on the following tech stack:DjangoReactDataBricks (Not essential)
Our client is a leader in Data Analytics and Business Intelligence and have
partnered with one of New Zealands biggest companies on their Real Time
Programme of work. About You:experienced with DjangoSolid React experience with
the ability to build clean code and thoughtful user experienceDataBricks
experience beneficial Able to work with Product Managers and engage with
stakeholders as required The ProcessSingle stage interview process to start as
soon as possible. For more information, contact Dikesh on 0275345374

================================================================================

JOB #26
----------------------------------------
Title: IT Support Lead
Company: The Cause Collective
Location: Manukau, Auckland
Posted: 26d ago
Job ID: 84836654
Link: https://www.seek.co.nz/job/84836654?ref=recom-homepage&pos=29#sol=b937eb3155fbd46d00847de95bc1c9f322e1e700
Quick Apply Available: True
Description:
The RoleWe are seeking a dynamic and experienced IT Support Lead to provide
technical leadership, strategic oversight, and hands-on support for our IT
systems and services. This is a key role at TCC, responsible for ensuring our
digital infrastructure is robust, secure, and future-ready to support our people
and purpose.As IT Support Lead, you will oversee day-to-day IT operations,
provide high-quality user support, and lead the development and implementation
of our digital strategy. You will ensure IT assets and projects are effectively
managed and that digital equity is championed across all staff and communities
we serve.About YouProven experience in IT support, systems administration, or
infrastructure management, ideally in a mission-driven or nonprofit
environment.Strong technical skills across systems, networking, cloud platforms
(e.g., Microsoft 365, Google Workspace, Azure), and security best
practices.Demonstrated leadership experience, with a collaborative and values-
driven approach.Able to think strategically while staying hands-on with day-to-
day support and problem-solving.A commitment to equity, inclusion, and
supporting diverse teams through culturally grounded technology
leadership.Strong communication skills and the ability to translate technical
concepts for non-technical audiences.What We Offer:🔹 Flexible working
arrangements🔹 Competitive remuneration🔹 Medical insurance with zero excess🔹 Life
insuranceHow to ApplyPlease submit your CV and a short cover letter explaining
why you're the right fit for this role. Applications will be reviewed on a
rolling basis, so early applications are encouraged.

================================================================================

JOB #29
----------------------------------------
Title: Head of Software Development
Company: Evolve Recruitment Group
Location: Albany, Auckland
Posted: Hardware and Software B2B SaaS company
Job ID: 84915311
Link: https://www.seek.co.nz/job/84915311?ref=recom-homepage&pos=32#sol=b74e399b6ef5837be4528af768cffc49a8a1c1a4
Quick Apply Available: True
Description:
Hardware and Software B2B SaaS companyTechnical leadership opportunity driving
innovation and growthAlbany office location with flexibilityOur client is a
global leader in B2B hardware-enabled SaaS, delivering innovative solutions for
some of the world’s most recognisable brands. Headquartered in Auckland and
operating across multiple continents, they are entering a dynamic growth phase -
expanding into new markets with bold ambition.To accelerate this momentum, they
are seeking a Head of Software to join their senior leadership team. This is a
rare opportunity to make your mark on an established, scaling tech company where
your leadership will have global impact.The Opportunity:As Head of Software,
you’ll be responsible for the full software engineering environment, spanning
software product development, QA, data, AI/ML and cloud infrastructure. You will
shape and execute the technical strategy and lead high-performing teams to
deliver secure, scalable, and reliable cloud-based applications across the
company’s SaaS solution.You’ll bring deep technical expertise and come from an
engineering background yourself. Your leadership style will be collaborative and
bring an authentic passion for building high-impact teams and products. Working
closely with product and hardware teams, you’ll ensure alignment between
business goals and technology delivery, striving for innovation, performance,
and continuous improvement.This is an influential leadership role with plenty of
exposure to executive level decision making and the opportunity to shape the
company’s technology direction at a global scale.Key Responsibilities:Lead and
inspire the end-to-end software engineering function, including strategy,
architecture, development, testing, AI/ML and cloud operationsPromote a strong
engineering culture built on innovation, collaboration, and technical
excellenceEnsure delivery of robust, scalable, and secure solutions in a cloud-
native environmentAdvance the adoption and integration of AI and machine
learning across products and platforms, identifying opportunities to enhance
customer value through intelligent automation and data-driven insights.Champion
and drive adoption of modern software practices including Agile delivery,
DevOps, and CI/CDCollaborate cross-functionally with product, and hardware teams
to deliver integrated market-leading solutionsIdentify and proactively manage
technical risk while promoting resilience and scalabilityBuild, develop, and
mentor a diverse team of engineers and technical leaders fostering growth and
engagementManage budgets, resource planning, and vendor relationships to
optimise delivery and maximise ROIStay ahead of industry trends, emerging
technologies and best practices to maintain the company’s competitive
edge.Incorporate sustainable thinking and practices into every aspect of
software delivery.About You:10+ years of experience in software engineering
within SaaS, IoT, or enterprise software sectors.Proven track record leading
high performing multi-disciplinary teams in a hi-tech environment focused on
product development.Strong knowledge of system architecture, cloud platforms
(preferably AWS), and modern development frameworksStrong understanding of Agile
methodologies, DevOps practices, and CI/CD pipelinesCommercial acumen with
experience aligning technology strategy to business outcomesExceptional
communication and stakeholder engagement skills.Passion for people leadership
and a commitment to inclusive, values-driven cultureCommitted to innovation,
quality and continuous improvementWhy Apply?This is a pivotal leadership role in
a forward-thinking, globally operating company - the perfect opportunity for an
ambitious person who wants to shape technology strategy at scale, lead with
impact, and grow with a company on the rise.Diversity matters. Our client
proudly supports a diverse and inclusive workplace and welcomes applicants from
all backgrounds. Even if you don’t meet 100% of the requirements, we encourage
you to apply. We value potential as much as experience.Ready to shape the future
of SaaS? Apply now to become the Head of Software Engineering and help drive
global innovation from the heart of Auckland.Laura Douglas -
<EMAIL>

================================================================================

JOB #30
----------------------------------------
Title: Principal Architect (Digital Channels)
Company: Collada
Location: Auckland CBD, Auckland
Posted: 26d ago
Job ID: 84814232
Link: https://www.seek.co.nz/job/84814232?ref=recom-homepage&pos=33#sol=90aa23118cf26e662aa51b7a83a4b24d114a6446
Quick Apply Available: True
Description:
Many architects aspire to get into financial services, but it can be incredibly
difficult to do that, if you don't have the financial services experience. If
this sounds like you, then we might be able to help you. Collada is working with
a financial services organisation that is in the process of establishing their
practice. They have a few key roles locked in, but now their focus has turned to
engaging an architect who lives and breathes digital. This role isn't purely a
technology role, this requires someone who understands the broader business
context and can engage with key business leaders. Although you don't necessarily
have to have worked in financial services, they do require someone who has a
great depth in the digital domain.Given the smaller size of the practice, this
role will operate across multiple layers of architecture, from the contextual
down to logical. There will be solution architects who will be engaged to
support this role in the future, but in the interim you'll need to be able to
drop to the detail where required.One of the great things about this role is
there aren't really any preconcieved ideas about architecture and digital. This
organisation really hasn't had much capability in this space before, so it's not
like you are moving to a role where they've been burnt by architects. You can
chart your own courseThis organisation does not have a dedicated enterprise
architecture practice, instead they have principal architects to own different
business domains. These Principal Architects operate across the enterprise and
programme level of architecture, ensuring that what they have envisioned is
delivered accordingly.As the Principal Architect you will own and drive the
implementation and leadership of architectural, strategic, and innovation
capabilities within the consumer domain of their architecture practice. You will
ensure alignment with organisational goals and strategic direction, providing
thought leadership and insight that will enable competitive advantage through
the innovative use of people, processes, and technology. Role: Principal
ArchitectLocation: Auckland CBDSalary: $220,000+ base + bonusSkills: Enterprise
Architecture, Programme Architecture, Customer, CRM, Digital Key
Responsibilities:Lead the collaborative development of architectural, strategic,
and innovation artefacts and governance, providing strategic direction, solution
realisation, and thought leadership.Define architectural principles, guardrails,
and standards for the organisation.Provide leadership to a team of architecture,
strategy, and innovation professionals, inspiring and motivating them to deliver
high-quality, fit-for-purpose deliverables.Formulate, develop, and maintain
architecture standards and their application within the architectural
practice.Create platform, ecosystem, enterprise, experience/episode, and
strategic artefacts and roadmaps to achieve cohesive cross-domain outcomes and
improved strategic planning.Champion and lead the collaboration on and
governance of strategic roadmaps.Consult and liaise with leadership teams on
risks and benefits related to all scopes of architecture.Contribute to the
research and innovation capability across the Architecture community.What We Are
Looking For:Extensive experience in both enterprise architecture and programme
architecture, with a proven track record of delivering successful enterprise
scale initiatives.Strong background in the consumer/customer domain, with
detailed experience in digital transformation, CRM, and customer
engagementAbility to translate industry models and blueprints into technical
impacts.Experience with core banking, CRM transformation, legacy monoliths,
modern integration stacks, and cloud technologiesExcellent leadership,
communication, and collaboration skills, with the ability to inspire and
motivate teams.

================================================================================

JOB #31
----------------------------------------
Title: Game Designer
Company: Grinding Gear Games
Location: Henderson, Auckland
Posted: 6h ago
Job ID: ********
Link: https://www.seek.co.nz/job/********?ref=recom-homepage&pos=34#sol=faddd5a7d952a46b4ee923b57f25e9be6f5f6b30
Quick Apply Available: True
Description:
We seek experienced Game Designers to join the Path of Exile development
team.Location: On-site in Henderson, Auckland, New Zealand*Relocation assistance
(for the right candidate if needed)Full-time position - 40 hours a weekThe
position includes the following duties:Designing gameplay systems, monsters,
bosses and items for Path of ExileEditing metadata and scripting to implement
your designsCommunicating your designs to artists and programmers to allow them
to create what you have designedWe are seeking someone with:3+ years of
experience with production, quality assurance or game design work on a
commercial online game project OR a bachelor's degree in game development or
similarHigh creativity and a keen eye delivering engaging, immersive
experiencesExperience with game editors, scripting and design toolsStrong
critical thinking and analytical skillsAbility to receive and apply feedback in
a highly iterative environmentExcellent communication skills and a proven
ability to work within a teamA passion for narrative driven gameplayKnowledge of
the Path of Exile universeFamiliarity with the fantasy genre in games, movies
and literaturePortfolio, and/or reel demonstrating relevant skills required.Must
be willing to complete a test if requested to do so.Pluses:Familiarity with 3D,
animation and visual effects pipelinesExperience with MMORPGs with a focus on
in-game economiesHow to applyIf you meet the above requirements, please email
your resume, portfolio and cover <NAME_EMAIL> with your name
and role you are applying for in the subject line.No phone calls or agencies,
please.What we offerA huge selection of free drinks for all the staff to enjoy
while working. Think of a convenience store's range of drinks a few metres from
your desk! Our rec rooms are stocked up daily with healthy snacks and a rotating
range of seasonal fresh fruit and yoghurt, and catered meals are offered every
week as part of our Monday mid-morning meetings.When the work is done, we invite
all staff to attend our expansion launch parties and Christmas parties to
celebrate the effort they've put towards Path of Exile.

================================================================================

JOB #32
----------------------------------------
Title: Development Lead (Backend)
Company: PHQ
Location: Parnell, Auckland
Posted: Flexible working opportunities & more benefits
Job ID: 85152644
Link: https://www.seek.co.nz/job/85152644?ref=recom-homepage&pos=35#sol=b55cbf8b7495cc46b4f45c844f900fe49d97f5f3
Quick Apply Available: True
Description:
About usPHQ (https://phq.nz) is an independent, digitally led creative agency
based in Auckland, founded by the Phantom London studio (https://phantom.land/).
Our very essence is to shape shift - to adapt and evolve, collaborating with
brands that challenge and endlessly inspire us. We transform challenges of all
shapes and sizes into inventive, engaging and performance driven solutions. For
us, every project is a unique opportunity to lead the industry and deliver
groundbreaking results.Put simply, we want to do incredible work that we’re
proud of and make our clients love us. Where there’s an innovative solution to
be had or simple amends to complete, we will tackle it even better than anyone
could have imagined. About the roleAs a Development Lead (Backend), you will
support and lead other Backend Development team members. You'll also collaborate
closely with Frontend Developers, Designers and Producers on technical solutions
that align closely with a creative vision (web apps & games, cloud architecture,
devops, AI, creative tools & installations etc). About youWe are looking for an
enthusiastic Development Lead who has strong experience in web development &
related technologies. We are looking for individuals who tick these boxes: Your
credentials: Minimum of 8 years development experience in a commercial or agency
environment. Expert knowledge in backend programming languages and methodologies
(primarily Python, some PHP)Expert knowledge in development workflows (e.g. git)
& confidence with command line toolsA strong understanding of solutions
architecture working with cloud platforms - Google Cloud, AWS, AzureA strong
understanding of Authentication flows and solutionsA strong understanding of API
developmentA strong understanding of secure web development practises (e.g.
OWASP)Strong experience with code review processes (e.g. Git pull requests)A
good understanding of frontend web technologies (HTML5, CSS3,
JavaScript)Excellent written and verbal communication skills demonstrated
through detailed presentation on technical concepts. Excellent documentation
skills including creating meaningful technical specsStrong ability to work on
simultaneous projects and meet deadlinesBonus points:Degree in Computer Science
or related technical disciplineYour own website, showcasing your own work and
interestsOpen source / community contributions (e.g. on GitHub)Experience with
the following:CMS frameworks (e.g. Wagtail/Django, Drupal)SQL databases (e.g.
Postgres, MySQL)Frontend frameworks (e.g. React, Angular)Automated testing (unit
testing etc)Knowledge of networking and/or DevOps experience (e.g. Linux,
Docker, Vercel, CI/CD, Terraform, HTTP, DNS etc) for both development and
production environments

================================================================================

JOB #33
----------------------------------------
Title: Solution Architect
Company: DXC Technology Australia & New Zealand
Location: Auckland CBD, Auckland
Posted: Highly flexible work environment
Job ID: 85157788
Link: https://www.seek.co.nz/job/85157788?ref=recom-homepage&pos=36#sol=50ce18e0148b4cfc4404f0d4c116e9569cb7f980
Quick Apply Available: True
Description:
We are hiring! Join the fantastic DXC team delivering cutting edge Dynamics 365
projects!· Variety and range of tech across platforms· Collaborative &
supportive team that are keen to share IP· Highly flexible work environment We
are hiring! Join the fantastic DXC team delivering cutting edge Dynamics 365
projects! Why join DXC Technology?DXC Technology (NYSE:DXC) - where brilliant
people embrace change and seize opportunities to advance their careers and
amplify customer success. People are the heart of our business. We support each
other and work as a team, globally and locally to deliver excellence for our
customers and colleagues. We live in more than 70 countries, speak multiple
languages and work with over 6,000 customers on almost every continent. We use
the power of technology to deliver mission critical IT services that move the
world. DXC.comDXC Practice for MicrosoftDXC helps enterprise and mid-market
organisations to digitally transform their business and move to the cloud with
cost-effective, rapid projects that offer lower risk and are delivered in a
faster time to market using Microsoft applications.We are dedicated to
delivering business-improving software and solutions built on Microsoft Dynamics
with the highest levels of customer service and support.The largest independent
Microsoft Dynamics partner in the world, the DXC team in Australia, New Zealand
and the Pacific supports over 1,200 customer sites in the region. We specialise
in new implementations and upgrades to cloud-based industry-specific solutions,
powered by Microsoft Dynamics 365.At DXC we pride ourselves on delivering
excellence in everything we do. What this means for you is the opportunity to be
a part of delivering innovative solutions and helping to solve real business
problems for a wide variety of valued clients. Be a part of a strategic
Microsoft partnership that provides you with opportunities to deepen your
expertise and work at the cutting-edge of technology.MS D365 CE Solution
Architect You will work closely with DXC customers, understanding their business
objectives, and architecting end-to-end solutions that leverage the power of
Dynamics 365 CE. Your expertise in enterprise architecture principles, coupled
with your deep understanding of the Dynamics 365 CE platform, will enable you to
design and implement robust and scalable solutions that drive business
outcomes.As Solution Architect, you will not only focus on Dynamics 365 CE, but
also the full suite of Microsoft technologies, including Power Platform, Azure,
and Office 365, to create seamless and integrated solutions that drive digital
transformation.· Deep knowledge of Dynamics 365 CE: You should have an extensive
understanding of the Dynamics 365 CE platform, including its various modules
such as Sales, Marketing, and Customer Service.· Enterprise architecture
expertise: Strong knowledge and experience in enterprise architecture
principles, methodologies, and frameworks.· Technical skills in Microsoft
technologies and platforms is essential, including Azure, Power Platform (Power
Apps, Power Automate, Power BI), and Office 365. You should understand how to
leverage these technologies in conjunction with Dynamics 365 CE to create
comprehensive solutions.· Solution design and implementation: You should have a
track record of designing and implementing end-to-end solutions using Dynamics
365 CE. This includes gathering business requirements, creating solution
designs, configuring the platform, and overseeing the implementation process.·
Business acumen: As a Solution Architect, you need to understand the business
objectives, processes, and challenges of the organizations you work with.·
Communication and stakeholder management: Effective communication skills are
crucial in conveying complex technical concepts to both technical and non-
technical stakeholders.· Leadership and team collaboration: As an architect, you
will often lead project teams, coordinating and collaborating with developers,
consultants, and other stakeholders What DXC has to offer· Extensive resources
to support your onboarding and continual development including DXC University·
DXC Recognition, our global virtual platform that fosters a culture of
appreciation and celebration with real-time recognition· We know that great
people refer great people. We will reward you when you bring your friends and
family to work at DXC· More time to do the things you love with flexible leave
options, including purchased leave· Take time to give back with charitable and
emergency services volunteer days· Well-being matters to us and our Employee
Assistance Program is there to support you and your family· And of course, all
the basics; novated leasing, discounted health insurance, paid parental leave
and many other discountsDXC is committed to building better futures for our
customers, colleagues, environment, and communities. We take care of each other
and foster a culture of inclusion, belonging and corporate citizenship. We put
this to action developing and implementing societal initiatives within our
Social Impact Practice. #WeAreDXCIn alignment with our commitment to fostering a
flexible and dynamic work environment, we have adopted a hybrid working approach
that acknowledges the importance of in-person collaboration while recognising
that the determination of your work location will be based on a range of factors
to ensure both individual preferences and operational requirements are met.If
you would like to be part of a culture that drives innovation, delivers results,
rewards performance and encourages ideas, then please press the "Apply Now"
button to submit your resume.

================================================================================

JOB #34
----------------------------------------
Title: Control Systems Software Engineer / Automation
Company: Information Systems Integration LTD
Location: Auckland Airport, Auckland
Posted: 3d ago
Job ID: 85435303
Link: https://www.seek.co.nz/job/85435303?ref=recom-homepage&pos=37#sol=91df179c811706569456c2ac6751834a123c3614
Quick Apply Available: True
Description:
Take the next step in your career with a company where you can grow, innovate,
and thrive.About ISIInformation Systems Integration (ISI) has been providing
solutions to New Zealand’s leading manufacturing companies for the past 25
years. Deploying complex and leading-edge technology to enable nimble and
efficient manufacturing and processes. Providing management data and KPI’s in
real time, giving a view into plant and manufacturing performance that empowers
management through historical and real-world online data.About the RoleWe have
an opening for a Control System Engineer / Automation Engineer at our Auckland
office. As a Control Systems Engineer, you will be responsible for delivering
control system projects from concept through to completion. This includes
designing, developing, testing, commissioning, and documenting solutions that
meet both engineering and business standards. You’ll work with technologies such
as PLCs, SCADA, SQL Database systems, and various hardware and software
platforms to support our clients' operational needs. Your time will be split
between office-based development work, on-site commissioning and servicing, and
providing ad-hoc on-call support as required. This role offers a dynamic mix of
technical challenges and hands-on fieldwork in a collaborative and innovative
environment.Key ResponsibilitiesDeliver automation services including design
documentation, programming, and implementation aligned with project scope and
standardsEnsure timely and budget-conscious project delivery with high-quality
outcomes ready for client handoverPlan and execute installation, testing, and
commissioning of control systems including SCADA and Database DevelopmentProvide
ongoing service and support for key customer sites, including participation in
on-call rostersMaintain effective communication with clients regarding project
scope, timelines, and budgets, while identifying future opportunitiesAdhere to
safety, quality, and environmental procedures, ensuring compliance with company
and client standardsEvaluate completed work for continuous improvement in
safety, quality, and environmental practicesMaintain internal systems including
task tracking, timesheets, and adherence to company policiesAbout YouYou will be
degree qualified in Engineering having experience in control systems design,
programming and commissioningWe will highly regard candidates with strong skills
and experience in Industrial Control and Automation (SCADA and Database Design)
including design, software development, testing and commissioningCandidates with
experience in Database development and administration will be highly valuedYou
will have a keen interest in Software development and be able to code in java
and visual basicQualifications and ExperienceClient-focused with strong
communication skills.SCADA system design and development.Industrial
communications, network design and development.Experience in the manufacturing
sector such as Food & Beverage and Heavy IndustriesExperience with Rockwell
software and Ignition SCADA

================================================================================

JOB #35
----------------------------------------
Title: AI Automotion Developer
Company: Stonewood Group Ltd
Location: Auckland CBD, Auckland
Posted: Backed by Sir John Key – Join an AI team supported by NZ’s former PM
Job ID: 84785297
Link: https://www.seek.co.nz/job/84785297?ref=recom-homepage&pos=38#sol=3733ef81e65f271736696673164c9d59dca20aa4
Quick Apply Available: True
Description:
Job TitleAI Automation DeveloperReporting ToHead of AI &
AutomationLocationAuckland CBDRole PurposeTo design and deploy intelligent
workflow automation solutions that eliminate manual tasks, integrate internal
systems, and improve operational efficiency across the Stonewood Group.Stonewood
Group is a diversified group of companies operating across real estate,
infrastructure, renewable energy, co-working, private equity, and hospitality.
We employ over 600 staff and contractors across New Zealand, Australia, and
China. Stonewood Homes, a national housing company operating across New Zealand
and Australia with 22 franchisees, is a core part of our group portfolio. Our
associated businesses include:- RCR Green – focused on renewable energy
infrastructure, particularly large-scale solar farms and EV charging station
deployment.- RCR Infrastructure – a leading electrical and HVAC services
provider with clients such as Woolworths Supermarkets, Chorus, ANZ, Fonterra,
and KiwiRail.- DataGuard – specialists in UPS power solutions, serving Datacom,
hospitals, and universities.- Urban Hub – shared office spaces.- Key Capital –
private equity, chaired by former Prime Minister Sir John Key.We also manage a
property portfolio with tenants including TAB, NZTA, Wilson Parking, Radisson
Hotel, Pullman Hotel, Mitre10, and Noel Leeming.This role will be central to
building scalable automation solutions that support core business functions and
eliminate inefficiencies through integrated and intelligent workflows.
Application link:
https://stonewoodgroup.bamboohr.com/careers/663?source=aWQ9Ng%3D%3D.Key
ResponsibilitiesWorkflow Automation- Design and deploy n8n-powered workflows
across departments.- Replace repetitive, manual tasks with efficient
automation.System Integration- Integrate Xero, Teams, PipeDrive, ReLeased, and
phone systems.- Ensure reliable real-time system-to-system data sync.Custom
Logic & AI Embedding- Extend workflows with custom JavaScript or AI-based
logic.- Apply LangChain where advanced logic or LLMs are required.Process
Discovery & ROI Mapping- Collaborate with teams to map processes and define
ROI.- Document improvements and track time savings and efficiency gains.Tools &
Technologiesn8n, JavaScript, APIs/webhooks, LangChain, CrewAI, Xero, ReLeased,
PipeDriveKey Performance Indicators (KPIs)- 8–12 production-grade workflows in
Year 1- 10–15% reduction in manual time for 2+ units- Integration with 5+
systems- 99%+ uptime across automation stackSkills & Experience- Proven
experience building workflows using n8n- Strong in systems integration, API
design, and JS scripting- Familiar with finance, CRM, and admin automation- Able
to extend and troubleshoot automation pipelinesPersonal Attributes- Hands-on and
proactive- Outcome-focused with strong problem-solving drive- Collaborative in a
fast-moving environmentPlease apply via this application link:
https://stonewoodgroup.bamboohr.com/careers/663?source=aWQ9Ng%3D%3D.

================================================================================

JOB #37
----------------------------------------
Title: Senior Software Engineer
Company: JOYN
Location: Auckland CBD, Auckland
Posted: Senior Software Engineer - Agtech
Job ID: 85386912
Link: https://www.seek.co.nz/job/85386912?ref=recom-homepage&pos=40#sol=03f539073b7ed7a221fe343af7808a0515947f26
Quick Apply Available: True
Description:
Senior Software Engineer – Remote Auckland-based Build Tech That Grows ThingsAt
WayBeyond, we’re fusing 12,000 years of crop-growing tradition with today’s
smartest tech - think cloud computing, IoT, AI, machine learning and data
science - to revolutionise farming.We’ve got the proven foundations, global
traction, and big plans. Now we’re looking for a curious, driven Software
Engineer to help us level up.What you'll be doingDesigning and building
scalable, event-driven systemsWorking with AWS tools like Lambda, Kinesis,
EventBridgeCollaborating with data scientists and researchers to solve real-
world AgTech problemsExploring the use of AI tools in developmentContributing
across our product and tech stack—front and back endHelping us continuously
improve how we build, test and shipTech we loveJava, Python back endAWS API
Gateway, Lambda, EventBridge, etc.Postgres, Terraform, CI/CD pipelinesReactJS,
React Native bonusGo, AWS Timestream nice to haveWhat you'll bring5+ years of
experience as a software engineerStrength in back-end development and cloud-
native architectureComfort in solving messy, complex problems in a fast-moving
environmentA collaborative mindset and desire to contribute to a mission-driven
teamRemote-first, but real humansWe work mostly from home, but get together
twice a month in Auckland to connect, collaborate and enjoy good kai. You’ll
need to be Auckland-based or willing to travel here for those sessions.Bonus
points for curiosityWe love people who are constantly learning, question the
status quo, and enjoy tackling the impossible. Extra points if you’re good at
online quizzes.A bit about usWayBeyond exists to empower the next generation of
growers with tech that’s ethical, scalable, and impactful. Our values? Turn up,
back each other, and always keep results front and centre.Ready to grow
something meaningful?Whether you’re after a contract or permanent role, apply
now with a brief cover note explaining why you could be our next team member.
This process is being managed by our recruitment partner Bob Walker from JOYN.

================================================================================

JOB #38
----------------------------------------
Title: Senior Web Application Developer (C#/.NET) - Remote/Flexible
Company: Private Advertiser
Location: Rosedale, Auckland
Posted: Work from home
Job ID: 85288929
Link: https://www.seek.co.nz/job/85288929?ref=recom-homepage&pos=42#sol=2886589f87561043ce548e7795b9b33f959403c7
Quick Apply Available: True
Description:
Join our innovative medical software company, based on Auckland's North Shore,
and help shape the future of healthcare technology! We specialize in cutting-
edge electronic anaesthesia data recording and deliver essential software
solutions to hospitals across New Zealand and the UK.We champion a work-from-
home model with highly flexible hours, ensuring a great work-life balance.
You'll receive comprehensive one-on-one training in our office to ensure a
smooth onboarding. As such preference will be to candidates in the Auckland
region.We're searching for a seasoned Web Application Developer who is a self-
starter and deeply committed to building creative, robust, and pristine code. If
you have substantial experience in C# for developing high-performance web-based
applications, you'll be a perfect fit for our small, dedicated team.We're
looking for expertise in:C#/.NET 4.5+ development for web platformsProficiency
in SQL Server, SQL queries, and stored proceduresStrong command of modern front-
end technologies: HTML5, CSS3, JavaScript (framework experience a
bonus)Experience with WCF and Web Services (RESTful APIs are a plus)Knowledge of
MVVM and WPF (XAML) is beneficialOutstanding verbal and written English
communication skills are paramount. You're a rapid learner, adept at diagnosing
complex issues, and implementing stable, efficient solutions. As a valued member
of our close-knit team, you'll be a proactive collaborator and an exemplary
contributor.A tertiary qualification in Computer Science (or equivalent) is
essential. We expect a strong, successful background in commercial software
development with verifiable references. Experience consulting directly with
customers will be highly advantageous.

================================================================================

JOB #40
----------------------------------------
Title: Intermediate / Senior Software Developer
Company: Liverton Limited
Location: Auckland CBD, Auckland
Posted: 5d ago
Job ID: 85337415
Link: https://www.seek.co.nz/job/85337415?ref=recom-homepage&pos=45#sol=8b39946e7aab12365a550840cbcc696f7ec281a5
Quick Apply Available: True
Description:
Job Description – Intermediate / Senior Software Developer 1.0 OverviewLiverton
build and provide a range of products primarily in the hospitality space to
customers in New Zealand, Australia, USA and Europe. All Liverton products are
built in house using a range of modern technologies and popular cloud stacks.
These products range from self-service digital check-in, self-service kiosks,
cashless payments, mobile app's, and internet gateway technologies. We have
these installed and deployed in some of the largest companies in the world,
clients include the likes of Assa Abloy, Accor, EVT, BHP among many others.To be
a successful candidate you must be an experienced software developer comfortable
with all aspects of the software development process. You will be a confident
with application design and have a willingness to mentor and contribute to a
small team. You will have: At least 5+ years commercial experience using modern
Java (Spring Boot) or an equivalent popular backend language like Node JS At
least 3+ years’ commercial experience using React, Angular or Vue JS. Experience
with a popular cloud services provider like AWS, AZURE, GCP2.0 Technical
SkillsExpert using modern Java or a similar backend language (Node, C#
etc)Expert using React JS, Angular or VueProficient with NPM and the Javascript
ecosystem Expert understanding of HTML, CSS and the web technologiesExposure to
mobile development using React Native/Flutter or bare metal ‘native’
development. Expert consuming API services i.e. REST/SOAP (Exposure to GraphQL
is a big plus)Solid software engineering practices and good knowledge of design
patterns and object-oriented programming.At home with Jira and Git. Comfortable
with code review and mentoring other developersException English written and
oral skills is essentialAnalytical problem-solving skillsComfortable within
Chrome and other browser environments.Ability to work autonomously or as part of
a small professional team3.0 Personal AttributesAbility to work within a team
environment. Evidence of this would be prior experience of having been part of a
successful team.Leadership of junior staff, and ability to mentor, train and
help them learn and manage;Ability to communicate, demo, manage clients and
interact and work with clients via virtual meetings, requirement taking and
gathering and work with the sales team to help provide technical input into
closing deals.Good communication skills. Evidence of this would be successful
publications, production of well written proposals/reports etc.A commitment to
high quality products and services, and to high ethical standards. Evidence of
this commitment would be the standards achieved in previous positions.Technical
skills. Evidence of this would be the type of work performed in previous
positions.Good time management skills. Evidence of this would be the ability to
complete assigned tasks on time.4.0 QualificationsA formal education in software
development at a Diploma/Degree level or equivalent work experience or life
experience. Any other qualifications that demonstrate an interest in self-
improvement.

================================================================================

JOB #41
----------------------------------------
Title: Automation Consultant
Company: Flex Consulting
Location: Auckland CBD, Auckland
Posted: Career Progression and Growth
Job ID: 84961721
Link: https://www.seek.co.nz/job/84961721?ref=recom-homepage&pos=46#sol=956b737e145228c1fa15758e4e8c64a57a4eab33
Quick Apply Available: True
Description:
About Flex Consulting:Flex Consulting provide automation and test automation
services to many of NZ's largest enterprises. As a boutique specialist
consultancy we are trusted by our customers to deliver mission critical
automations to operate and run their core business. We embrace innovation at
speed, and use the latest modern tools and technologies to assist our customers
deliver their strategic goals. You'll be part of a team that are on the
forefront of working to deliver solutions to our customers diverse and
challenging business requirements. We encourage people to bring new ideas,
toolsets and innovation to work every day to drive better outcomes for our
customers. About the role:There are three roles open. Automation Consultant,
Automation Support Consultant and Senior Automation Consultant. These roles will
work across a number of different customers and projects to automate existing
manual processes for our customers.As a Consultant with Flex, you'll have the
opportunity to get hands-on with some interesting and complex pieces of work,
all the while being part of a boutique agency who provide innovation to our
customers, and enable you to explore new agentic and AI technology.You'll
provide specialised automation and AI expertise assistance to our consulting
leads, through to leading delivery on customer projects.You'll be an independent
thinker and doer that enjoys new, different and interesting challenge every day.
General IT expertise to solve problems is valued as well as knowledge across
automation platforms including UiPath, Blue Prism, Power Automate, Automation
Anywhere, Selenium and Playwright.About you: Flex have multiple roles and types
of jobs currently available across our automation team. We understand everyone
is unique and look to build on your strengths to deliver great projects for our
customers. You could be a recent software engineering graduate through to an
experienced consultant- we are looking for the right people with growth
capability and a willingness to quickly learn. Pay reflects your value and
experience.Preferred Skills• Consulting mindset• General IT knowledge•
Programming Skills, broad range preferred• Basic understanding of AI concepts
and protocols.• Knowledge across one or more of the following automation
platforms UiPath, Automation Anywhere, Blue Prism, Power Automate - not
necessary if you are more junior we will train you.• Strong problem-solving
skills and attention to detail.• Excellent communication and collaboration
skills.• Proven ability to work in a fast-paced, dynamic environment.

================================================================================

JOB #42
----------------------------------------
Title: Senior Test Analyst - BI & Data Migration
Company: Younity
Location: Auckland CBD, Auckland
Posted: Auckland CBD
Job ID: 85512733
Link: https://www.seek.co.nz/job/85512733?ref=recom-homepage&pos=47#sol=de42846f4d97071653fc1e63d316bbba734e59bc
Quick Apply Available: True
Description:
What it's about:We’re partnering with a well-established enterprise undergoing a
major transformation of its BI environment. As part of this change, a legacy SAP
Business Objects platform is being decommissioned, with reporting migrating to
Power BI. We're looking for a Senior Test Analyst who thrives in data-driven
environments and can take ownership of testing across backend systems, EDW, and
reporting layers.What you'll be doing: Validating data completeness and accuracy
between SAP BO and Power BI reports Writing intermediate-to-advanced SQL queries
(joins, CTEs, subqueries) to validate data transformations and aggregations
Navigating EDW structures (star/snowflake schemas) to trace data lineage and
verify report integrity Supporting test planning, case design, execution, and
regression testing activities Performing backend testing using SQL, validating
ETL/ELT processes and data movement Engaging across squads including BI
developers, data engineers, product owners, and business stakeholders Logging,
tracking, and retesting defects using Jira, Azure DevOps, or similar tools What
you’ll bring: Proven experience as a Senior Test Analyst or Data QA Engineer in
BI/reporting environments Strong SQL skills and the ability to interrogate large
datasets for validation Experience with Power BI, SAP Business Objects (WebI,
Universes), or similar tools Solid understanding of data models, data
warehousing, and ETL/ELT pipelines Familiarity with Agile methodologies (Scrum,
Kanban) and modern test management tools Experience with Excel and Power Query
for offline data validation is a bonus Why join: Work on a business-critical
programme with high visibility and purpose Be part of a collaborative, cross-
functional team focused on delivering high-quality, trusted data Enjoy a
flexible hybrid setup (3 days in office), with a contract expected to run for 8
months or more Apply now or get in touch with us with any questions. Please
note, a valid NZ work visa is required to be considered for this role.

================================================================================

JOB #43
----------------------------------------
Title: Integration Development Engineers
Company: Lexel – Resourcing - Connecting IT Talent with Opportunity
Location: Auckland CBD, Auckland
Posted: Auckland CBD based, Hybrid options, 6 months initial contact
Job ID: 85360221
Link: https://www.seek.co.nz/job/85360221?ref=recom-homepage&pos=48#sol=c92397d4cd9d9849cd2717a6d1cf74edd78c4618
Quick Apply Available: True
Description:
We’re looking for multiple Integration Development Engineers to join a high-
performing team delivering a large-scale digital transformation. You’ll play a
key role in shaping and building out a modern, AWS-native integration platform
that underpins critical business operations and future innovation. These are
hands-on roles for those who love writing clean, scalable code in TypeScript,
working with modern serverless services and embedding best practices across
engineering teams. What You’ll Be Doing: Build cloud-native integrations using
AWS services (Lambda, API Gateway, EventBridge, SQS/SNS, Step Functions); and
TypeScript for backend integration development Participate in the migration of
legacy/iPaaS integrations to a reusable, modular platform Build RESTful APIs,
event-driven services and microservices Support CI/CD pipelines (GitHub Actions,
CodePipeline) and Infrastructure as Code (CDK, Terraform) Contribute to API
governance and integration architecture direction What We’re Looking For: Strong
backend development experience with TypeScript (essential) Deep hands-on
experience across core AWS services for integration Sound knowledge of OOP,
SOLID principles and modular architecture Automation testing (Jest, TDD/BDD),
monitoring and secure coding practices Experience in event-driven systems and
microservices Great collaboration skills – working across DevOps, QA, data and
product Bonus Points For: Experience with Kafka or similar streaming tools
Familiarity with Docker, ECS, or EKS Background in media or content-rich
environments If this sounds like you 'Apply Now' or call Amelia on 09- 926 7056
NZ-based candidates only – we are unable to consider offshore applicants.

================================================================================

JOB #44
----------------------------------------
Title: Senior Site Reliability Engineer
Company: Plexure Limited
Location: Auckland CBD, Auckland
Posted: Join a global, growing SaaS company with over 320M users
Job ID: 85081076
Link: https://www.seek.co.nz/job/85081076?ref=recom-homepage&pos=49#sol=93d17a70e6785d6ca651832f9a597770a3e81bee
Quick Apply Available: True
Description:
We're looking for a Senior Site Reliability Engineer to join the Plexure team in
this newly created role! Become part of a global, growing SaaS company at an
exciting point in our growth journey.You'll be based in our Auckland CBD office
& enjoy 3 days WFH each week + lots of lovely benefits (more on that below). Who
are we? Plexure redefines the customer experience and drives results through
personalized mobile marketing and next-generation loyalty programs for household
names such as McDonald's.Our industry-leading customer engagement data platform
has over 300 million users in 60+ countries around the globe. What will you do?
Join our SRE squad, where you’ll help automate, operate, and scale our systems
while enabling high change velocity and system reliability. Your team is
responsible for:Provisioning and configuring shared infrastructure. Capacity
planning, monitoring, and support. Building and enhancing infrastructure tooling
and templates. Supporting domain teams in adopting and using infrastructure
tooling effectively.Reporting to the SRE Team Lead, you'll work collaboratively
alongside your squad to achieve a shared mission: creating a platform and
culture that empowers domain teams to deliver products quickly, safely and at
scale.Your day-to-day work will involve: Developing, maintaining, and deploying
Infrastructure as Code (IaC).Managing CI/CD pipelines.Platform monitoring,
alerting, provisioning, and configuration.Enabling teams to interface seamlessly
with the platform.Identifying and driving improvements. Mentoring and supporting
less experienced engineers.Some project work we have on the horizon includes a
potential pipeline migration, optimizing observability, uplifting our monitoring
solution and enhanced automation for our IaC tools.Our tech stack: Azure, Bicep,
Terraform, Azure DevOps, Docker, PowerShell, New Relic. What are we looking for?
Strong experience working in Azure environments, including Azure DevOps,
pipelines, and APIs. Proven experience with Infrastructure as Code, ideally
using Bicep (but experience with similar tools like Terraform is great too).The
ability to write shell scripts in any language.A passion for mentoring and
coaching other engineers.A collaborative mindset and desire to work with good
humans in an environment focused on innovation. What's in it for you? Joining a
cloud-first organization where you get the opportunity to work with cutting edge
technologies.Paid wellness days! You'll get 4 each year + a paid day off in the
month of your birthday.Ongoing career development and support- Receive ongoing
coaching from your SRE Team Lead and a $2,000 yearly training budget to use
towards upskilling yourself!Work from home allowance - we'll give you a one off
payment of $ 1,000 to help you get set up at home. Plus, you'll have access to
staff discounts at PB Tech to help your dollars go further.Fully paid health
insurance for yourself and discounts for family members (Southern Cross -
Wellbeing 2).We've got you covered with life insurance and illness or injury
income protection too.New hospitality benefits - grab a free lunch or dinner in
one of Sky City's restaurants (twice a year!) + you'll have a monthly credit at
TANK.Summer hours! If there's nothing pressing left to do on a Friday arvo,
finish up at 1:00pm (December - February).Free Udemy subscription– choose from
more than 200,000 available courses online!Mental wellbeing support such as EAP
services through Workplace Options.We arrange a company-wide volunteering day
once a year (paid of course). Refer a Friend program - receive a $2,500 bonus
once your referral has been with us 3 months.A modern office and an active
social club that organizes events each month. Why do our employees love working
for Plexure: "I collaborate with a lot of teams across the business and have
gained a lot of exposure to different areas.""We use state of the art cutting
edge technology. It is great to work with different technologies.""You get
appreciation for the work you do. I receive recognition from my leadership,
peers and clients. I feel valued.""We are a very diverse team - we have people
from all over the world! We work hard but also like to have fun."

================================================================================

JOB #45
----------------------------------------
Title: Data Analyst
Company: RWA People
Location: Auckland CBD, Auckland
Posted: Hybrid - Auckland
Job ID: 85147207
Link: https://www.seek.co.nz/job/85147207?ref=recom-homepage&pos=50#sol=b695211186305aa0639a7d470ef1b06d76488742
Quick Apply Available: True
Description:
Join a company with global roots – utilise Power BI to provide meaningful
insights to key stakeholders and grow your career with them! They’re looking for
a Data Analyst to join their innovative and high performing team.What you’ll be
doing:Essentially, you’ll be fine tuning the way the data is being used to
deliver the insights that are used for key decision making. The better we
understand our customers, the better we can meet their needs is the ethos.
You’ll get involved in the strategic piece of work improving the quality of
data, working with stakeholders across the business to ensure you’re capturing
exactly what they need and turning it into meaningful, useful insights. There’s
huge scope for you to grow as the company does, which has been at an
unprecedented pace so far, and with some of the industry’s tech leaders you
won’t be short of mentors. What you’ll need to succeed:With customers at the
heart of what you do, you’re a data expert who knows how to pull raw data and
turn it into something that is meaningful, insightful and of course useful to
the business to help them better meet the needs of their customers. Technically,
you’re Power BI and SQL savvy, able to bring data insights to life in a visually
compelling way for your stakeholders. You’re also up with the play with the
latest and greatest tools that enable you to garner the most valuable insights
possible. This is a very client-involved role, so if you enjoy working with
people, this could be for you. If you have Power BI and SQL experience – reach
out to <NAME_EMAIL> to discuss. (We are only considering
candidates who have visas with the right to work in NZ. Experience within New
Zealand preferred.)

================================================================================

JOB #46
----------------------------------------
Title: Front End Developer
Company: BrightSpark Recruitment
Location: Location not found
Posted: Located in Auckland Central - modern offices
Job ID: 85534777
Link: https://www.seek.co.nz/job/85534777?ref=recom-homepage&pos=51#sol=96456a38441a38f66d4cdf2dc57ba1188925a07e
Quick Apply Available: True
Description:
About the Company: This is an awesome opportunity to join a start up which
offers a solution to make peoples lives easier. They have won awards and have
some big and exciting things in the pipeline for 2025 and beyond. With a strong
leadership team and other Developers to work alongside, this is a great
opportunity for a Front End Developer to learn, and to also contribute to the
product. With offices based in Auckland City Fringe - the team typically works
the full week in office with some flexibility. About the Role:This is an
exciting opportunity for an experienced Front End Engineer to get involved in
innovative products and become part of a highly talented software team. Working
on software that is meaningful and improves the lives of their end users - you
will be the FE expert in the team. Key Requirements: 5 + years experience with
React and Typescript Able to understand what make great UI Experience with Node
or other Back End Technologies Understanding of quality and security issues Good
written and oral technical communication skills Project delivery Able to work in
a fast paced and agile environment If you're interested in finding out more,
please apply online and we'll be in touch or if you need some more information,
you can reach out to Sarah at 0272150562

================================================================================

JOB #47
----------------------------------------
Title: Product Owner/ServiceNow
Company: Younity
Location: Auckland CBD, Auckland
Posted: Excellent Scope Across EUC and Cloud
Job ID: 84892953
Link: https://www.seek.co.nz/job/84892953?ref=recom-homepage&pos=52#sol=08cc927f0cace55eb21210c1892fce8810cc2dfd
Quick Apply Available: True
Description:
Seeking a Technical Product Owner – End User Computing & Cloud Infrastructure
We’re looking for a Technical Product Owner with deep expertise in end user
platforms, cloud infrastructure, and workplace technologies. This is an
opportunity to shape the digital employee experience and lead delivery across
device management, collaboration, and security tools in a large-scale enterprise
environment. What You’ll Bring Experience • 5+ years in a Product Owner or
Product Management role with technical infrastructure exposure • Proven ability
to translate user needs into technical backlogs and prioritise delivery
effectively Technical Knowledge • Strong understanding of Windows/macOS, mobile
platforms (iOS/Android), and VDI (VMware Horizon, AVD) • Deep experience with
device management: Microsoft Intune, Jamf, Workspace ONE, GPOs • Familiarity
with Microsoft 365 suite (Teams, Outlook, SharePoint), Google Workspace • Cloud
platform exposure (Azure, AWS, or GCP); understanding of DevOps, virtualisation,
containerisation • Solid knowledge of networking, endpoint security (Defender,
encryption, AV), and compliance frameworks (ISO 27001, PCI-DSS, GDPR) •
Experience with ITSM platforms like ServiceNow or Jira; workflow and automation
design • Monitoring tools such as Splunk, KQL, SCCM, and scripting knowledge
(PowerShell/Python) is a bonus Soft Skills • Ability to collaborate across
infrastructure, security, and end-user teams • Skilled at balancing technical
debt and product value • Confident in stakeholder communication and user journey
mapping Why Join Our Client? You’ll join a forward-thinking team committed to
modern workplace transformation. If you’re passionate about delivering secure,
user-friendly digital environments and enjoy working at the crossroads of tech
and business—this role is for you. Apply now or contact Cornelia at
<EMAIL> to learn more.

================================================================================

JOB #48
----------------------------------------
Title: Data & BI Analyst
Company: Ando
Location: Auckland CBD, Auckland
Posted: Auckland / Tauranga / Hamilton Offices
Job ID: 85285791
Link: https://www.seek.co.nz/job/85285791?ref=recom-homepage&pos=53#sol=de04ed5c7bb403d5637bd3571c2cd1300b240368
Quick Apply Available: True
Description:
Data & BI Analyst Auckland / Tauranga / Hamilton Location Graduate/Intermediate
Position Great culture and fun team + Flexible working We do things a bit
differently hereAndo is a multi-lines insurance business and sell our policies
through New Zealand’s leading insurance brokers.At Ando we believe that the
fundamental purpose and function of insurance is to help people reduce their
losses when bad things happen. That’s why at Ando we’ve always believed in the
importance of treating your insurance as a relationship, not a transaction. And
why our purpose is to positively impact the wellbeing of New Zealanders.The
roleWe have an exciting opportunity for a graduate/intermediate professional
with excellent relationship and people skills to join our high performing Data &
Business Intelligence team as Data and BI Analyst based in Auckland, Tauranga or
Hamilton. Responsibilities Technical delivery and support of our data platforms.
Helping to develop and maintain an agreed window into business operations while
allowing deep dive by our data users across the business. Strong analytical, and
problem-solving skills to help identify, communicate, and resolve issues in
order to provide data solutions and information. As part of an agile team,
you'll collaborate with the business to gather requirements, design, and build
data solutions for end users. Qualifications and experience A degree in
Statistics, Mathematics, Engineering, Data Analysis, BI, AI/ML, or anything
similar Solid skills in understanding business logic, and translating that logic
into SQL or other query formats and implementing complex business rules
Beneficial but not essential if you have experience with business intelligence
tools like Qlik, Tableau, or Power BI, as well as geospatial analysis. Strong
communication skills to clearly understand and capture user requirements
Innovative and creative approach with a strong desire to develop novel solutions
Data integration experience, with cloud or traditional tools Skills in data
systems and tools including Data Warehousing, ETL/ELT Experience with Snowflake
and DBT. Want to surround yourself with great people?We’re proud that we’re a
bit different from regular insurance companies – knowing it’s our people and
their relationships who make that difference. We know when to work hard, be
focused and smart, but we also never underestimate the importance of the human
connections that make us tick and get along together. Jump in!So, when you put a
group of people together who all share that thought, you get a truly inclusive,
supportive culture and a special group of people.

================================================================================

JOB #49
----------------------------------------
Title: IBMS Technical Specialist/Software Engineer
Company: SKY TV
Location: Auckland CBD, Auckland
Posted: Hybrid role at the intersection of tech and media operations
Job ID: 85450208
Link: https://www.seek.co.nz/job/85450208?ref=recom-homepage&pos=54#sol=f13d94224ae3b42c31f9801fe74ab47d51d93f92
Quick Apply Available: True
Description:
KO WAI MĀTOU I ABOUT SKY Welcome to Rangiata | Sky, the ultimate entertainment
destination that connects Kiwis to the stories and sport they love, in ways
tailored just for them. Our Sky crew work tirelessly to ensure we are Aotearoa's
most engaging and essential media company. We're incredibly proud of our
achievements, but that doesn't mean we're satisfied. As innovators, we
constantly challenge ourselves to redefine boundaries and adapt to the ever-
changing marketplace. Come be your authentic self at Sky, where we actively
listen and adapt to create shared experiences that excite both our customers and
our crew. As an Equal Employment Opportunity (EEO) employer, we proudly champion
inclusivity and diversity as our core values, and we are open to applicants of
all gender identities to feel welcomed and supported during the application
process. We have included a "Diverse" category for those who identify as non-
binary, genderqueer, genderfluid, or any other gender identity outside the
binary system of male/female. TE TŪRANGA I THE ROLE: We’re looking for an IBMS
Technical Specialist / Software Engineer to join our team and play a key role in
supporting, optimising and evolving our content systems. This hybrid role sits
at the intersection of software engineering and operational excellence,
supporting the end-to-end functionality of our IBMS (Integrated Broadcast
Management System) platform – a critical system used to manage content rights,
financials, workflows, and distribution. This is an exciting opportunity for a
technically skilled and solutions-oriented professional who thrives in both a
development environment and in supporting business users to get the most out of
complex systems. You'll help enhance our project delivery capacity, reduce
operational risk, and provide exceptional support to the teams who rely on IBMS
every day. Key responsibilities will include: Providing application and
operational support for IBMS, including workflow optimisation and user training.
Investigating and resolving incidents and issues, ensuring timely recovery and
continuous service improvement. Collaborating with stakeholders across the
business to support their use of IBMS and connected systems. Designing and
delivering enhancements that improve business performance, reduce complexity,
and increase automation. Contributing to an integrated training strategy and
maintaining up-to-date support documentation. NGĀ PŪKENGA ME NGĀ WHEAKO I WHAT
YOU WILL BRING: You’ll bring a unique combination of software engineering skills
and hands-on knowledge of broadcast/content management systems, ideally IBMS.
You enjoy working in a collaborative team, thrive in a complex, fast-paced
environment, and are committed to delivering tools and support that help the
wider organisation succeed. Essential: Proven experience in supporting and
maintaining enterprise applications, ideally in a media or broadcast
environment. Hands-on experience with IBMS (or similar broadcast/content
management systems). Strong problem-solving and incident management skills, with
the ability to troubleshoot complex issues. Experience in software development
and scripting – able to design and implement enhancements and integrations.
Excellent stakeholder engagement skills – able to translate technical
information into clear, actionable support for end users. Comfortable developing
operational training material and leading user training sessions. Preferred:
Previous experience in the media, content, or broadcasting industries. Knowledge
of system architecture and integration patterns relevant to content workflows.
Familiarity with ITIL-based service management practices. Exposure to licensing,
metadata, scheduling, or content distribution systems. Understanding of
application security, backup, and disaster recovery best practices. KO NGĀ
HUANGA I WHAT’S IN IT FOR YOU? As part of the Sky crew, you’ll enjoy a range of
great benefits – from free Sky, Sky Sport Now and Neon, to discounted Southern
Cross health insurance and YMCA gym memberships. We offer free onsite parking at
our Mt Wellington and Albany office, and we’re big on supporting your growth
with exciting career development opportunities. ME PĒHEA TE TONO I HOW TO APPLY
If this opportunity excites you and you want to join us in sharing stories,
possibilities, and joy, apply online today. Please note that you must have the
right to work in New Zealand or hold a valid work visa for at least 12 months
from joining Sky. We’re reviewing applications as they come in, and once we’ve
found the right addition to our Sky Crew, we’ll blow the final whistle on this
ad. If you’re ready to get off the sidelines and into the action, apply now. At
Sky, we recognise that traditional recruitment processes don’t always work for
everyone. If you need any adjustments or accommodations to help you through the
recruitment journey, we’re here to support you — just reach out to us at
<EMAIL>. Please note, this email address is for support purposes only and
shouldn’t be used to submit job applications. RECRUITMENT AGENCIES. Sky has
established partnerships with a small number of preferred recruitment agencies,
and we will not engage with unsolicited communication outside of this pool.

================================================================================

================================================================================
END OF REPORT
================================================================================
