#!"C:\Python310\python.exe"
### Seek.co.nz Job Application Automation Bot
### Based on the structure and patterns from main9.py
### Dependencies:
'''
python -m pip uninstall undetected-chromedriver
python -m pip install git+https://github.com/ultrafunkamsterdam/undetected-chromedriver@fix-multiple-instance
python -m pip install --upgrade selenium
python -m pip install requests
python -m pip install PyPDF2
python -m pip install openai
'''

# Imports - Following the same pattern as main9.py
import requests
import os
import sys
import re
import time
import datetime
import shutil
import json
import random
import ssl
import certifi
from subprocess import PIPE, Popen
import subprocess
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support.select import Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from undetected_chromedriver import Chrome

# Additional imports for cover letter generation
import PyPDF2
import openai
from selenium import webdriver
import seleniumwire

# OpenAI API Configuration
openai_client = openai.OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

def test_selenium_proxy(profile_number=1):
    """
    Creates selenium-wire proxy options for a specific profile number
    Args:
        profile_number: Integer profile number (1, 2, 3, etc.)
    Returns:
        dict: Selenium-wire proxy options
    """
    # Use the profile number as the session ID
    proxy = "http://oc-02cd7488cb63263f789dbb2c02d86b5eb2e0c5b566dfbef9c527e283324d1d1c-country-NZ-session-{}:<EMAIL>:31111".format(profile_number)

    # proxy = 'http://WuCFLRXt006DaTsA:<EMAIL>:12321'
    # Extract username, password, host, and port from the proxy URL
    # Format: *****************************:port
    proxy_parts = proxy.replace('http://', '').split('@')
    auth = proxy_parts[0].split(':')
    host_port = proxy_parts[1].split(':')

    username = auth[0]
    password = auth[1]
    host = host_port[0]
    port = host_port[1]

    # Configure selenium-wire options with the proxy
    options = {
        'proxy': {
            'http': f'http://{username}:{password}@{host}:{port}',
            'https': f'https://{username}:{password}@{host}:{port}',
            'no_proxy': 'localhost,127.0.0.1'
        }
    }

    print(f"[*] test_selenium_proxy: Using session-{profile_number} for profile {profile_number}")
    return options

def read_resume_from_pdf(pdf_path="resume.pdf"):
    """
    Reads text content from a PDF resume file
    Args:
        pdf_path: Path to the PDF resume file
    Returns:
        str: Extracted text from the PDF
    """
    try:
        print(f"[*] Reading resume from {pdf_path}...")

        if not os.path.exists(pdf_path):
            print(f"[ERROR] Resume PDF not found at {pdf_path}")
            return "Resume not available"

        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""

            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"

            print(f"[*] Successfully extracted {len(text)} characters from resume")
            return text.strip()

    except Exception as e:
        print(f"[ERROR] Failed to read resume PDF: {e}")
        return "Resume not available due to error"

def generate_cover_letter(job_description, resume_text):
    """
    Generates a tailored cover letter using OpenAI API based on job description and resume
    Args:
        job_description: HTML job description from the job posting
        resume_text: Text content from the PDF resume
    Returns:
        str: Generated cover letter text
    """
    try:
        print("[*] Generating tailored cover letter using OpenAI...")

        # Clean job description by removing HTML tags
        import re
        clean_job_desc = re.sub(r'<[^>]+>', '', job_description)
        clean_job_desc = re.sub(r'&nbsp;', ' ', clean_job_desc)
        clean_job_desc = re.sub(r'&amp;', '&', clean_job_desc)
        clean_job_desc = re.sub(r'&lt;', '<', clean_job_desc)
        clean_job_desc = re.sub(r'&gt;', '>', clean_job_desc)
        clean_job_desc = re.sub(r'\s+', ' ', clean_job_desc).strip()

        # Create the prompt for OpenAI
        prompt = f"""
Write a professional cover letter for a job application. Use the following information:

RESUME/CV CONTENT:
{resume_text}

JOB DESCRIPTION:
{clean_job_desc}

CRITICAL INSTRUCTIONS:
- Write a concise, professional cover letter (maximum 200 words)
- Highlight relevant skills and experience from the resume that match the job requirements
- Show enthusiasm for the role and company
- Use a professional but personable tone
- NEVER EVER include ANY placeholder text such as [Your Name], [Company Name], [Position], [Date], [Hiring Manager], [Company Address], etc.
- ABSOLUTELY DO NOT use square brackets [ ] anywhere in the cover letter under any circumstances
- Write the cover letter as if it's ready to submit immediately without any editing needed
- Focus on specific achievements and skills that align with the job
- End with a strong closing statement
- If you don't know specific company details, refer to "your organization", "your team", or "your company"
- Start with "Dear Hiring Manager," and end with "Sincerely," or "Best regards,"

Cover Letter:
"""

        # Call OpenAI API using the new client
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a professional career advisor who writes excellent cover letters. You NEVER use placeholder text or square brackets. You write complete, ready-to-submit cover letters."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.7
        )

        cover_letter_content = response.choices[0].message.content
        if cover_letter_content:
            cover_letter = cover_letter_content.strip()
            print(f"[*] Successfully generated cover letter ({len(cover_letter)} characters)")
            return cover_letter
        else:
            print("[ERROR] OpenAI returned empty content")
            return "I am writing to express my strong interest in this position. Based on my background and experience, I believe I would be a valuable addition to your team."

    except Exception as e:
        print(f"[ERROR] Failed to generate cover letter: {e}")
        # Fallback to a generic message
        return "I am writing to express my strong interest in this position. Based on my background and experience, I believe I would be a valuable addition to your team. I am excited about the opportunity to contribute to your organization and would welcome the chance to discuss how my skills align with your needs. Thank you for your consideration."

def generate_textarea_answer(question_text, resume_text):
    """
    Generates an appropriate answer to a textarea question using OpenAI API based on resume content
    Args:
        question_text: The label text/question from the textarea field
        resume_text: Text content from the PDF resume
    Returns:
        str: Generated answer text
    """
    try:
        print(f"[*] Generating AI answer for question: '{question_text}'")

        # Create the prompt for OpenAI
        prompt = f"""
You are helping someone fill out a job application form. Based on their resume/CV content, provide a concise, professional answer to the following question.

RESUME/CV CONTENT:
{resume_text}

QUESTION/FIELD LABEL:
{question_text}

CRITICAL INSTRUCTIONS:
- NEVER EVER include ANY placeholder text such as [Your Name], [Company Name], [Position], [Date], [Hiring Manager], [Company Address], etc.
- ABSOLUTELY DO NOT use square brackets [ ] anywhere in the cover letter under any circumstances
- Provide a brief, relevant answer (maximum 100 words)
- Base your answer on information from the resume
- If the question is about experience, mention specific relevant experience from the resume
- If the question is about skills, highlight matching skills from the resume
- If the question is about availability, motivation, or general questions, provide a professional response
- If the resume doesn't contain relevant information, provide a brief professional response
- Do not include placeholder text or ask for more information
- Keep the tone professional but personable
- NEVER EVER include ANY placeholder text such as [Your Name], [Company Name], [Position], [Date], [Hiring Manager], [Company Address], etc.
- ABSOLUTELY DO NOT use square brackets [ ] anywhere in the cover letter under any circumstances
- Write the cover letter as if it's ready to submit immediately without any editing needed

Answer:
"""

        # Call OpenAI API using the client
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a professional career advisor helping someone complete job application forms based on their resume."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=150,
            temperature=0.7
        )

        answer_content = response.choices[0].message.content
        if answer_content:
            answer = answer_content.strip()
            print(f"[*] Generated answer ({len(answer)} characters): {answer[:100]}{'...' if len(answer) > 100 else ''}")
            return answer
        else:
            print("[ERROR] OpenAI returned empty content for textarea question")
            return "Yes, I have relevant experience and skills for this position as outlined in my resume."

    except Exception as e:
        print(f"[ERROR] Failed to generate textarea answer: {e}")
        # Fallback to a generic professional response
        return "Yes, I have relevant experience and skills for this position as outlined in my resume."

def get_random_user_agent():
    """
    Returns a random user agent string to avoid detection
    Following the same pattern as main9.py
    """
    desktop_user_agents = [
        # Windows Chrome
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
        
        # Windows Firefox
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
        
        # macOS Chrome
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        
        # macOS Safari
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15"
    ]
    selected_agent = random.choice(desktop_user_agents)
    print(f"[*] Using User Agent: {selected_agent}")
    return selected_agent

def get_proxy_url(session_id):
    """
    Generates a proxy URL with a specific session ID
    Args:
        session_id: Integer session ID for the proxy
    Returns:
        str: Formatted proxy URL
    """
    proxy = "http://oc-02cd7488cb63263f789dbb2c02d86b5eb2e0c5b566dfbef9c527e283324d1d1c-country-NZ-session-{}:<EMAIL>:31111".format(session_id)
    print(f"[*] Using proxy session ID: {session_id}")
    return proxy

def setup_persistent_directories(profile_number=1):
    """
    Creates persistent directories for Chrome profile and cache data
    Args:
        profile_number: Integer profile number (1, 2, 3, etc.)
    Returns:
        tuple: (chrome_profile_dir, chrome_cache_dir)
    """
    try:
        # Get current working directory (Seek-Auto-Apply folder)
        current_dir = os.getcwd()

        # Create numbered directory paths within the working directory
        chrome_profile_dir = os.path.join(current_dir, f"chrome_data_{profile_number}")
        chrome_cache_dir = os.path.join(current_dir, f"chrome_cache_{profile_number}")

        # Create directories if they don't exist
        os.makedirs(chrome_profile_dir, exist_ok=True)
        os.makedirs(chrome_cache_dir, exist_ok=True)

        print(f"[*] Chrome profile directory: {chrome_profile_dir}")
        print(f"[*] Chrome cache directory: {chrome_cache_dir}")

        return chrome_profile_dir, chrome_cache_dir

    except Exception as e:
        print(f"[ERROR] Failed to create persistent directories: {e}")
        # Fallback to temporary directories if creation fails
        fallback_profile = os.path.join(os.getcwd(), f"temp_chrome_profile_{profile_number}")
        fallback_cache = os.path.join(os.getcwd(), f"temp_chrome_cache_{profile_number}")

        try:
            os.makedirs(fallback_profile, exist_ok=True)
            os.makedirs(fallback_cache, exist_ok=True)
            print(f"[*] Using fallback directories: {fallback_profile}, {fallback_cache}")
            return fallback_profile, fallback_cache
        except Exception as fallback_error:
            print(f"[ERROR] Fallback directory creation failed: {fallback_error}")
            return None, None

def setup_chrome_driver(headless=False, profile_number=1):
    """
    Sets up Chrome WebDriver with persistent profile for login session persistence
    Args:
        headless (bool): Whether to run in headless mode
        profile_number (int): Profile number for multiple accounts (1, 2, 3, etc.)
    Returns:
        Chrome WebDriver instance
    """
    # Add SSL certificate fix for macOS (from main9.py pattern)
    ssl._create_default_https_context = ssl._create_unverified_context

    # Setup persistent directories for this profile
    chrome_profile_dir, chrome_cache_dir = setup_persistent_directories(profile_number)

    if chrome_profile_dir is None or chrome_cache_dir is None:
        print("[ERROR] Could not create persistent directories. Exiting...")
        return None

    # Setup Chrome options with persistent profile
    options = webdriver.ChromeOptions()

    # Persistent profile options for login session persistence
    options.add_argument(f'--user-data-dir={chrome_profile_dir}')
    options.add_argument(f"--profile-directory=SeekAutoApplyProfile_{profile_number}")  # Unique profile name

    # Performance options (modified for persistence)
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument(f'--disk-cache-dir={chrome_cache_dir}')

    # User agent options
    options.add_argument(f'--user-agent="{get_random_user_agent()}"')

    # Window options
    options.add_argument("--start-maximized")

    # Headless mode option
    if headless:
        options.add_argument("--headless")

    # Remove incognito mode to allow session persistence
    # Removed: options.add_argument("--incognito")

    # Additional options for better automation (commented out due to compatibility issues)
    # options.add_experimental_option("excludeSwitches", ["enable-automation"])
    # options.add_experimental_option('useAutomationExtension', False)

    print(f"[*] Setting up Chrome WebDriver with persistent profile {profile_number}...")
    print(f"[*] Using proxy session ID: {profile_number}")
    print("[*] Login sessions will be preserved between runs")

    # Get proxy options for this profile using the updated test_selenium_proxy function
    proxy_options = test_selenium_proxy(profile_number)
    # driver = Chrome(seleniumwire_options=proxy_options, options=options)
    driver = Chrome(options=options)

    # Do NOT clear cookies to maintain login sessions
    # Removed: driver.delete_all_cookies()

    return driver

def navigate_to_seek(profile_number=1):
    """
    Navigates to seek.co.nz homepage
    Args:
        profile_number: Profile number for multiple accounts (1, 2, 3, etc.)
    Returns:
        WebDriver instance
    """
    print(f"[*] SEEK.CO.NZ JOB APPLICATION BOT STARTING - PROFILE {profile_number} [*]")

    # Setup WebDriver with specific profile
    driver = setup_chrome_driver(headless=False, profile_number=profile_number)  # Keep visible for manual login

    if driver is None:
        print("[ERROR] Failed to setup Chrome WebDriver")
        return None

    try:
        # Navigate to Seek.co.nz
        seek_url = "https://www.seek.co.nz/oauth/login?returnUrl=http%3A%2F%2Fwww.seek.co.nz%2F"
        # seek_url = "https://www.seek.com.au/worker-jobs/in-Brisbane-QLD-4000"
        print(f"[*] Navigating to {seek_url}")
        driver.get(seek_url)

        # Wait for page to load
        driver.implicitly_wait(10)
        time.sleep(random.uniform(3.0, 5.0))

        print("[*] Successfully loaded Seek.co.nz homepage")
        return driver

    except Exception as e:
        print(f"[ERROR] Failed to navigate to Seek.co.nz: {e}")
        if driver:
            driver.quit()
        return None

def debug_page_elements(driver):
    """
    Debug function to inspect page elements and find potential login buttons
    """
    try:
        print("[*] Debugging page elements...")

        # Get all links on the page
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"[*] Found {len(links)} links on the page")

        # Look for potential login links
        potential_login_links = []
        for link in links[:20]:  # Check first 20 links
            try:
                href = link.get_attribute("href") or ""
                text = link.text.strip().lower()
                data_automation = link.get_attribute("data-automation") or ""

                if any(keyword in href.lower() for keyword in ["login", "sign", "auth"]) or \
                   any(keyword in text for keyword in ["sign", "log", "login"]) or \
                   any(keyword in data_automation.lower() for keyword in ["sign", "login"]):
                    potential_login_links.append({
                        "text": text,
                        "href": href,
                        "data_automation": data_automation
                    })
            except:
                continue

        print(f"[*] Found {len(potential_login_links)} potential login links:")
        for i, link in enumerate(potential_login_links):
            print(f"  {i+1}. Text: '{link['text']}', Href: '{link['href']}', Data-automation: '{link['data_automation']}'")

        # Get all buttons on the page
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"[*] Found {len(buttons)} buttons on the page")

        # Look for potential login buttons
        potential_login_buttons = []
        for button in buttons[:10]:  # Check first 10 buttons
            try:
                text = button.text.strip().lower()
                data_automation = button.get_attribute("data-automation") or ""

                if any(keyword in text for keyword in ["sign", "log", "login"]) or \
                   any(keyword in data_automation.lower() for keyword in ["sign", "login"]):
                    potential_login_buttons.append({
                        "text": text,
                        "data_automation": data_automation
                    })
            except:
                continue

        print(f"[*] Found {len(potential_login_buttons)} potential login buttons:")
        for i, button in enumerate(potential_login_buttons):
            print(f"  {i+1}. Text: '{button['text']}', Data-automation: '{button['data_automation']}'")

        return potential_login_links, potential_login_buttons

    except Exception as e:
        print(f"[ERROR] Error during page debugging: {e}")
        return [], []

def find_and_click_login(driver):
    """
    Locates and clicks the login button/link on Seek.co.nz homepage
    Args:
        driver: WebDriver instance
    Returns:
        bool: True if login button was found and clicked, False otherwise
    """
    try:
        print("[*] Looking for login button/link...")

        # Wait for page elements to load
        time.sleep(3)  # Give page more time to load

        # First, debug the page to see what's available
        print("[*] Page title:", driver.title)
        print("[*] Current URL:", driver.current_url)

        # Save page source for debugging
        try:
            with open("seek_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("[*] Page source saved to seek_page_source.html for debugging")
        except Exception as e:
            print(f"[*] Could not save page source: {e}")

        potential_links, potential_buttons = debug_page_elements(driver)

        # Pause for manual inspection (disabled for continuous operation)
        print("\n[*] MANUAL INSPECTION PAUSE - SKIPPED FOR CONTINUOUS OPERATION")
        print("[*] Proceeding with automated login detection...")
        time.sleep(2)  # Brief pause instead of manual input

        # Enhanced selectors for login buttons on Seek.co.nz
        login_selectors = [
            # CSS selectors
            "a[href*='login']",  # Link containing 'login' in href
            "a[href*='sign-in']",  # Link containing 'sign-in' in href
            "a[href*='signin']",  # Link containing 'signin' in href
            "button[data-automation*='sign']",  # Button with sign in automation attribute
            "a[data-automation*='sign']",  # Link with sign in automation attribute
            "a[data-automation*='login']",  # Link with login automation attribute
            "button[data-automation*='login']",  # Button with login automation attribute
            "a[class*='sign']",  # Link with sign in class
            "button[class*='sign']",  # Button with sign in class

            # XPath selectors
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'sign in')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'log in')]",
            "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'login')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'sign in')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'log in')]",
            "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'login')]"
        ]

        login_element = None
        successful_selector = None

        # Try each selector until we find the login element
        for selector in login_selectors:
            try:
                print(f"[*] Trying selector: {selector}")
                if selector.startswith("//"):
                    # XPath selector
                    login_element = WebDriverWait(driver, 2).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                else:
                    # CSS selector
                    login_element = WebDriverWait(driver, 2).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )

                successful_selector = selector
                print(f"[*] Found login element using selector: {selector}")
                break

            except TimeoutException:
                continue
            except Exception as e:
                print(f"[*] Selector {selector} failed: {e}")
                continue

        if login_element is None:
            print("[ERROR] Could not find login button/link on the page")
            print("[*] Available potential login elements found during debugging:")
            if potential_links:
                print("  Links:", potential_links)
            if potential_buttons:
                print("  Buttons:", potential_buttons)
            return False

        # Scroll to the element to ensure it's visible
        driver.execute_script("arguments[0].scrollIntoView(true);", login_element)
        time.sleep(1)

        # Click the login element
        print(f"[*] Clicking login button/link using selector: {successful_selector}")
        login_element.click()

        # Wait for navigation to login page
        time.sleep(random.uniform(2.0, 4.0))

        print("[*] Successfully clicked login button")
        print(f"[*] Current URL after click: {driver.current_url}")

        return True

    except Exception as e:
        print(f"[ERROR] Failed to find or click login button: {e}")
        return False

def detect_rate_limiter(driver, job_index):
    """
    Detects if the rate limiter error panel appears after submitting an application
    Args:
        driver: WebDriver instance
        job_index: Index of the job for logging
    Returns:
        bool: True if rate limiter detected, False otherwise
    """
    try:
        print(f"[*] Checking for rate limiter after submitting job {job_index}...")

        # Wait a moment for the error panel to appear
        time.sleep(1)

        # Look for the specific error panel that indicates rate limiting
        rate_limiter_selectors = [
            # Main error panel selector based on the provided HTML
            "div._1bdiith0._1m460an7z._1m460an93._1m460anav._1m460an9r._1m460an67._1m460an5f._1m460ani7._6bcfdt18._6bcfdt1b._6bcfdt1c._6bcfdt1l._1m460an1t._1m460an2i#errorPanel",

            # Alternative selectors for the error panel
            "div[id='errorPanel']",
            "div[role='alert'][aria-live='polite']",
            "#errorPanel",

            # More generic error panel selectors
            "div[role='alert']",
            "div.errorPanel",
            "div[class*='errorPanel']",
            "div[class*='error'][role='alert']",

            # Look for the specific error message text using XPath
            "//div[contains(@class, 'errorPanel') or @id='errorPanel']//span[contains(text(), 'Something has gone wrong')]",
            "//span[contains(text(), 'Something has gone wrong. Please try again or contact Customer Service')]",
            "//div[@role='alert']//span[contains(text(), 'Something has gone wrong')]",
            "//span[contains(text(), 'Something has gone wrong')]",
            "//div[contains(text(), 'Something has gone wrong')]",
            "//*[contains(text(), 'Something has gone wrong')]",

            # Look for cooldown/rate limit related messages
            "//*[contains(text(), 'try again') and contains(text(), 'contact')]",
            "//*[contains(text(), 'cooldown')]",
            "//*[contains(text(), 'too many')]",
            "//*[contains(text(), 'limit')]"
        ]

        rate_limiter_detected = False
        error_message = ""

        for selector in rate_limiter_selectors:
            try:
                if selector.startswith("//"):
                    # XPath selector
                    error_element = driver.find_element(By.XPATH, selector)
                else:
                    # CSS selector
                    error_element = driver.find_element(By.CSS_SELECTOR, selector)

                if error_element and error_element.is_displayed():
                    error_message = error_element.text.strip()
                    error_message_lower = error_message.lower()

                    # Check if this is the rate limiter error
                    rate_limit_indicators = [
                        "something has gone wrong",
                        "try again",
                        "contact customer service",
                        "cooldown",
                        "too many",
                        "limit exceeded",
                        "rate limit"
                    ]

                    if any(indicator in error_message_lower for indicator in rate_limit_indicators):
                        rate_limiter_detected = True
                        print(f"[!] RATE LIMITER DETECTED for job {job_index}!")
                        print(f"[!] Error message: {error_message}")
                        print(f"[!] This indicates you have applied to too many jobs and there is a cooldown period.")
                        print(f"[!] You should wait before applying to more jobs.")
                        exit()

            except NoSuchElementException:
                continue
            except Exception as e:
                print(f"[DEBUG] Error checking selector '{selector}': {e}")
                continue

        if not rate_limiter_detected:
            print(f"[*] No rate limiter detected for job {job_index} - application likely successful")

        return rate_limiter_detected

    except Exception as e:
        print(f"[ERROR] Error detecting rate limiter for job {job_index}: {e}")
        return False

def check_for_errors_and_locate_fields(driver, job_index):
    """
    Checks for error alerts and locates all select and input fields
    Args:
        driver: WebDriver instance
        job_index: Index of the job for logging
    Returns:
        bool: True if rate limiter detected (should stop processing), False otherwise
    """
    try:
        print(f"\n[*] Checking for error alerts and locating form fields for job {job_index}...")

        # Check for error alert
        # try:
        #     error_panel = driver.find_element(By.CSS_SELECTOR,
        #         "div.azpfys0._1fz17ik7z._1fz17ik93._1fz17ikav._1fz17ik9r._1fz17ik67._1fz17ik5f._1fz17iki7._1gljd4818._1gljd481b._1gljd481c._1gljd481l._1fz17ik1t._1fz17ik2i#errorPanel")

        #     print(f"[*] Error alert found for job {job_index}")

        #     # Extract error messages
        #     error_messages = driver.find_elements(By.CSS_SELECTOR,
        #         "span.azpfys0._1fz17ik4z._1gljd480._1gljd481._1gljd4821._1sm6ioh4._1gljd48a")

        #     print(f"[*] Found {len(error_messages)} error messages:")
        #     for i, error_msg in enumerate(error_messages, 1):
        #         error_text = error_msg.text.strip()
        #         if error_text and "Please make a selection" in error_text:
        #             print(f"  Error {i}: {error_text}")

        # except NoSuchElementException:
        #     print(f"[*] No error alert found for job {job_index}")

        # Locate all SELECT fields
        select_fields = driver.find_elements(By.TAG_NAME, "select")
        print(f"\n[*] Found {len(select_fields)} SELECT fields for job {job_index}:")

        for i, select_field in enumerate(select_fields, 1):
            try:
                select_id = select_field.get_attribute("id") or "No ID"
                select_name = select_field.get_attribute("name") or "No name"
                select_class = select_field.get_attribute("class") or "No class"

                # Find corresponding label using the select ID
                label_text = "No label found"
                if select_id != "No ID":
                    try:
                        # Look for label with for attribute matching select ID
                        label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{select_id}']")
                        label_text = label_element.text.strip() or "Empty label"
                    except NoSuchElementException:
                        # Try to find label with the specific class structure
                        try:
                            label_element = driver.find_element(By.CSS_SELECTOR,
                                f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{select_id}']")
                            label_text = label_element.text.strip() or "Empty label"
                        except NoSuchElementException:
                            label_text = "Label not found"

                # Get options
                options = select_field.find_elements(By.TAG_NAME, "option")
                option_texts = [opt.text.strip() for opt in options if opt.text.strip()]

                print(f"  SELECT {i}:")
                print(f"    ID: {select_id}")
                print(f"    Name: {select_name}")
                print(f"    Class: {select_class}")
                print(f"    Label: {label_text}")
                print(f"    Options ({len(option_texts)}): {option_texts[:5]}{'...' if len(option_texts) > 5 else ''}")
            except Exception as e:
                print(f"  SELECT {i}: Error getting details - {e}")

        # Locate all INPUT fields and map them to labels
        input_fields = driver.find_elements(By.TAG_NAME, "input")
        print(f"\n[*] Found {len(input_fields)} INPUT fields for job {job_index}:")

        for i, input_field in enumerate(input_fields, 1):
            try:
                input_type = input_field.get_attribute("type") or "No type"
                input_id = input_field.get_attribute("id") or "No ID"
                input_name = input_field.get_attribute("name") or "No name"
                input_class = input_field.get_attribute("class") or "No class"
                input_value = input_field.get_attribute("value") or "No value"

                # Find corresponding label using the input ID
                label_text = "No label found"
                if input_id != "No ID":
                    try:
                        # Look for label with for attribute matching input ID
                        label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{input_id}']")
                        label_text = label_element.text.strip() or "Empty label"
                    except NoSuchElementException:
                        # Try to find label with the specific class structure
                        try:
                            label_element = driver.find_element(By.CSS_SELECTOR,
                                f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{input_id}']")
                            label_text = label_element.text.strip() or "Empty label"
                        except NoSuchElementException:
                            label_text = "Label not found"

                print(f"  INPUT {i}:")
                print(f"    Type: {input_type}")
                print(f"    ID: {input_id}")
                print(f"    Name: {input_name}")
                print(f"    Class: {input_class}")
                print(f"    Value: {input_value}")
                print(f"    Label: {label_text}")

            except Exception as e:
                print(f"  INPUT {i}: Error getting details - {e}")
        # Now automatically fill SELECT fields
        print(f"\n[*] Starting automatic SELECT field completion for job {job_index}...")
        fill_select_fields_automatically(driver, select_fields, job_index)

        # Now automatically fill radio buttons (always select "Yes")
        print(f"\n[*] Starting automatic radio button completion for job {job_index}...")
        fill_radio_buttons_automatically(driver, input_fields, job_index)

        # Now automatically fill checkboxes (select all except "None" options)
        print(f"\n[*] Starting automatic checkbox completion for job {job_index}...")
        fill_checkboxes_automatically(driver, input_fields, job_index)

        # Now automatically fill textarea fields
        print(f"\n[*] Starting automatic textarea completion for job {job_index}...")
        fill_textareas_automatically(driver, job_index)

        # Click the final Continue buttons after filling all form fields
        print(f"\n[*] Clicking final Continue buttons for job {job_index}...")
        try:
            time.sleep(random.uniform(0.5, 1.0))

            # Try to find and click Continue button(s) - there are typically 2 buttons to click
            continue_buttons_clicked = 0
            max_continue_attempts = 2  # Changed from 1 to 2 to handle both buttons

            for attempt in range(max_continue_attempts):
                continue_button = None

                # Try multiple selectors for Continue button
                try:
                    continue_button = driver.find_element(By.CSS_SELECTOR, "button[data-testid='continue-button']")
                    print(f"[*] Found Continue button {attempt + 1} using data-testid for job {job_index}")
                except NoSuchElementException:
                    try:
                        continue_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Continue')]")
                        print(f"[*] Found Continue button {attempt + 1} using text for job {job_index}")
                    except NoSuchElementException:
                        try:
                            continue_button = driver.find_element(By.XPATH, "//button[.//span[contains(text(), 'Continue')]]")
                            print(f"[*] Found Continue button {attempt + 1} using span text for job {job_index}")
                        except NoSuchElementException:
                            pass

                if continue_button:
                    continue_button.click()
                    continue_buttons_clicked += 1
                    print(f"[*] Clicked Continue button {attempt + 1} for job {job_index}")
                    time.sleep(random.uniform(2.0, 3.0))  # Increased wait time between button clicks
                else:
                    print(f"[*] No more Continue buttons found after {continue_buttons_clicked} clicks for job {job_index}")
                    break

            print(f"[*] Successfully clicked {continue_buttons_clicked} Continue buttons for job {job_index}")

        except Exception as e:
            print(f"[ERROR] Failed to click Continue buttons for job {job_index}: {e}")

        # Try to click the final submit application button
        try:
            print(f"[*] Looking for final Submit Application button for job {job_index}...")
            time.sleep(random.uniform(0.5, 1.0))

            submit_button = None

            # Selector 1: Try by data-testid (most reliable)
            try:
                submit_button = driver.find_element(By.CSS_SELECTOR, "button[data-testid='review-submit-application']")
                print(f"[*] Found Submit Application button using data-testid for job {job_index}")
            except NoSuchElementException:
                pass

            # Selector 2: Try by span text "Submit application"
            if not submit_button:
                try:
                    submit_button = driver.find_element(By.XPATH, "//span[contains(text(), 'Submit application')]")
                    print(f"[*] Found Submit Application span using text for job {job_index}")
                except NoSuchElementException:
                    pass

            # Selector 3: Try by button containing "Submit application" text
            if not submit_button:
                try:
                    submit_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Submit application')]")
                    print(f"[*] Found Submit Application button using text for job {job_index}")
                except NoSuchElementException:
                    pass

            # Selector 4: Try by clickable element containing "Submit application"
            if not submit_button:
                try:
                    submit_button = driver.find_element(By.XPATH, "//*[contains(text(), 'Submit application') and (self::button or self::span or self::div)]")
                    print(f"[*] Found Submit Application element using generic text for job {job_index}")
                except NoSuchElementException:
                    pass

            if submit_button:
                submit_button.click()
                print(f"[*] Clicked final Submit Application button for job {job_index}")

                # Check for rate limiter after clicking submit
                # rate_limiter_detected = detect_rate_limiter(driver, job_index)
                rate_limiter_detected = False

                if rate_limiter_detected:
                    print(f"[!] STOPPING APPLICATION PROCESS - Rate limiter detected!")
                    print(f"[!] You have applied to too many jobs and need to wait for the cooldown period.")
                    print(f"[!] Please try again later.")
                    input("Press Enter to acknowledge and continue...")
                    return rate_limiter_detected  # Return True to indicate rate limiter
                else:
                    print(f"[*] Application for job {job_index} appears to have been submitted successfully")

                # Removed manual input for continuous operation
                print(f"[*] Continuing to next job automatically...")
                time.sleep(random.uniform(0.5, 1.0))
            else:
                print(f"[*] Final Submit Application button not found for job {job_index}")
                # input()

                # Debug: List all clickable elements with "submit" text
                try:
                    submit_elements = driver.find_elements(By.XPATH, "//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'submit')]")
                    print(f"[DEBUG] Found {len(submit_elements)} elements containing 'submit':")
                    for i, elem in enumerate(submit_elements[:3]):  # Show first 3
                        elem_tag = elem.tag_name
                        elem_text = elem.text[:50] if elem.text else "no-text"
                        elem_class = elem.get_attribute("class")[:50] if elem.get_attribute("class") else "no-class"
                        print(f"  Element {i+1}: <{elem_tag}> text='{elem_text}' class='{elem_class}...'")
                except Exception as debug_e:
                    print(f"[DEBUG] Error listing submit elements: {debug_e}")

        except Exception as e:
            print(f"[ERROR] Failed to click final Submit Application button for job {job_index}: {e}")

        print(f"\n[*] Form field analysis and automation completed for job {job_index}")
        return False  # No rate limiter detected, continue processing

    except Exception as e:
        print(f"[ERROR] Error checking for alerts and fields for job {job_index}: {e}")
        return False  # Error occurred, but continue processing

def debug_select_element(driver, select_field, field_index):
    """
    Debug function to analyze a specific select element in detail
    """
    try:
        print(f"\n[DEBUG] Analyzing SELECT field {field_index} in detail:")

        # Basic attributes
        select_id = select_field.get_attribute("id") or "No ID"
        select_name = select_field.get_attribute("name") or "No name"
        select_class = select_field.get_attribute("class") or "No class"

        print(f"  ID: {select_id}")
        print(f"  Name: {select_name}")
        print(f"  Class: {select_class}")
        print(f"  Tag: {select_field.tag_name}")
        print(f"  Enabled: {select_field.is_enabled()}")
        print(f"  Displayed: {select_field.is_displayed()}")

        # Try to create Select object
        try:
            select_obj = Select(select_field)
            print(f"  Select object created successfully")

            # Get all options using Select object
            options = select_obj.options
            print(f"  Options count (via Select): {len(options)}")

            for i, option in enumerate(options):
                option_text = option.text.strip()
                option_value = option.get_attribute("value")
                option_selected = option.is_selected()
                print(f"    Option {i}: text='{option_text}', value='{option_value}', selected={option_selected}")

        except Exception as select_error:
            print(f"  Failed to create Select object: {select_error}")

            # Fallback to manual option detection
            options = select_field.find_elements(By.TAG_NAME, "option")
            print(f"  Options count (manual): {len(options)}")

            for i, option in enumerate(options):
                option_text = option.text.strip()
                option_value = option.get_attribute("value")
                option_selected = option.is_selected()
                print(f"    Option {i}: text='{option_text}', value='{option_value}', selected={option_selected}")

        # Check for any parent elements that might be interfering
        parent = select_field.find_element(By.XPATH, "..")
        print(f"  Parent tag: {parent.tag_name}")
        print(f"  Parent class: {parent.get_attribute('class') or 'No class'}")

        return True

    except Exception as e:
        print(f"[DEBUG] Error analyzing select field {field_index}: {e}")
        return False

def fill_select_fields_automatically(driver, select_fields, job_index):
    """
    Automatically fills SELECT fields based on priority rules:
    1. Work authorization: Select "I'm a New Zealand citizen" if available
    2. All other fields: Select middle option
    """
    try:
        print(f"[*] Filling {len(select_fields)} SELECT fields automatically...")

        for i, select_field in enumerate(select_fields, 1):
            try:
                select_id = select_field.get_attribute("id") or "No ID"

                # Debug the select element in detail
                print(f"\n[*] Processing SELECT field {i}...")
                debug_select_element(driver, select_field, i)

                # Get label text for field identification
                label_text = "No label found"
                if select_id != "No ID":
                    try:
                        label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{select_id}']")
                        label_text = label_element.text.strip().lower()
                    except NoSuchElementException:
                        try:
                            label_element = driver.find_element(By.CSS_SELECTOR,
                                f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{select_id}']")
                            label_text = label_element.text.strip().lower()
                        except NoSuchElementException:
                            label_text = "label not found"

                # Create Select object for proper dropdown handling
                try:
                    select_obj = Select(select_field)
                    options = select_obj.options
                    option_texts = [opt.text.strip() for opt in options if opt.text.strip()]
                except Exception as select_error:
                    print(f"  SELECT {i}: Failed to create Select object: {select_error}")
                    # Fallback to manual option handling
                    options = select_field.find_elements(By.TAG_NAME, "option")
                    option_texts = [opt.text.strip() for opt in options if opt.text.strip()]
                    select_obj = None

                if len(option_texts) <= 1:
                    print(f"  SELECT {i}: Skipping - no selectable options")
                    continue

                print(f"  SELECT {i}: Processing field with label: {label_text}")
                print(f"    Available options: {option_texts}")

                # Priority 1: Work authorization field - look for NZ citizen option
                nz_citizen_found = False
                for j, option_text in enumerate(option_texts):
                    option_lower = option_text.lower()
                    if "new zealand" in option_lower and "citizen" in option_lower:
                        print(f"    → Found NZ citizen option at index {j}: '{option_text}'")
                        try:
                            success = False

                            # Method 1: Use Select object with visible text
                            if select_obj and not success:
                                try:
                                    select_obj.select_by_visible_text(option_text)
                                    success = True
                                    print(f"    ✓ Selected NZ citizen option using select_by_visible_text for SELECT {i}")
                                except Exception as e1:
                                    print(f"    → select_by_visible_text failed: {e1}")

                            # Method 2: Use Select object with index
                            if select_obj and not success:
                                try:
                                    select_obj.select_by_index(j)
                                    success = True
                                    print(f"    ✓ Selected NZ citizen option using select_by_index for SELECT {i}")
                                except Exception as e2:
                                    print(f"    → select_by_index failed: {e2}")

                            # Method 3: Use Select object with value
                            if select_obj and not success:
                                try:
                                    option_value = options[j].get_attribute("value")
                                    if option_value:
                                        select_obj.select_by_value(option_value)
                                        success = True
                                        print(f"    ✓ Selected NZ citizen option using select_by_value for SELECT {i}")
                                except Exception as e3:
                                    print(f"    → select_by_value failed: {e3}")

                            # Method 4: Direct click on option element
                            if not success:
                                try:
                                    # Scroll to the select element first
                                    driver.execute_script("arguments[0].scrollIntoView(true);", select_field)
                                    time.sleep(0.5)

                                    # Click the select to open dropdown
                                    select_field.click()
                                    time.sleep(0.5)

                                    # Click the specific option
                                    options[j].click()
                                    success = True
                                    print(f"    ✓ Selected NZ citizen option using direct click for SELECT {i}")
                                except Exception as e4:
                                    print(f"    → Direct click failed: {e4}")

                            # Method 5: JavaScript selection
                            if not success:
                                try:
                                    option_value = options[j].get_attribute("value")
                                    if option_value:
                                        driver.execute_script(f"arguments[0].value = '{option_value}'; arguments[0].dispatchEvent(new Event('change'));", select_field)
                                        success = True
                                        print(f"    ✓ Selected NZ citizen option using JavaScript for SELECT {i}")
                                except Exception as e5:
                                    print(f"    → JavaScript selection failed: {e5}")

                            if success:
                                nz_citizen_found = True
                                break
                            else:
                                print(f"    ✗ All selection methods failed for NZ citizen option")

                        except Exception as e:
                            print(f"    ✗ Failed to select NZ citizen option: {e}")

                # If NZ citizen option not found, use middle selection strategy
                if not nz_citizen_found:
                    middle_index = len(option_texts) // 2
                    selected_option = option_texts[middle_index]

                    print(f"    → Using middle selection strategy: index {middle_index}")
                    print(f"    → Selected option: '{selected_option}'")

                    success = False

                    # Method 1: Use Select object with visible text
                    if select_obj and not success:
                        try:
                            select_obj.select_by_visible_text(selected_option)
                            success = True
                            print(f"    ✓ Selected middle option using select_by_visible_text for SELECT {i}")
                        except Exception as e1:
                            print(f"    → select_by_visible_text failed: {e1}")

                    # Method 2: Use Select object with index
                    if select_obj and not success:
                        try:
                            select_obj.select_by_index(middle_index)
                            success = True
                            print(f"    ✓ Selected middle option using select_by_index for SELECT {i}")
                        except Exception as e2:
                            print(f"    → select_by_index failed: {e2}")

                    # Method 3: Use Select object with value
                    if select_obj and not success:
                        try:
                            option_value = options[middle_index].get_attribute("value")
                            if option_value:
                                select_obj.select_by_value(option_value)
                                success = True
                                print(f"    ✓ Selected middle option using select_by_value for SELECT {i}")
                        except Exception as e3:
                            print(f"    → select_by_value failed: {e3}")

                    # Method 4: Direct click on option element
                    if not success:
                        try:
                            # Scroll to the select element first
                            driver.execute_script("arguments[0].scrollIntoView(true);", select_field)
                            time.sleep(0.5)

                            # Click the select to open dropdown
                            select_field.click()
                            time.sleep(0.5)

                            # Click the specific option
                            options[middle_index].click()
                            success = True
                            print(f"    ✓ Selected middle option using direct click for SELECT {i}")
                        except Exception as e4:
                            print(f"    → Direct click failed: {e4}")

                    # Method 5: JavaScript selection
                    if not success:
                        try:
                            option_value = options[middle_index].get_attribute("value")
                            if option_value:
                                driver.execute_script(f"arguments[0].value = '{option_value}'; arguments[0].dispatchEvent(new Event('change'));", select_field)
                                success = True
                                print(f"    ✓ Selected middle option using JavaScript for SELECT {i}")
                        except Exception as e5:
                            print(f"    → JavaScript selection failed: {e5}")

                    if not success:
                        print(f"    ✗ All selection methods failed for middle option")

                # Small delay between selections
                time.sleep(random.uniform(0.5, 1.0))

            except Exception as e:
                print(f"  SELECT {i}: Error processing field - {e}")

        print(f"[*] Completed automatic SELECT field filling for job {job_index}")
    except Exception as e:
        print(f"[ERROR] Error in automatic SELECT field filling for job {job_index}: {e}")

def fill_radio_buttons_automatically(driver, input_fields, job_index):
    """
    Automatically fills radio buttons based on priority rules:
    1. Work authorization: Select "New Zealand citizen" options (highest priority)
    2. General questions: Select "Yes" options (medium priority)
    3. Fallback: Select middle option using Math.floor(options.length / 2) strategy (lowest priority)
    """
    try:
        # Filter for radio button inputs only
        radio_buttons = [field for field in input_fields if field.get_attribute("type") == "radio"]

        if not radio_buttons:
            print(f"[*] No radio buttons found for job {job_index}")
            return

        print(f"[*] Found {len(radio_buttons)} radio buttons for job {job_index}")

        # Group radio buttons by name (same question)
        radio_groups = {}
        for radio in radio_buttons:
            name = radio.get_attribute("name")
            if name not in radio_groups:
                radio_groups[name] = []
            radio_groups[name].append(radio)

        print(f"[*] Processing {len(radio_groups)} radio button groups...")

        for group_name, radios in radio_groups.items():
            try:
                print(f"  Radio Group: {group_name}")

                # Priority 1: Look for citizen option first
                citizen_radio = None
                yes_radio = None

                for radio in radios:
                    radio_id = radio.get_attribute("id")

                    # Get label text for this radio button
                    label_text = "No label"
                    if radio_id:
                        try:
                            label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                            label_text = label_element.text.strip()
                        except NoSuchElementException:
                            try:
                                label_element = driver.find_element(By.CSS_SELECTOR,
                                    f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{radio_id}']")
                                label_text = label_element.text.strip()
                            except NoSuchElementException:
                                label_text = "label not found"

                    print(f"    Option: {label_text}")

                    # Priority 1: Check for citizen option
                    label_lower = label_text.lower()
                    if "new zealand" in label_lower and "citizen" in label_lower:
                        citizen_radio = radio
                        print(f"    → Found NZ citizen option for group {group_name}")
                        break
                    # Priority 2: Check if this is the "Yes" option
                    elif label_text.lower() == "yes":
                        yes_radio = radio
                        print(f"    → Found 'Yes' option for group {group_name}")

                # Click the citizen radio button if found (highest priority)
                if citizen_radio:
                    try:
                        citizen_radio.click()
                        print(f"    ✓ Selected NZ citizen option for radio group {group_name}")
                        time.sleep(random.uniform(0.3, 0.7))
                    except Exception as e:
                        print(f"    ✗ Failed to select citizen option for group {group_name}: {e}")
                # Otherwise click the "Yes" radio button if found
                elif yes_radio:
                    try:
                        yes_radio.click()
                        print(f"    ✓ Selected 'Yes' for radio group {group_name}")
                        time.sleep(random.uniform(0.3, 0.7))
                    except Exception as e:
                        print(f"    ✗ Failed to select 'Yes' for group {group_name}: {e}")
                else:
                    # Priority 3: Fallback - use middle selection strategy for groups without citizen or Yes options
                    print(f"    → No citizen or 'Yes' option found for group {group_name}")
                    if len(radios) > 0:
                        middle_index = len(radios) // 2
                        fallback_radio = radios[middle_index]

                        # Get label text for the selected fallback option
                        fallback_label = "Unknown option"
                        fallback_id = fallback_radio.get_attribute("id")
                        if fallback_id:
                            try:
                                label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{fallback_id}']")
                                fallback_label = label_element.text.strip()
                            except NoSuchElementException:
                                try:
                                    label_element = driver.find_element(By.CSS_SELECTOR,
                                        f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{fallback_id}']")
                                    fallback_label = label_element.text.strip()
                                except NoSuchElementException:
                                    fallback_label = "label not found"

                        print(f"    → Using fallback middle selection strategy: index {middle_index}")
                        print(f"    → Selected fallback option: '{fallback_label}'")

                        try:
                            fallback_radio.click()
                            print(f"    ✓ Selected fallback option for radio group {group_name}")
                            time.sleep(random.uniform(0.3, 0.7))
                        except Exception as e:
                            print(f"    ✗ Failed to select fallback option for group {group_name}: {e}")
                    else:
                        print(f"    → No radio options available in group {group_name}")

            except Exception as e:
                print(f"  Error processing radio group {group_name}: {e}")

        print(f"[*] Completed automatic radio button filling for job {job_index}")
    except Exception as e:
        print(f"[ERROR] Error in automatic radio button filling for job {job_index}: {e}")

def fill_checkboxes_automatically(driver, input_fields, job_index):
    """
    Automatically fills checkboxes by selecting all except those with labels containing "None"
    """
    try:
        # Filter for checkbox inputs only
        checkboxes = [field for field in input_fields if field.get_attribute("type") == "checkbox"]

        if not checkboxes:
            print(f"[*] No checkboxes found for job {job_index}")
            return

        print(f"[*] Found {len(checkboxes)} checkboxes for job {job_index}")

        selected_count = 0
        skipped_count = 0

        for i, checkbox in enumerate(checkboxes, 1):
            try:
                checkbox_id = checkbox.get_attribute("id")
                checkbox_name = checkbox.get_attribute("name") or "No name"

                # Get label text for this checkbox
                label_text = "No label"
                if checkbox_id:
                    try:
                        label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{checkbox_id}']")
                        label_text = label_element.text.strip()
                    except NoSuchElementException:
                        try:
                            label_element = driver.find_element(By.CSS_SELECTOR,
                                f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{checkbox_id}']")
                            label_text = label_element.text.strip()
                        except NoSuchElementException:
                            label_text = "Label not found"

                print(f"  Checkbox {i}: {label_text}")

                # Check if label contains "None", "do not", or "don't" (case insensitive)
                label_lower = label_text.lower()
                if "none" in label_lower or "do not" in label_lower or "don't" in label_lower:
                    print(f"    → Skipping checkbox with exclusion keyword in label: {label_text}")
                    skipped_count += 1
                    continue

                # Check if checkbox is already selected
                if checkbox.is_selected():
                    print(f"    → Already selected: {label_text}")
                    selected_count += 1
                    continue

                # Select the checkbox
                try:
                    checkbox.click()
                    print(f"    ✓ Selected checkbox: {label_text}")
                    selected_count += 1
                    time.sleep(random.uniform(0.2, 0.5))
                except Exception as e:
                    print(f"    ✗ Failed to select checkbox '{label_text}': {e}")

            except Exception as e:
                print(f"  Checkbox {i}: Error processing - {e}")

        print(f"[*] Checkbox completion summary for job {job_index}:")
        print(f"    Total checkboxes: {len(checkboxes)}")
        print(f"    Selected: {selected_count}")
        print(f"    Skipped (None options): {skipped_count}")
        print(f"    Errors: {len(checkboxes) - selected_count - skipped_count}")

    except Exception as e:
        print(f"[ERROR] Error in automatic checkbox filling for job {job_index}: {e}")

def fill_textareas_automatically(driver, job_index):
    """
    Automatically fills textarea fields using AI to generate appropriate answers based on label text and resume content
    """
    try:
        # Find all textarea elements
        textareas = driver.find_elements(By.TAG_NAME, "textarea")

        if not textareas:
            print(f"[*] No textarea fields found for job {job_index}")
            return

        print(f"[*] Found {len(textareas)} textarea fields for job {job_index}")

        filled_count = 0

        for i, textarea in enumerate(textareas, 1):
            try:
                textarea_id = textarea.get_attribute("id") or "No ID"
                textarea_name = textarea.get_attribute("name") or "No name"
                textarea_class = textarea.get_attribute("class") or "No class"

                # Get label text for this textarea if available
                label_text = "No label found"
                if textarea_id != "No ID":
                    try:
                        label_element = driver.find_element(By.CSS_SELECTOR, f"label[for='{textarea_id}']")
                        label_text = label_element.text.strip()
                    except NoSuchElementException:
                        try:
                            label_element = driver.find_element(By.CSS_SELECTOR,
                                f"label.azpfys0._1fz17ik4._1fz17ik4z._1fz17ikh._1p9xdad0[for='{textarea_id}']")
                            label_text = label_element.text.strip()
                        except NoSuchElementException:
                            label_text = "Label not found"

                print(f"  Textarea {i}:")
                print(f"    ID: {textarea_id}")
                print(f"    Name: {textarea_name}")
                print(f"    Label: {label_text}")

                # Skip if this is the cover letter textarea (already handled)
                if "coverLetter" in textarea_id or "cover-letter" in textarea_id.lower():
                    print(f"    → Skipping cover letter textarea")
                    continue

                # Generate AI-powered answer based on label text and resume
                try:
                    # Read resume from PDF
                    resume_text = read_resume_from_pdf("resume.pdf")

                    # Generate appropriate answer using AI
                    if label_text and label_text != "No label found" and label_text != "Label not found":
                        ai_answer = generate_textarea_answer(label_text, resume_text)
                    else:
                        # Fallback for fields without clear labels
                        ai_answer = "Yes, I have relevant experience and skills for this position as outlined in my resume."

                    # Clear and fill with AI-generated answer
                    textarea.clear()
                    textarea.send_keys(ai_answer)
                    time.sleep(random.uniform(1.5, 3.0))
                    print(f"    ✓ Filled textarea with AI-generated answer ({len(ai_answer)} characters)")
                    filled_count += 1
                except Exception as e:
                    print(f"    ✗ Failed to fill textarea: {e}")
                    # Fallback to simple "yes" if AI generation fails
                    try:
                        textarea.clear()
                        textarea.send_keys("yes")
                        print(f"    ✓ Filled textarea with fallback 'yes'")
                        filled_count += 1
                        time.sleep(random.uniform(0.3, 0.7))
                    except Exception as fallback_e:
                        print(f"    ✗ Fallback also failed: {fallback_e}")

            except Exception as e:
                print(f"  Textarea {i}: Error processing - {e}")

        print(f"[*] Textarea completion summary for job {job_index}:")
        print(f"    Total textareas: {len(textareas)}")
        print(f"    Filled: {filled_count}")
        print(f"    Skipped/Errors: {len(textareas) - filled_count}")

    except Exception as e:
        print(f"[ERROR] Error in automatic textarea filling for job {job_index}: {e}")

def extract_job_description_from_url(driver, job_url, job_index):
    """
    Visits a job URL, checks for Quick Apply button, extracts description and handles application
    Args:
        driver: WebDriver instance
        job_url: URL of the job posting
        job_index: Index of the job for logging
    Returns:
        dict: Job description and quick apply status
    """
    try:
        print(f"[*] Processing job {job_index}...")

        # Navigate to the job URL
        driver.get(job_url)
        time.sleep(random.uniform(2.0, 4.0))  # Wait for page to load

        # Check for Quick Apply button first - must contain "Quick apply" text
        try:
            # Look for button with the newest format only
            apply_buttons = driver.find_elements(By.CSS_SELECTOR,
                "span._32fem00._1nh354w50.gyz43x0.gyz43x2.gyz43x1t.gyz43xa._1lwlriv4")

            quick_apply_button = None
            for button in apply_buttons:
                button_text = button.text.strip().lower()
                if "quick apply" in button_text:
                    quick_apply_button = button
                    print(f"[*] Quick Apply button found for job {job_index}")
                    break
                elif button_text == "apply":
                    print(f"[*] Found 'Apply' button (external site) for job {job_index} - SKIPPING")
                    return {
                        "description": "SKIPPED - External site (Apply button without 'Quick')",
                        "quick_apply_available": False,
                        "skipped": True
                    }

            # if quick_apply_button is None:
            #     # Fallback: look for any element containing "Quick apply" text
            #     print(f"[*] Trying fallback method to find Quick Apply button for job {job_index}...")

            #     # Try to find by text content using XPath
            #     try:
            #         quick_apply_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Quick apply')]")
            #         for element in quick_apply_elements:
            #             if element.text.strip().lower() == "quick apply":
            #                 quick_apply_button = element
            #                 print(f"[*] Found Quick Apply button via text search for job {job_index}")
            #                 break
            #     except:
            #         pass

                # If still not found, try broader search
                if quick_apply_button is None:
                    all_buttons = driver.find_elements(By.TAG_NAME, "button")
                    all_spans = driver.find_elements(By.TAG_NAME, "span")
                    all_elements = all_buttons + all_spans

                    for element in all_elements:
                        try:
                            element_text = element.text.strip().lower()
                            if element_text == "quick apply":
                                quick_apply_button = element
                                print(f"[*] Found Quick Apply button via broad search for job {job_index}")
                                break
                        except:
                            continue

            if quick_apply_button is None:
                print(f"[*] No Quick Apply button found for job {job_index} - SKIPPING (external site)")
                # input()
                return {
                    "description": "SKIPPED - External site (no Quick Apply button)",
                    "quick_apply_available": False,
                    "skipped": True
                }

        except NoSuchElementException:
            print(f"[*] No apply buttons found for job {job_index} - SKIPPING")
            return {
                "description": "SKIPPED - No apply buttons found",
                "quick_apply_available": False,
                "skipped": True
            }

        # Extract job description if Quick Apply is available
        try:
            # Try newest format first (with data-automation="jobAdDetails")
            try:
                description_element = driver.find_element(By.CSS_SELECTOR, "div[data-automation='jobAdDetails']")
                print(f"[*] Found job description using newest format for job {job_index}")
            except NoSuchElementException:
                # Try previous new format
                try:
                    description_element = driver.find_element(By.CSS_SELECTOR, "div.ube6hn0._39dx750")
                    print(f"[*] Found job description using previous new format for job {job_index}")
                except NoSuchElementException:
                    # Fallback to old format
                    description_element = driver.find_element(By.CSS_SELECTOR, "div.xhgj00._39dx750")
                    print(f"[*] Found job description using old format for job {job_index}")

            job_description = description_element.get_attribute("innerHTML").strip()
            print(f"[*] Successfully extracted description for job {job_index}")

            # Click Quick Apply button
            try:
                print(f"[*] Clicking Quick Apply button for job {job_index}...")
                # input()
                quick_apply_button.click()
                time.sleep(random.uniform(2.0, 3.0))  # Wait for application form to load
                # Generate and write tailored cover letter
                try:
                    # Try multiple selectors for cover letter textarea
                    cover_letter_textarea = None

                    # Selector 1: Current Seek.co.nz cover letter textarea
                    try:
                        cover_letter_textarea = driver.find_element(By.CSS_SELECTOR,
                            "textarea._1bdiith0._1bdiith1._1bdiith6._1bdiith7._1m460an5f._1m460an8._1m460anp._1m460anb7._1m460ana3._1m460an5._3030mq0.yxe8al0.yxe8al1._6bcfdt0._6bcfdt1._6bcfdt21._6bcfdti._6bcfdt26")
                        print(f"[*] Found cover letter textarea using current selector for job {job_index}")
                    except NoSuchElementException:
                        pass

                    # Selector 2: Alternative by ID pattern
                    if not cover_letter_textarea:
                        try:
                            cover_letter_textarea = driver.find_element(By.CSS_SELECTOR, "textarea[id*='coverLetter']")
                            print(f"[*] Found cover letter textarea using ID pattern for job {job_index}")
                        except NoSuchElementException:
                            pass

                    # Selector 3: Alternative by data-testid
                    if not cover_letter_textarea:
                        try:
                            cover_letter_textarea = driver.find_element(By.CSS_SELECTOR, "textarea[data-testid*='coverLetter']")
                            print(f"[*] Found cover letter textarea using data-testid for job {job_index}")
                        except NoSuchElementException:
                            pass

                    # Selector 4: Alternative by aria-label
                    if not cover_letter_textarea:
                        try:
                            cover_letter_textarea = driver.find_element(By.CSS_SELECTOR, "textarea[aria-label*='cover letter']")
                            print(f"[*] Found cover letter textarea using aria-label for job {job_index}")
                        except NoSuchElementException:
                            pass

                    if not cover_letter_textarea:
                        raise NoSuchElementException("Cover letter textarea not found with any selector")

                    # Read resume from PDF
                    resume_text = read_resume_from_pdf("resume.pdf")

                    # Generate tailored cover letter using OpenAI
                    cover_letter = generate_cover_letter(job_description, resume_text)

                    # Clear existing text and write generated cover letter
                    cover_letter_textarea.clear()
                    cover_letter_textarea.send_keys(cover_letter)
                    time.sleep(random.uniform(1.5, 3.0))
                    print(f"[*] Generated and wrote tailored cover letter for job {job_index} ({len(cover_letter)} characters)")
                    # Click the first Continue button
                    try:
                        # Try multiple selectors for Continue button
                        continue_button_1 = None

                        # Selector 1: Current data-testid selector (most reliable)
                        try:
                            continue_button_1 = driver.find_element(By.CSS_SELECTOR, "button[data-testid='continue-button']")
                            print(f"[*] Found first Continue button using data-testid for job {job_index}")
                        except NoSuchElementException:
                            pass

                        # Selector 2: Alternative by button text
                        if not continue_button_1:
                            try:
                                continue_button_1 = driver.find_element(By.XPATH, "//button[contains(text(), 'Continue')]")
                                print(f"[*] Found first Continue button using text for job {job_index}")
                            except NoSuchElementException:
                                pass

                        # Selector 3: Alternative by span text inside button
                        if not continue_button_1:
                            try:
                                continue_button_1 = driver.find_element(By.XPATH, "//button[.//span[contains(text(), 'Continue')]]")
                                print(f"[*] Found first Continue button using span text for job {job_index}")
                            except NoSuchElementException:
                                pass

                        if continue_button_1:
                            continue_button_1.click()
                            print(f"[*] Clicked first Continue button for job {job_index}")
                            time.sleep(random.uniform(2.0, 3.0))

                            # Click the second Continue button (same selectors)
                            continue_button_2 = None

                            # Try the same selectors for second button
                            try:
                                continue_button_2 = driver.find_element(By.CSS_SELECTOR, "button[data-testid='continue-button']")
                                print(f"[*] Found second Continue button using data-testid for job {job_index}")
                            except NoSuchElementException:
                                pass

                            if not continue_button_2:
                                try:
                                    continue_button_2 = driver.find_element(By.XPATH, "//button[contains(text(), 'Continue')]")
                                    print(f"[*] Found second Continue button using text for job {job_index}")
                                except NoSuchElementException:
                                    pass

                            if not continue_button_2:
                                try:
                                    continue_button_2 = driver.find_element(By.XPATH, "//button[.//span[contains(text(), 'Continue')]]")
                                    print(f"[*] Found second Continue button using span text for job {job_index}")
                                except NoSuchElementException:
                                    pass

                            if continue_button_2:
                                continue_button_2.click()
                                print(f"[*] Clicked second Continue button for job {job_index}")
                                time.sleep(random.uniform(2.0, 3.0))

                                # Check for error alert and locate form fields
                                rate_limiter_detected = check_for_errors_and_locate_fields(driver, job_index)

                                # If rate limiter detected, stop processing more jobs
                                if rate_limiter_detected:
                                    print(f"[!] Rate limiter detected - stopping job application process")
                                    return  # Exit the function to stop processing more jobs
                            else:
                                print(f"[*] Second Continue button not found for job {job_index}")
                        else:
                            print(f"[*] First Continue button not found for job {job_index}")

                    except NoSuchElementException:
                        print(f"[*] First Continue button not found for job {job_index}")
                        # Removed manual input for continuous operation

                except NoSuchElementException:
                    print(f"[*] Cover letter textarea not found for job {job_index}")

                    # Debug: List all textareas on the page
                    try:
                        all_textareas = driver.find_elements(By.TAG_NAME, "textarea")
                        print(f"[DEBUG] Found {len(all_textareas)} textarea elements on page:")
                        for i, ta in enumerate(all_textareas[:5]):  # Show first 5
                            ta_id = ta.get_attribute("id") or "no-id"
                            ta_class = ta.get_attribute("class") or "no-class"
                            ta_testid = ta.get_attribute("data-testid") or "no-testid"
                            ta_label = ta.get_attribute("aria-label") or "no-label"
                            print(f"  Textarea {i+1}: id='{ta_id}', class='{ta_class[:50]}...', testid='{ta_testid}', label='{ta_label}'")
                    except Exception as debug_e:
                        print(f"[DEBUG] Error listing textareas: {debug_e}")

            except Exception as e:
                print(f"[ERROR] Failed to click Quick Apply button for job {job_index}: {e}")

            return {
                "description": job_description,
                "quick_apply_available": True,
                "skipped": False
            }

        except NoSuchElementException:
            print(f"[*] No description found for job {job_index}")
            # Removed manual input for continuous operation
            return {
                "description": "Description not found on job page",
                "quick_apply_available": True,
                "skipped": False
            }

    except Exception as e:
        print(f"[ERROR] Failed to process job {job_index}: {e}")
        return {
            "description": f"Error processing job: {e}",
            "quick_apply_available": False,
            "skipped": True
        }

def find_and_print_job_listings(driver):
    """
    Finds and prints all job listings with class 'xhgj00' on the current page,
    then visits each job URL to extract detailed descriptions
    Args:
        driver: WebDriver instance
    Returns:
        list: List of job elements found
    """
    try:
        print("\n[*] SEARCHING FOR JOB LISTINGS...")
        # input("Press Enter to continue...")

        # Find all elements with class 'ube6hn0' that contain job data (updated for new format)
        job_elements = driver.find_elements(By.CSS_SELECTOR, "div._32fem00[data-search-sol-meta]")

        # Fallback: try old format if new format returns no results
        if len(job_elements) == 0:
            print("[*] No jobs found with new format, trying old format...")
            job_elements = driver.find_elements(By.CSS_SELECTOR, "div.xhgj00[data-search-sol-meta]")

        print(f"[*] Found {len(job_elements)} job listings with data-search-sol-meta")

        job_data = []

        # Store the original page URL to return to later
        original_url = driver.current_url

        for i, job_element in enumerate(job_elements, 1):
            try:
                print(f"\n--- JOB {i} ---")

                # Extract job title
                try:
                    job_title_element = job_element.find_element(By.CSS_SELECTOR, "[data-automation='jobTitle']")
                    job_title = job_title_element.text.strip()
                    print(f"Title: {job_title}")
                except:
                    job_title = "Title not found"
                    print(f"Title: {job_title}")

                # Extract company name
                try:
                    company_element = job_element.find_element(By.CSS_SELECTOR, "[data-automation='jobAdvertiser']")
                    company_name = company_element.text.strip()
                    print(f"Company: {company_name}")
                except:
                    company_name = "Company not found"
                    print(f"Company: {company_name}")

                # Extract location
                try:
                    location_elements = job_element.find_elements(By.CSS_SELECTOR, "span._32fem00._1nh354w50.gyz43x0.gyz43x1.gyz43x1u.gyz43x6._1lwlriv4")
                    location = "Location not found"
                    for loc_elem in location_elements:
                        text = loc_elem.text.strip()
                        if text and "," in text and any(city in text for city in ["Auckland", "Wellington", "Christchurch", "Hamilton", "Tauranga", "Dunedin"]):
                            location = text
                            break
                    print(f"Location: {location}")
                except:
                    location = "Location not found"
                    print(f"Location: {location}")

                # Extract job link
                try:
                    job_link_element = job_element.find_element(By.CSS_SELECTOR, "a[data-automation*='recommendedJobLink']")
                    job_href = job_link_element.get_attribute("href")
                    print(f"Link: {job_href}")
                except:
                    job_href = "Link not found"
                    print(f"Link: {job_href}")

                # Extract job ID from data-automation attribute
                try:
                    job_link_element = job_element.find_element(By.CSS_SELECTOR, "a[data-automation*='recommendedJobLink']")
                    data_automation = job_link_element.get_attribute("data-automation")
                    job_id = data_automation.split("_")[-1] if "_" in data_automation else "ID not found"
                    print(f"Job ID: {job_id}")
                except:
                    job_id = "ID not found"
                    print(f"Job ID: {job_id}")

                # Extract posting date
                try:
                    # Look for time-related text (e.g., "3d ago", "1w ago") - updated for new format
                    time_elements = job_element.find_elements(By.CSS_SELECTOR, "span.ube6hn0.wc8kxl4z.m81yar0.m81yar1.m81yar1u.m81yar4")

                    # Fallback: try old format if new format doesn't work
                    if not time_elements:
                        time_elements = job_element.find_elements(By.CSS_SELECTOR, "span.xhgj00.ciuj3f4z.eu0zaq0.eu0zaq1.eu0zaq1u.eu0zaq4")

                    posting_date = "Date not found"
                    for time_elem in time_elements:
                        text = time_elem.text.strip()
                        if text and ("ago" in text or "d" in text or "w" in text or "h" in text):
                            posting_date = text
                            break
                    print(f"Posted: {posting_date}")
                except:
                    posting_date = "Date not found"
                    print(f"Posted: {posting_date}")

                # Store job data
                job_info = {
                    "index": i,
                    "title": job_title,
                    "company": company_name,
                    "location": location,
                    "link": job_href,
                    "job_id": job_id,
                    "posted": posting_date,
                    "element": job_element
                }
                job_data.append(job_info)

            except Exception as e:
                print(f"[ERROR] Error processing job {i}: {e}")
                continue

        print(f"\n[*] SUMMARY: Successfully extracted {len(job_data)} job listings")

        # Now extract detailed descriptions from each job URL and check for Quick Apply
        print(f"\n[*] EXTRACTING DETAILED JOB DESCRIPTIONS AND CHECKING FOR QUICK APPLY...")
        print(f"[*] This will visit {len(job_data)} job URLs to get full descriptions...")
        print(f"[*] Jobs without Quick Apply buttons will be skipped (external sites)")

        processed_jobs = []
        skipped_count = 0

        for i, job in enumerate(job_data, 1):
            if job['link'] != "Link not found":
                try:
                    print(f"\n[*] Processing job {i}/{len(job_data)}: {job['title']}")
                    result = extract_job_description_from_url(driver, job['link'], i)

                    # Update job with results
                    job['description'] = result['description']
                    job['quick_apply_available'] = result['quick_apply_available']
                    job['skipped'] = result.get('skipped', False)

                    if result.get('skipped', False):
                        skipped_count += 1
                        print(f"[*] Job {i} skipped - external site")
                    else:
                        processed_jobs.append(job)
                        print(f"[*] Job {i} processed successfully")

                    # Removed delay for faster job listing

                except Exception as e:
                    print(f"[ERROR] Failed to process job {i}: {e}")
                    job['description'] = f"Error processing job: {e}"
                    job['quick_apply_available'] = False
                    job['skipped'] = True
                    skipped_count += 1
            else:
                job['description'] = "No valid link to extract description"
                job['quick_apply_available'] = False
                job['skipped'] = True
                skipped_count += 1

        print(f"\n[*] DESCRIPTION EXTRACTION COMPLETED")
        print(f"[*] Total jobs found: {len(job_data)}")
        print(f"[*] Jobs with Quick Apply (processed): {len(job_data) - skipped_count}")
        print(f"[*] Jobs skipped (external sites): {skipped_count}")

        # Update job_data to only include non-skipped jobs for file output
        job_data = [job for job in job_data if not job.get('skipped', False)]

        # Save job data to text file
        if job_data:
            save_jobs_to_file(job_data)

        return job_data

    except Exception as e:
        print(f"[ERROR] Error finding job listings: {e}")
        return []

def save_jobs_to_file(job_data):
    """
    Saves job listings data to a text file
    Args:
        job_data: List of job dictionaries
    """
    try:
        # Generate filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"seek_jobs_{timestamp}.txt"

        print(f"\n[*] Saving job listings to {filename}...")

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("SEEK.CO.NZ JOB LISTINGS EXTRACTION\n")
            f.write(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Jobs Found: {len(job_data)}\n")
            f.write("="*80 + "\n\n")

            for job in job_data:
                f.write(f"JOB #{job['index']}\n")
                f.write("-" * 40 + "\n")
                f.write(f"Title: {job['title']}\n")
                f.write(f"Company: {job['company']}\n")
                f.write(f"Location: {job['location']}\n")
                f.write(f"Posted: {job['posted']}\n")
                f.write(f"Job ID: {job['job_id']}\n")
                f.write(f"Link: {job['link']}\n")
                f.write(f"Quick Apply Available: {job.get('quick_apply_available', 'Unknown')}\n")
                f.write(f"Description:\n")

                # Clean and format description for text file
                if 'description' in job and job['description'] not in ["Description not found", "Extracting..."]:
                    import re
                    # Remove HTML tags and clean up formatting
                    clean_desc = re.sub(r'<[^>]+>', '', job['description'])
                    clean_desc = re.sub(r'&nbsp;', ' ', clean_desc)
                    clean_desc = re.sub(r'&amp;', '&', clean_desc)
                    clean_desc = re.sub(r'&lt;', '<', clean_desc)
                    clean_desc = re.sub(r'&gt;', '>', clean_desc)
                    clean_desc = re.sub(r'\s+', ' ', clean_desc).strip()

                    # Wrap text to 80 characters for readability
                    import textwrap
                    wrapped_desc = textwrap.fill(clean_desc, width=80)
                    f.write(f"{wrapped_desc}\n")
                else:
                    f.write(f"{job.get('description', 'Description not available')}\n")

                f.write("\n" + "="*80 + "\n\n")

            f.write("="*80 + "\n")
            f.write("END OF REPORT\n")
            f.write("="*80 + "\n")

        print(f"[*] Successfully saved {len(job_data)} jobs to {filename}")

        # Also create a CSV-style file for easy data processing
        csv_filename = f"seek_jobs_{timestamp}.csv"
        with open(csv_filename, 'w', encoding='utf-8') as f:
            f.write("Job_Number,Title,Company,Location,Posted,Job_ID,Link,Quick_Apply_Available,Description\n")
            for job in job_data:
                # Escape commas and quotes in CSV data
                title = job['title'].replace('"', '""').replace(',', ';')
                company = job['company'].replace('"', '""').replace(',', ';')
                location = job['location'].replace('"', '""').replace(',', ';')
                posted = job['posted'].replace('"', '""').replace(',', ';')
                job_id = job['job_id'].replace('"', '""').replace(',', ';')
                link = job['link'].replace('"', '""')
                quick_apply = str(job.get('quick_apply_available', 'Unknown'))

                # Clean description for CSV
                if 'description' in job and job['description'] not in ["Description not found", "Extracting..."]:
                    import re
                    clean_desc = re.sub(r'<[^>]+>', '', job['description'])
                    clean_desc = re.sub(r'&nbsp;', ' ', clean_desc)
                    clean_desc = re.sub(r'&amp;', '&', clean_desc)
                    clean_desc = re.sub(r'&lt;', '<', clean_desc)
                    clean_desc = re.sub(r'&gt;', '>', clean_desc)
                    clean_desc = re.sub(r'\s+', ' ', clean_desc).strip()
                    clean_desc = clean_desc.replace('"', '""').replace('\n', ' ').replace('\r', ' ')
                    description = clean_desc
                else:
                    description = job.get('description', 'Description not available')

                f.write(f'{job["index"]},"{title}","{company}","{location}","{posted}","{job_id}","{link}","{quick_apply}","{description}"\n')

        print(f"[*] Also saved CSV format to {csv_filename}")

    except Exception as e:
        print(f"[ERROR] Failed to save jobs to file: {e}")

def check_login_status(driver):
    """
    Checks if user is already logged in to Seek.co.nz
    Args:
        driver: WebDriver instance
    Returns:
        bool: True if already logged in, False otherwise
    """
    try:
        current_url = driver.current_url
        page_title = driver.title

        print(f"[*] Checking login status...")
        print(f"[*] Current URL: {current_url}")
        print(f"[*] Page title: {page_title}")

        # Check if we're on a login page or already logged in
        login_indicators = ["login", "sign-in", "signin", "auth"]
        logged_in_indicators = ["dashboard", "profile", "jobs", "search"]

        url_lower = current_url.lower()
        title_lower = page_title.lower()

        # If URL or title contains login indicators, user needs to log in
        if any(indicator in url_lower for indicator in login_indicators):
            print("[*] Login required - currently on login page")
            return False

        # If URL or title contains logged-in indicators, user is likely logged in
        if any(indicator in url_lower or indicator in title_lower for indicator in logged_in_indicators):
            print("[*] User appears to be already logged in!")
            return True

        # Additional check: look for user-specific elements on the page
        try:
            # Look for common logged-in elements
            user_elements = driver.find_elements(By.CSS_SELECTOR,
                "[data-automation*='user'], [data-automation*='profile'], [data-automation*='account']")

            if user_elements:
                print(f"[*] Found {len(user_elements)} user-related elements - likely logged in")
                return True
        except:
            pass

        print("[*] Login status unclear - will proceed with login flow")
        return False

    except Exception as e:
        print(f"[ERROR] Error checking login status: {e}")
        return False

def wait_for_manual_login(driver):
    """
    Waits for user to complete manual login process (modified for continuous operation)
    Args:
        driver: WebDriver instance
    """
    print("\n" + "="*60)
    print("[*] MANUAL LOGIN REQUIRED")
    print("[*] Please complete the Google login process manually")
    print("[*] The bot will automatically detect when login is complete")
    print("[*] Waiting for login completion...")
    print("="*60 + "\n")

    # Auto-detect login completion instead of manual input
    max_wait_time = 300  # 5 minutes maximum wait
    check_interval = 5   # Check every 5 seconds
    elapsed_time = 0

    while elapsed_time < max_wait_time:
        time.sleep(check_interval)
        elapsed_time += check_interval

        # Check if login is complete by looking for indicators
        current_url = driver.current_url
        if "seek.co.nz" in current_url and not any(x in current_url.lower() for x in ["login", "oauth", "auth"]):
            print("[*] Login detected as complete!")
            break

        print(f"[*] Still waiting for login... ({elapsed_time}/{max_wait_time} seconds)")

    if elapsed_time >= max_wait_time:
        print("[WARNING] Login wait timeout reached. Proceeding anyway...")

    print("[*] Continuing with automation...")
    print(f"[*] Current URL: {driver.current_url}")

def cleanup_driver(driver):
    """
    Properly closes the WebDriver and cleans up processes
    Following the cleanup pattern from main9.py
    """
    try:
        print("[*] Cleaning up WebDriver...")
        driver.quit()
        
        # Kill Chrome processes (following main9.py pattern)
        if os.name == 'nt':  # Windows
            subprocess.Popen('taskkill /f /im chrome.exe', shell=True,
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=subprocess.PIPE)
        else:  # Unix/Linux/Mac
            Popen('pkill Chrome', shell=True,
                           stdout=PIPE, stderr=PIPE, stdin=PIPE)
        
        print("[*] Cleanup completed")
        
    except Exception as e:
        print(f"[ERROR] Error during cleanup: {e}")

def main(profile_number=1):
    """
    Main function to run the Seek.co.nz automation bot
    Args:
        profile_number: Profile number for multiple accounts (1, 2, 3, etc.)
    """
    driver = None

    try:
        # Step 1: Navigate to Seek.co.nz with specific profile
        driver = navigate_to_seek(profile_number)
        if driver is None:
            return

        # Step 2: Check if user is already logged in (due to persistent profile)
        is_logged_in = check_login_status(driver)

        if is_logged_in:
            print("\n[*] USER ALREADY LOGGED IN!")
            print("[*] Persistent profile working - no manual login required")
            print("[*] Proceeding directly to job application functionality")
        else:
            print("\n[*] Login required - proceeding with login flow")

            # Step 3: Find and click login button (if needed)
            login_success = find_and_click_login(driver)
            if not login_success:
                print("[ERROR] Failed to click login button. Exiting...")
                return

            # Step 4: Wait for manual login completion
            wait_for_manual_login(driver)

        # Step 5: Extract job listings from the current page
        print("\n[*] LOGIN PHASE COMPLETED")
        print("[*] Now extracting job listings from the page...")
        # time.sleep(3000)
        # Navigate to main jobs page if not already there
        try:
            current_url = driver.current_url
            if "seek.co.nz" in current_url and not any(x in current_url for x in ["/jobs", "/search"]):
                print("[*] Navigating to main Seek.co.nz page to find job listings...")
                driver.get("https://www.seek.co.nz/")
                time.sleep(3)
        except Exception as e:
            print(f"[*] Navigation note: {e}")

        # Find and print all job listings
        job_listings = find_and_print_job_listings(driver)

        if job_listings:
            print(f"\n[*] SUCCESS: Found and extracted {len(job_listings)} job listings!")
            print("[*] All job data has been printed above")
            print("[*] Job data has also been saved to text and CSV files")
        else:
            print("\n[*] No job listings found on current page")
            # Removed manual input for continuous operation
            print("[*] You may need to navigate to a jobs search page manually")

        print("\n[*] JOB EXTRACTION COMPLETED")
        print("[*] Login session will be preserved for next run due to persistent profile")

        # Modified for continuous operation
        print("\n[*] Cycle completed. Browser will be cleaned up automatically.")
        print("[*] Preparing for next cycle...")

    except KeyboardInterrupt:
        print("\n[*] Bot interrupted by user")

    except Exception as e:
        print(f"[ERROR] Unexpected error in main function: {e}")

    finally:
        # Always cleanup
        if driver:
            cleanup_driver(driver)

if __name__ == "__main__":
    import sys

    # Check if profile number is provided as command line argument
    profile_number = 1  # Default profile

    if len(sys.argv) > 1:
        try:
            profile_number = int(sys.argv[1])
            if profile_number < 1:
                print("[ERROR] Profile number must be 1 or greater")
                sys.exit(1)
        except ValueError:
            print("[ERROR] Profile number must be a valid integer")
            print("Usage: python seek_auto_apply.py [profile_number]")
            print("Example: python seek_auto_apply.py 2")
            sys.exit(1)

    print(f"[*] Starting continuous job application bot with profile {profile_number}")
    print(f"[*] Chrome data will be stored in: chrome_data_{profile_number}")
    print(f"[*] Proxy session ID: {profile_number}")
    print(f"[*] Bot will run continuously until rate limiter is detected")
    print(f"[*] When rate limiter is hit, the program will automatically exit")
    print()

    # Continuous loop - runs until rate limiter is detected and exit() is called
    cycle_count = 0
    while True:
        cycle_count += 1
        print(f"\n{'='*60}")
        print(f"[*] STARTING JOB APPLICATION CYCLE #{cycle_count}")
        print(f"{'='*60}")

        try:
            main(profile_number)

            # If we reach here, the cycle completed without rate limiter
            print(f"\n[*] Cycle #{cycle_count} completed successfully")
            print(f"[*] Waiting 30 seconds before starting next cycle...")
            time.sleep(30)  # Wait 30 seconds between cycles

        except SystemExit:
            # This happens when exit() is called due to rate limiter detection
            print(f"\n[!] Program terminated due to rate limiter detection in cycle #{cycle_count}")
            print(f"[!] Total cycles completed before rate limit: {cycle_count}")
            break

        except KeyboardInterrupt:
            print(f"\n[*] Bot interrupted by user during cycle #{cycle_count}")
            print(f"[*] Total cycles completed: {cycle_count}")
            break

        except Exception as e:
            print(f"\n[ERROR] Unexpected error in cycle #{cycle_count}: {e}")
            print(f"[*] Waiting 60 seconds before retrying...")
            time.sleep(60)  # Wait longer on errors
            continue
