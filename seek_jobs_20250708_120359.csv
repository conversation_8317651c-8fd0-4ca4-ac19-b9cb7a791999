Job_Number,Title,Company,Location,Posted,Job_ID,Link,Quick_Apply_Available,Description
1,"Front-End Developer (React) – UtilityTrack","TDG Environmental","Location not found","Opportunities for professional growth and career development","85315148","https://www.seek.co.nz/job/85315148?ref=recom-homepage&pos=1#sol=4ef01a603c95090b66b014d63d873615d9331da2","True","Join a Dynamic Team and Drive Operational Excellence! TDG Environmental is seeking a talented and driven Front-End Developer to join our growing tech team, based in either Dandenong or Truganina (VIC), or Wetherill Park (NSW). About Us With over 34 years’ experience in the sustainable waste management and recycling industry, TDG Environmental is a proudly Australian owned company, operating throughout Australia and New Zealand. We specialise in providing innovative solutions to stormwater and critical infrastructure assets. Our purpose is to deliver sustainable value for our employees and all stakeholders. Our people are the foundation of our business and are vital to our success now and into the future. TDG Environmental offers a safe, empowering and inclusive work environment that is as diverse as the communities in which we operate. Front-End Developer (React) – UtilityTrackAbout the RoleAs our Front-End Developer, you will take the lead on building intuitive, high-performance user interfaces for our in-house platform, UtilityTrack. Working closely with the Product Manager and Technical Lead, you’ll play a critical role in delivering front-end solutions that support asset inspection, condition data capture, GIS mapping, and works management.While your focus will be on front-end development using React, your ability to contribute across the stack will be a valuable asset, particularly when working within our AWS serverless architecture and supporting integrations with back-end services.This is a hands-on role with real impact — you’ll shape the user experience of a platform that supports field operations and infrastructure asset management across Australia.Key ResponsibilitiesBuild and maintain responsive, accessible, and performant interfaces using ReactCollaborate on the design and implementation of UI/UX patterns that enhance usability in a field-first environmentIntegrate with RESTful APIs and work alongside backend developers to ensure seamless user experiencesContribute to the evolution of design systems and reusable component librariesSupport testing and deployment of front-end features via CI/CD pipelines (SST, GitHub, Amplify, etc.)Monitor front-end performance and error handling using CloudWatch or similar observability toolsMaintain clean, well-documented code and participate in code reviews, and establish test coverageWork cross-functionally to align deliverables with product goals and timelinesAbout YouTo be successful in this role, you will have:3+ years of professional experience building modern front-end applications using React or similar frameworksA strong understanding of TypeScript, state management and modern front-end toolingDemonstrated experience integrating with RESTful APIs and cloud-hosted backendsAn eye for detail when it comes to design, accessibility, and user experienceFamiliarity with AWS services (especially S3, CloudFront, Cognito, and API Gateway)Experience with CI/CD pipelines, automated testing, and version control workflowsExcellent collaboration and communication skills; able to work effectively independently to reach a solutionA degree in Computer Science, Software Engineering, or equivalent practical experienceBonus Points for Experience with:AWS Lambda, DynamoDB, AWS RDS, or other backend/serverless servicesInfrastructure as Code (SST v3)GIS mapping tools or spatial data (Mapbox, Leaflet, etc.)Machine learning experienceAWS Certifications (Developer Associate, Solutions Architect, etc.)Why Join TDG Environmental? A supportive, high-performance team environment. Competitive salary package. Opportunities for professional growth and career development. Be part of an innovative company committed to continuous improvement. What We Offer TDG Environmental values its employees and understands the importance of employee offerings. Your contributions will be rewarded with a supportive work environment, as well as being part of a values driven company committed to your career succession. How To Apply If you are a motivated individual who thrives in a fast-paced and dynamic environment, please apply with your resume today by clicking the ‘Apply’ button. TDG prides itself on being a workplace that actively seeks to include, welcome and value the unique contributions of all people. We encourage applications from diverse community groups and ages to apply. Please note: Only shortlisted candidates will be contacted for an arrangement of an initial interview with TDG Environmental. Prospective candidates must have the right to work and live in Australia to be considered for this position."
2,"Integration Lead","iterate","Location not found","Major transformation initiatives across a growing enterprise environment","85251094","https://www.seek.co.nz/job/85251094?ref=recom-homepage&pos=2#sol=da9a7309712890f259817f9b7cf30a76a7a3d901","True","Integration Lead | Enterprise Tech & iPaaS Transformation We’re on the hunt for an experienced Integration Lead to spearhead a suite of major transformation initiatives across a growing enterprise environment. This is your chance to take the reins on strategic, technical, and hands-on work as the company migrates from legacy systems to a modern iPaaS ecosystem. You’ll be instrumental in leading mission-critical projects including a new loyalty platform, a headless website rebuild, and a distribution centre overhaul. The ideal candidate will thrive in a fast-moving, delivery-focused team, combining architecture and leadership with sleeves-rolled-up integration execution. What You’ll Do Lead the end-to-end migration from legacy integration platforms to a modern iPaaS (e.g. Workato, Boomi, or Mulesoft) Provide technical direction and mentorship to integration engineers and adjacent teams Collaborate on architectural design, technical decision-making, and platform optimisation Work closely with stakeholders across digital, fulfilment, and product teams to ensure robust and scalable integration strategies Bring structure to an evolving environment by establishing integration patterns, frameworks, and tooling Play a hands-on role in implementing integrations, with oversight of both tactical delivery and long-term roadmap What We’re Looking For Strong experience with integration technologies and iPaaS platforms (Workato, Mulesoft, Boomi, etc.) Solid architectural background with a track record of designing scalable, enterprise-grade integration solutions Familiarity with Kafka or Confluent Cloud is a major advantage Able to lead cross-functional technical teams and coach developers transitioning into integration work Confident communicator, comfortable working with engineering, product, and business stakeholders Background in retail, e-commerce, or logistics beneficial but not essential Hands-on mindset with a pragmatic approach to delivery Why This Role? Large-scale, high-impact projects in flight Strategic influence with hands-on technical engagement Work within a modern architecture and cloud-native tech stack Interview process that moves quickly—decision-makers are engaged and ready"
3,"Software Engineer","DataMesh","Location not found","Work with cutting-edge tech and tools","85522484","https://www.seek.co.nz/job/85522484?ref=recom-homepage&pos=3#sol=9ab4f77223e810cd217ee62662ae0c1db8a28f78","True","Our Story! DataMesh Group is revolutionising the payment systems available to merchants and retailers, delivering integrated payment capabilities and valuable customer insights through bespoke payment and data processing solutions. The company is in an exciting growth phase, and we are looking for individuals with the appetite and energy to make impactful changes and accelerate their career development with this unique opportunity! Smarter connections, infinite possibilities! About the role Are you passionate about crafting innovative solutions and driving technical excellence? We're seeking a Software Engineer to join our dynamic engineering team. In this role, you'll play a pivotal part in developing enterprise-grade software, fostering team collaboration, mentoring peers, and delivering impactful results that align with business objectives. Sound like you? Keep reading! What you’ll be doing Producing quality code, raising the bar for the whole team on quality and speed. Interpreting business requirements and ensuring technical requirements are an accurate reflection. Estimating complexity of implementation tasks. Writing code and unit tests, building prototypes, solving issues, profiling, and analysing bottlenecks. Designing performant, robust APIs. Managing and optimising scalable distributed systems on the cloud. Optimising applications for performance and scalability. Developing automated tests to ensure business needs are met and allow for regression testing. Regularly communicating with stakeholders, project managers, quality assurance teams and other developers regarding progress on the long-term technology roadmap. Creating and maintaining workflows with teams to provide visibility and to ensure workload balance for consistent visual designs. What we’re looking for A degree in Computer Science (or equivalent experience) Strong foundations in computer science, algorithms, and application design Experience building secure, high-performance applications Proficiency with AWS or Azure and experience with cloud-native development Expertise in designing RESTful APIs and developing with the MERN stack Experience with Golang, Kubernetes, and Docker A collaborative mindset paired with the ability to work independently on mission-critical projects Curiosity, a growth mindset, and a knack for challenging the status quo Why You’ll Love Working with Us Innovation at Its Core: Be part of a company that’s shaping the future of tech with cutting-edge solutions Collaborative Culture: Work with smart, driven, and supportive teammates who value your input Career Growth: Enjoy a clear path for growth and development in an expanding organisation Impactful Work: See the results of your efforts as you help bring transformative projects to life Flexibility and Balance: We are a hybrid workplace with flexible hours and work-from home options Social Perks: Enjoy company events, including lunches, celebrations and after work drinks! Ready to Join Us? If you’re passionate about building groundbreaking software and want to be part of a mission-driven team pushing boundaries in payments innovation, we’d love to hear from you. Apply today and let’s build something incredible together!"
4,"Full Stack Wordpress and React Developer - Freelance","Enterprise Marketing Pty Ltd","Location not found","3d ago","85448353","https://www.seek.co.nz/job/85448353?ref=recom-homepage&pos=4#sol=49506c13eeadf81f7fdd0fbef77dde05d118f714","True","Full Stack WordPress & React DeveloperOngoing Freelance WorkWarriewood NSWFreelance work for a suitably qualified senior developer who is expert in WordPress and React. This will suit a developer who already has regular work but is looking to supplement that with another income stream.You must have recent agency experience and be able to demonstrate a range of WordPress and React website builds, especially ones that you have been solely responsible for. While you will be able to work remotely most of the time, there will be occasions when you will be required to attend our office in Warriewood, NSW. As a result we will only consider applicants who are within commuting distance of our office.The work will be ad-hoc projects and maintenance for exiting sites with the prospect of working on new builds and larger projects as they arise. Requirements:The right to work in Australia Excellent oral and written English language skills.Minimum 6 years commercial experience, agency experience preferred.Extensive experience as a Full Stack Web Developer, focussed on WordPress and PHP.Expert in front-end technologies including HTML, CSS, JavaScript and React.Strong WordPress development experience including the ability to create custom templates and themesProficiency in integrating and building APIs, REST APIs, Graph QL, 3rd Party APIs.Broad understanding of the WordPress ecosystem including themes, plugins and page builders. Skilled using code management and version control systems such as BitBucket and Git.Strong database skills, focussed on MySQL.Solid understanding of website usability, accessibility, performance, and security.Basic knowledge of Linux server administration, including Apache and nginx.Basic knowledge of cloud infrastructure and hosting, including cPanel and AWS.Strong documentation skills for both internal teams and clientsAble to accurately estimate development time from specifications providedAble to demonstrate accuracy and reliability in their workPersonable, collaborative and a team player· Answer emails and questions promptly, share updates, and ask questions when necessary· Keep clear and detailed documentation for future devs· Take initiative on your work. We value someone who can understand project goals and develop their own solutions to hit the target rather than someone who needs every task scoped out for themWrite clear, human readable code with commentsWork within budget constraints The workOngoing maintenance of existing client websites plus the development of new ones.Ongoing development of existing web applications,Ongoing maintenance and development of integration between systemsDevelop, test and deliver unique new websites using the latest techDevelop custom web applications using PHP.Collaborate with our design and production team to implement effective solutions and provide recommendations for development processesReports to our Digital Lead Remuneration will be negotiated based on demonstrable experience and skills. Applicants must provide examples of previous development work, particularly involving React and explain their contribution to those examples. Links to websites or an online portfolio would be ideal.About usEstablished in 1981, we are a full-service agency that works across a wide range of industries, especially tourism. Clients include iconic world-class brands, local businesses, and global leaders. We have an enthusiastic team that produces excellent work from very pleasant offices. There’s more information on our website at https://www.em.com.au/.If this role interests you, please apply now with your CV and a covering letter explaining how you meet our criteria, at: <EMAIL>"
5,"Head of IT","Codex Consulting","Location not found","Hybrid & Flexible Work: Enjoy a balanced lifestyle","85222290","https://www.seek.co.nz/job/85222290?ref=recom-homepage&pos=5#sol=cf80d61af3d0013b840d6e1fc078c3835994cfe4","True","Shape the digital future of a critical services business operating across Australia and New Zealand.This is a newly created, executive-level role at the heart of a national refrigeration and air conditioning organisation. With a growing footprint across ANZ and a commitment to modernising operations, the company is seeking a strategic Head of IT.You’ll report directly to the CEO and take ownership of the technology function — setting vision, building capability, and delivering real, lasting impact. Whether or not you've held a formal Head of IT or CTO title before, this role is ideal for a senior technologist ready to step up into full-spectrum leadership. Structured onboarding support is in place to set you up for success.What You’ll DoDefine and lead the organisation’s technology strategy, in alignment with commercial and operational priorities.Build and manage a cross-functional tech team of ~10, focused on delivery, support, and innovation.Partner with executive leadership to scope and deliver transformation projects across finance, supply chain, and field operations.Ensure scalability, security, and performance across all platforms and integrations.Promote a culture of continuous improvement, agility, and user-focused delivery.What You Bring5+ years of experience in senior technology leadership roles, ideally within operationally complex environments.Proven track record in developing and delivering technology strategies aligned with business outcomes.Strength in building, mentoring, and leading high-performing teams.Strong commercial acumen and the ability to influence at C-suite level.Hands-on approach to delivery, balanced with strategic oversight.Ready to drive real digital change in a sector that keeps industries moving?Apply now to lead the next phase of technology growth across Australia and New Zealand."
7,"Android Developer","Ethan Talent","Location not found","4d ago","85414049","https://www.seek.co.nz/job/85414049?ref=recom-homepage&pos=7#sol=78c6ace91c0e9183148727698056f0e7ff63f771","True","We are currently looking for an experienced Senior Android Developer to join the mobile team and take a lead role in designing, developing, and maintaining cutting-edge Android applications. The ideal candidate will be deeply familiar with core Android development, library creation and distribution, and have practical experience or strong interest in cross-platform technologies and modern app development practices. Experience 5+ years in a Android or IOS Development role. A Bachelor's degree or equivalent in Computer Science, Software Engineering, or a related field. Prior experience in Android application design and development using Kotlin and Java. Deep understanding of the Android ecosystem, modern architectural patterns (MVVM, MVI, Clean Architecture), and the entire mobile development lifecycle. Strong proficiency in Kotlin, including an expert understanding of Coroutines and Flow for asynchronous programming. Demonstrated experience building modern, declarative UIs with Jetpack Compose. Proficiency in the Java language within the context of legacy Android codebases. Experience in application performance fine-tuning, scalability, and memory management. The ability to write clean, efficient, testable code and adapt to new technologies as needed. Strong understanding of RESTful APIs and mobile client-server architecture. Familiarity with local data persistence using libraries like Room or SQLite. Demonstrated experience with managing app releases on the Google Play Store. Proven experience with CI/CD pipelines for mobile applications (e.g., Jenkins, GitLab CI, Bitrise, Azure DevOps etc). Effectively assess and prioritize change requests based on impact, risk, and feasibility. If interested please initially email over resume"
8,"Website Developer","Brilliant Logic","Location not found","17h ago","85539950","https://www.seek.co.nz/job/85539950?ref=recom-homepage&pos=9#sol=aad56a1e1cb51749ff4eb25ef7abbbb8b6b70922","True","Location | Sydney – Central Coast NSW/ HybridHours | Permanent Full-Time Benefits Work on a wide variety of digital projects in the field of website development & maintenanceMeaningful work. We’re proud to work with not-for-profits, First Nations communities, and values-driven organisationsWork from our Central Coast office, Sydney hub, with Hybrid optionWe're looking for a talented and reliable Web Developer to join our team. You’ll work closely with designers, Account Managers to build and maintain beautiful, functional websites – from slick one-pagers to complex, multi-page websites for our diverse client portfolio. If you're passionate about clean code, intuitive UX, responsive design, and want to work somewhere your ideas are heard and your work has impact, we’d love to hear from you.About UsWhen you work for Brilliant Logic, you get to work with a tight-knit group of creatives, dedicated to delivering marketing outcomes for our multi-faceted client portfolio. We are a boutique marketing agency that is proud to offer a work culture where you can work on national accounts, from a base on the beautiful Central Coast of NSW or from our Sydney office in Woolloomooloo.As a reputable marketing, social media and design agency, we are looking for our next knowledgeable and experienced Website Developer.About the RoleOur Website Developer is the glue within in our busy agency office working on WordPress as our preferred CMS. Working across our client portfolio, this role provides a wonderful opportunity to work on new and existing websites. Our Developer performs predominately web development and CMS update services as well as general IT support.Exposure to working with Gutenberg and Elementor in addition to classically built websites on WordPress is essential. Our Website Developer must be able to build from the ground up (not just work with templates).This role would require experience or knowledge of the following:New Wordpress Website BuildsExcellent knowledge of WordPress / PHPProficient in HTML5, CSS 3, SASS, Vanilla JavaScript and jQuery.Working knowledge of Astra, Bootstrap and/or Foundation frameworksProficient in relational databases such as MySQLKnowledgeof the WordPress database structureDevelop sites from scratch, from basic landing pages to more complex sites incorporating third-part REST APIsBuild sites from Figma designsProficient in building and integrating E-commerce sites with WooCommerceResponsive design & developmentDe-bugging, Cross-browser & device testingFamiliarity with terminal and basic Linux commandsAbility to quote hours required for particular jobsExperience with Git (basic commands)Experience with Gravity FormsWordpress Website ManagementTaking on a large array of different Wordpress buildsAbility to understand sites with minimal handover and documentationPropose and execute website upgrades within existing buildsOn-going website maintenance and optimisationSSL Installation and setupDNS Management skillsAbility to problem solve web security issues particularly with WordPress websitesFixing and applying security patches and prevention measuresServer administration (cPanel / WHM Hosting Management / Linux) OtherSEO OptimisationExperience with Google Suite (Analytics, Search Console, Tag Manager, Recaptcha, DataLooker & Maps)EDMsKnowledge of Adobe Photoshop, Illustrator is desired Candidate CriteriaAt least 1-2 years experience in similar rolesExperience with or exposure to working within a digital/creative agencyExcellent communication & personal skills and experience working directly with clientsAble to articulate best course of action for technical tasks and time needed to complete themPunctual, able to work to deadlines & multitask on multiple projectsThe confidence to work autonomously within a small team environment“Can do” work ethicWhat makes our workplace different?Rewarding work culture with a highly supportive and experienced teamRDO on birthdaysFlexible family and work conditionsWork for a company that likes to celebrate the wins with events and dinners East Gosford or Woolloomooloo location, work from home hybrid How to apply:Send your CV and cover letter addressed to Operations Manager Kim <NAME_EMAIL>. Please note: We kindly ask that you do not call regarding this role. We’ll be in touch directly with shortlisted applicants.Only shortlisted candidates will be contacted. All candidates must be Australian residents or have permanent rights to work in Australia. Our offices are situated on Darkinjung and Gadigal lands and we acknowledge the Traditional Custodians of this land. We pay our respects to the Elders, past, present and future, and recognise their continuing connection and contribution to this land."
9,"Software Engineer","Energetica","Location not found","career development","84971201","https://www.seek.co.nz/job/84971201?ref=recom-homepage&pos=10#sol=fa663058d1c5d4ff711419017ed35897f63e9757","True","Great to meet you. We're Energetica, an end-to-end platform for everything to do with enterprise energy and renewables. Buying, selling, analysing, optimising, working towards net zero… you name it. We do it all, and we do it for some of Australia's biggest names, too!We're a small (but rapidly growing) team, hyper-focused on building up and scaling our technology both here and globally, and we want smart, focused and hardworking people to join us as we head towards the next stage of our evolution.Built and managed here in Australia, we are leading the way in technology for the energy and sustainability sector and looking for like-minded individuals who want to roll their sleeves up and get in at the ground floor of an exciting journey.Our engineering smarts are what makes us different, and it's critical that every member of our engineering team not only loves what they do, but are bloody brilliant at it.The purpose of this role is to help us keep our platform at the forefront of the market by building enhancements and supporting our clients.For this position, we want you to convince us that you'll be fantastic at the following:Understanding customer needs and requirements quickly and coming up with clever ways to address them while pushing the system’s overall capabilities forward.Building and supporting software with a modern, distributed architecture across multiple customers/tenants.Picking up complex new concepts rapidly.Testing new stuff that doesn’t yet quite work yet and working with us on improving it.Staying positive and productive around stress and tight deadlines which are unavoidable in a rapidly growing company.Providing feedback and discussing solutions in a constructive and collaborative way.As we're a small startup, you will be open to:Getting involved in a little bit of everything: from customer support, to testing the system, and all the way through to energy analysisWorking hard, but also smartHelping us plan and execute our product visionWorking closely with all levels of the team, from the founder onwardsYou'll need to have at least some of the following experience:Real production system development on any stack, ideally using a type-safe language (full disclosure, we mainly use a mix of TypeScript + React on the frontend, and Kotlin/Java on the backend)Familiarity with computer science concepts that are relevant to everyday software construction… but we don't require you to be:An energy expert – (fantastic if you are, but we're also more than happy to train you up!)A rocket scientist, or a PhD in quantum physics (but we won't hold it against you)We offer the following:On-site or remote work anywhere within 3-4 hours of flying time to Melbourne (however, if you’re actually located in Melbourne, this would be ideal as we are now in the office 2-3 days a week)The opportunity to work with some smart people and to be part of something very big.A great salary appropriate to your experience.If this all sounds like something you'd be interested in, tell us why and we'd be very happy to hear from you!"
10,"Graduate Software Engineer - Australian Citizen only","Powerstaff Consulting","Location not found","20h ago","85533389","https://www.seek.co.nz/job/85533389?ref=recom-homepage&pos=11#sol=73f21e03e8ce4243140fa2a95f73f7a0cab6c1b9","True","Only recent grads with less than 1 year commercial experience and are Australian Citizens will be considered for this role This innovative company with a start-up culture combine technical and commercial excellence leads the field in a range of network and wireless communications disciplines. They are looking for an Australian Citizen - Junior/Graduate Network Application Software Engineer with a Degree (honours) in Engineering or a relevant discipline. This position requires a combination of internet and VOIP protocols understanding as well as C/C++ software development. Very good written and verbal communication skills and the ability to become a respected member of our development team are also essential. The successful candidate will report to the Engineering Manager and have responsibility for developing software for their range of soft switching technology for the defence, public safety, utilities and mining industries. You must be an Australian Citizen and be prepared to work the first 3 months in the office (WFH permissible once up to speed). This is a permanent role with office location near Central station. Skills / knowledge required: Development of C/C++ software applications under the Windows/Linux OS Knowledge of networking protocols such as IP, TCP, UDP and SNDCP Knowledge of VOIP (SIP/RTP) protocols Experience of scripting in bash, python and/or perl Some knowledge of SQL Knowledge of the full software development life cycle Ability to produce documentation including software requirements, software design and software test plans Again, please do not apply for this role if you are not an Australian Citizen"
11,"Senior Frontend Engineer","Black Nova VC","Location not found","6d ago","85303104","https://www.seek.co.nz/job/85303104?ref=recom-homepage&pos=12#sol=4ab3c83eb8fe6ad840bb5c32f3db2308bd395dcb","True","Senior Frontend Engineer - React Native & Mobile- Greenfield build of B2B SaaS InsureTech Platform - Sydney: Hybrid Working- $170-180k + Super- Applicants must have full, unrestricted working rights in AustraliaBlack Nova is representing one of our portfolio companies Lodgicl.Company Lodgicl connects insurers and their suppliers through structured data and intelligent workflows, starting with assessments and building towards a unified claims execution layer.How you can make an Impact This is a full-time on-site role for a Senior Software Engineer - Frontend, located in the Greater Sydney Area. As a key technical leader in our growing startup, you'll work directly alongside our founders to architect and build innovative mobile solutions from the ground up. You'll take ownership of critical front-end systems and drive technical decisions that shape our product direction. Daily responsibilities include hands-on development of mobile applications using React Native, implementing robust and scalable user interfaces, and translating business requirements into technical implementations. You'll play a pivotal role in establishing engineering best practices, building our technical foundation, and helping scale the team as we grow. This role offers the unique opportunity to have a significant impact in a fast-paced startup environment where your contributions directly influence company success. Key responsibilities include: Stay up-to-date with the latest trends and technologies in mobile development, particularly in the React Native ecosystem Contribute to improving our development processes and best practices Integrate with backend APIs and third-party services Work with native modules when necessary Qualifications Required Proven experience building clean, maintainable, enterprise-grade software solutions Minimum 5 years' experience developing mobile applications with React Native, and mobile app release management Strong understanding of JavaScript, including ES6+ syntax and asynchronous programming Solid understanding of React fundamentals and component lifecycle Experience with native mobile development (iOS or Android) Experience with testing frameworks like Jest or Detox Experience with Git version control system Bachelor's degree in computer science, Software Engineering, or related field Strong communication skills with eagerness to interact directly with customers and founders to deliver solutions Excellent problem-solving and debugging skills Experience with performance optimisation techniques for mobile applications Proven ability to work collaboratively in a team environment Ability to help onboard and mentor junior developers"
12,"Customer Support Analyst","Vela APX","Location not found","Flexible and supportive team","85539594","https://www.seek.co.nz/job/85539594?ref=recom-homepage&pos=13#sol=81cfa115bbc226582352e288753e84f4794acf99","True","About Us:We are a leading and proprietary software company that delivers a comprehensive ERP solution to many iconic fashion and footwear brands. Our customer-focussed business is dedicated to ensuring that synergy between our customer's essential business processes together with their use of our application maximizes their operational efficiency to drive their bottom-line. Our customer-centric approach and commitment to excellent service has our Support team delivering level 2 and 3 type support to clients as they navigate and optimize the usage of our ERP.Is this the Role for YOU?:We are excited to offer a great opportunity to someone looking for an interesting and long-term career in customer support with a successful and growing Australian company. From point of sale, warehousing, production and finance, our vast system can be challenging to learn, but we set you up for success from the beginning, with resources, in-team training, opportunities to work with senior team members, one on one catchups, and a supportive team and manager. We provide you with the opportunity for ongoing training and access to resources to help you create your own path within our business.Our company values of having Integrity, being Adaptive and being Collaborative are the foundation for how our support team operates. We are looking for candidates who share these values and will model them in their daily work life.Our Customer Support Analysts serve as key members of the team, providing frontline technical assistance to end-users of our system. If you excel at problem-solving, technical troubleshooting, are a critical thinker and detail-oriented, then our Support Analyst role focussed on working with customers to resolve application related issues, has your name all over it.As part of the team your day-to-day work will handle support tickets, manage customer inquiries, and collaborate with internal teams to resolve often complex problems and ensure a high-quality of service delivery. You will also assist in testing, documenting issues, and providing feedback for continuous system improvement.Day to Day Nuts and Bolts:Provide technical support: Respond to customer queries related to the ERP system via ticket, email, and phone, ensuring timely resolution of issues.Troubleshoot issues: Diagnose problems by understanding processes, analysing data, replicating issues and at times coordinating with other teams to ensure resolution within our agreed service levels (SLA).Issue tracking and documentation: Log customer interactions and technical issues in our ticketing system, ensuring proper documentation of incidents and resolutions and accurate maintenance of timesheets.Collaboration: Work within our Support swarms and escalate and collaborate with cross-functional teams (development, testing, consulting, CRM) and our 3rd party integration partners where necessary, to resolve complex issues of both a technical and business nature.Addons undertaken by the team include:Upgrades: Working with our customers to organise and deploy version upgrades of their test and live systemsSecurity Compliance: Our ISO27001 certification means we have a strong emphasis on keeping both our own and our client's systems secure. From time to time, we are called on to participate in activating changes that keeps our cybersecurity future-focussed for all our stakeholders.Testing: Participate in system testing to validate fixes, or updates before deployment to customers.Continuous improvement: Provide feedback on recurring customer issues and help improve internal support processes or system updates to prevent future problems.Qualifications:Experience: Minimum 3 years of experience in customer support, ideally for an ERP system or other complex software platforms.Technical knowledge: Familiarity with ERP systems, Oracle databases, and technical troubleshooting.Communication skills: Strong verbal and written communication skills, with the ability to explain technical concepts to non-technical users.Problem-solving: Ability to work independently and collaboratively to analyse and resolve technical problems.Customer-focused: Demonstrated ability to maintain professionalism, patience, and a positive attitude while handling challenging customer issues.Time management: Ability to prioritize and manage multiple support tickets, follow up and track progress efficiently.Education: Bachelor's degree in information technology, or related field, or equivalent work experience will be favourably considered.Preferred Skills:Familiarity with ERP solutions and supporting customers in SaaS environments.Experience with time-sheeting and ticketing systems.Knowledge of Oracle SQLBasic understanding of accounting, finance, or supply chain managementWhat we offer:We would be welcoming you to a supportive and collaborative team that works together in a dynamic and adaptive way. We offer a competitive market-related salary and ongoing training and professional development with career advancement.How to Apply: Interested candidates should submit their resume and a cover letter detailing their relevant experience!"
13,"Mid to Senior Software Engineer (Web)","VAPAR","Location not found","8h ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=14#sol=ef969bd0deda00f8cc227c181fc50dda5ecdc87a","True","Join one of the fastest growing tech sectors – Artificial Intelligence!Make a huge impact in an burgeoning tech industry. VAPAR is a growing company which supports fully remote working! Work from anywhere in Australia. We also have a co-location space in Sydney.At VAPAR, we’re creating a smarter way to keep underground pipes in operation, longer. Piped infrastructure around the world needs to be maintained and repaired in a way that removes the need for long, manual and fatiguing tasks. Currently, there are people who watch hours of video footage of pipe inspections to find issues and have outdated processes to manage their data. We’re the team that’s changing this.We turn visual data about every-day pipes directly into condition scores so maintenance and repairs happen faster, cheaper and more accurately.We’re looking for someone mid to senior level who is comfortable taking ownership of their role. In this role, you’ll be working within our tech team on the backend product, creating scalable and performant code. As the team grows, you’ll have the opportunity to grow with a great team, and we’ll support you with the required training to do so. We want you to become a pillar of the team and have a big impact on what we’re building at VAPAR.As a Software Engineer at Vapar, you'll have the opportunity contribute to the development of AI-powered solutions that tackle the complex problems associated with pipe and sewer maintenance. Our company culture thrives on innovation, teamwork, respect and a relentless pursuit of excellence. While enjoying the flexibility of a fully remote position within Australia, you'll collaborate with a diverse team of professionals who share your enthusiasm for cutting-edge technology and its potential to shape a better future for infrastructure management.Day in the lifeWe are seeking an experienced and motivated Software Engineer with a minimum of 4 years of Python experience to join our development team. The ideal candidate should be well-versed in software development principles, possess a strong foundation in Python programming, and demonstrate a proven track record in designing and implementing complex software systems. In addition to these skills, the right candidate should have a good understanding of Document based NoSQL solutions and RESTful APIs, relational databases is a bonus. As a Software Engineer at VAPAR, you'll collaborate with the team to deliver high-quality software solutions that meet customer needs and drive company success.In this role, you'll…Develop, test, and maintain software applications and systems following industry best practices and coding standards.Collaborate with product managers, designers, and other engineers to translate product requirements into well-structured, efficient code.Apply your expertise with Azure to develop against cloud-based services.Develop and integrate RESTful APIs for seamless communication between different software components.Participate in code reviews to ensure code quality, performance, and adherence to coding standards.Identify and troubleshoot software defects and provide timely solutions to uphold product integrity.Contribute to the architecture, design, and implementation of new features and enhancements.Create and maintain technical documentation, including specifications, design documents, and user guides.Stay updated with emerging technologies and trends in software development to drive continuous improvement.Must-have skillsOn day one we'll expect you to have…Bachelor's degree in Computer Science, Software Engineering, or a related field (or equivalent work experience).4 years of professional experience in Python software development.Solid understanding of software development principles, async functionality, algorithms, and data structures.Familiarity with industry standard software development methodologies, such as Agile or Scrum.Ability to analyse and solve complex technical problems and work independently in a remote setting.Excellent communication skills, both written and verbal.Experience with Azure and/or AWS cloud-based offerings.Proficiency in working with and designing RESTful APIs (FastAPI).Selling pointsAnnual salary: $115k to $135k + 12% superFlexible working hours with ability to work remotelyWork with a driven, fun and switched on team that likes to raise the bar in all that we do.Genuine career growth opportunities as we continue to expand!More about VAPARWebsite: CCTV Pipe Inspection Software Powered with AI - https://vapar.co/LinkedIn: https://www.linkedin.com/company/vaparVAPAR was founded by Amanda Siqueira and Michelle Aguilar in 2018. As a Civil Engineer, Amanda had seen enough of the inefficiencies in the pipe condition assessment process, and teamed up with Michelle (a Mechatronic Engineer with Intelligent Systems experience) to build technology to enable pipe asset managers to revolutionise their way of working. Since then, the VAPAR team has grown and now has clients in Australia, the UK and New Zealand and continues to scale."
15,"Commercial Analyst","efm Logistics Pty Ltd","Location not found","A dynamic and empowering culture; where we challenge each other to do it better","85286209","https://www.seek.co.nz/job/85286209?ref=recom-homepage&pos=16#sol=b0f36849e09686d3f5280f91cceb870fa244e232","True","At the heart of our service offering are our people and our promise to deliver a no better logistics experience. efm Logistics is the largest 4PL provider in Australia & NZ, meaning we do much more than just ship goods. We design, build, manage and provide integrated, optimised supply chain solutions.As an independent service provider, we create tailored transport and warehousing solutions using a single technology platform and a dedicated Account Management team. Backed by 200+ industry experts, our people, technology, and innovation set us above from the competition.We are looking for a Commercial Analyst who will discover, deliver, and embed commercial initiatives across the business. The role is analytically based, with a focus on continuously identifying and executing value-add projects. Partnering with a variety of different departments, it is a fast-paced and continuously changing role where you can truly see the outcome of what you do.RequirementsOn a daily basis you will: Extract, understand and manipulate data from various sources Convert data into insights through analytics and modelling, Work closely with key stakeholders to design operationally and commercially viable solutions for our customers and to support improvements to the business Work through and present analytical projects with tangible financial impacts Identify, implement, and monitor risks and action items required to realise the commercial benefit from your projects Partner with the Procurement function to understand market conditions and support negotiations to achieve required outcomes Present to the internal and external stakeholders on various commercial initiatives and projects on an ongoing basis Complete general system configurations and logic, extracting and manipulating data for your own analytical needs Your skills and attributes Someone curious, analytical, detail oriented, insightful and influential Bachelor degree in a Commerce, Business Information Systems or Supply Chain related field Capacity to produce high quality reports and presentations for technical and non-technical audiences Strong communication and stakeholder management skills Proficiency in MS Office applications, Power Point, Outlook, Word, Excel Ability to complete advanced financial modelling and data analysis in Excel Project management approach to task advantageous Previous experience in an analytical role advantageous BenefitsWhy should you join efm Logistics?Strong focus on regular and ongoing professional development training, enhancing skills and career progression A dynamic and empowering culture, where we challenge each other to do it better An opportunity to be part of an industry leader Competitive salary and bonus scheme Modern office space and technology, located within Moorabbin’s ever evolving Morris Moor complex Free parking and onsite café Company functions and team building activities Partner with 2 charities, volunteering time to make a difference and contribute to the positive impact they are having both locally and internationally"
16,"Senior Full Stack Engineer","Launch Recruitment Pty Ltd","Location not found","Work for a leading provider in their field","85529385","https://www.seek.co.nz/job/85529385?ref=recom-homepage&pos=17#sol=7c478c0e166efd9314b962e39b61619aba22d239","True","About the role Work with a leading global organisation who have been in the market for over 100 years. This is an exciting opportunity to join a leading engineering team in Australia and work on building and transforming the way they service their customers. They have a real-tech engineering focus where you will have the chance to experiment and work on new challenges as you grow within this role. What will you be doing? Building on and uplifting the current technology roadmap Work in a team and autonomously to write code, develop and deliver new technologies Always strive for excellence and continuous improvement – how can things be done better? Support testing phases Maintain and improve the user experience across all frontend What are we looking for? Strong demonstrated frontend experience with ReactJS or Full Stack with Next/React Strong backend API and microservices experience Good CI/CD and DevOps experience Growth mindset – somebody eager to learn and keen to develop and grow Excellent communication skills Benefits This is an initial 6-month contract to permanent opportunity ASAP Start Hybrid working (3 days in office) with a truly supported flexible working environment Highly collaborative team How to Apply You must have full working rights in Australia and reside in Sydney. If this sounds like you please click apply today!"
17,"Senior Software Engineer - secure web systems","MCS Consulting","Location not found","4d ago","85407616","https://www.seek.co.nz/job/85407616?ref=recom-homepage&pos=18#sol=28b4984e110037ad03f8f6c296b29ccec6d3e979","True","Create sophisticated platforms, SDKs and developer tools for a company that develops high-performance, mission-critical security systems that underpin vast quantities of commercial traffic.You will be working on systems that are used by major commercial developers worldwide, so the ability to write well-architected code with well-designed interfaces is paramount.· Expertise in one or more of C/C++/ObjC/Java· Strong understanding of Computer Science principles· Experience with multithreaded and concurrent systems· Experience with native development for mobile (e.g. Android NDK)· Javascript/web technology/Swift/Kotlin a bonus· 5-10 years of development experience· Systems-level mobile developmentWe are looking for talented system-level developers who can make a strong contribution to the team and produce excellent quality code for use in large scale, low latency customer systems.MCS Consulting has been a trusted Recruitment advocate for over 25 years to many companies in high value Manufacturing, Finance, R&D, IT&T and Government.We have an effective personal and honest approach that really makes the difference. Kevin Moore ; 02-9481 8888 job KM: Monticello. ********* Please send your resume ASAP A WORD CV IS ESSENTIAL ! A Suburb/Address helps us locate you for roles close to home and often unlocks those hidden jobs. www.mcs-consulting.com.au We are more than happy to discuss career aspirations. At MCS we are here to help, not just recruit"
19,"Mid-Level Full Stack Engineer (Frontend Leaning)","The Onset","Location not found","WFH 3 days; 2 days in office - Sydney Based","85540955","https://www.seek.co.nz/job/85540955?ref=recom-homepage&pos=20#sol=48a3b174c08096e30d804b3dc14aa0f00e9882ce","True","Workflow Automation Platform | React, Node.js, GolangSydney | Up to $130K + super | Hybrid (2 days in-office) Imagine building the infrastructure that powers how work gets done, not just for one company, but for dozens of global enterprises. That’s the opportunity here. You’d be joining a business with serious backing from Australia’s top investors is scaling fast, having already secured long-term contracts with enterprise clients and started expanding into the US. Their platform helps businesses streamline and automate their workflows and it’s at the heart of how their customers operate. As the platform expands in scale and complexity, we’re hiring a frontend-leaning full stack engineer to help evolve our product’s UI and contribute across the stack as needed. You’ll join a tight-knit founding team in Australia and work directly with the co-founders to design, architect, and improve the platform. There’s plenty of tech debt to tackle (and room to shape things the right way), so your impact will be immediate and meaningful as the company grows. What’s in it for you? High ownership in a scaling, product-first companySolve real UI/UX challenges - including form-heavy workflows, legacy UI refactors, and a new design system rolloutWork across the stack (React/Node/Go) with mentorship and the chance to upskill in backend developmentBe trusted with ownership and end-to-end delivery, not just ticketsLearn and grow alongside senior engineers in a high-autonomy environmentJoin a product-first company where engineers are involved in decisions earlyWork in a flexible hybrid model - two days a week in the office, with the rest remote About you2 - 4 years of experience as a software engineer with a strong focus on React and TypeScriptComfortable working across the stack, or keen to grow backend skills (Node.js, Go)Exposure to or interest in design systems and UI standardisationExperience working in a cross-functional product team with designers and product managersStrong communicator with a bias toward simple, maintainable solutionsCurious, proactive, and comfortable in a fast-moving startup environmentThe stackReact, TypeScript, Tailwind Node.js, GolangAWS, Docker, KubernetesSound like you? Apply now or message Indigo Munro on Linkedin if you’re keen to know more!"
20,"Full Stack Developer - Shopify","My Muscle Chef Pty Ltd","Location not found","Are you ready to join an industry leader?","85366455","https://www.seek.co.nz/job/85366455?ref=recom-homepage&pos=21#sol=9381cbf9a5b422c35eb88f4315421b95d235a471","True","My Muscle Chef (MYMC) is Australia’s premium fresh ready-made meal and functional food and beverage company. Based in Sydney NSW, we are growing quickly to extend our position as the online direct to consumer and retail industry leader in an industry that is experiencing explosive growth. Since 2013, we have experienced continuous growth & hold enormous ambition to remain an industry leader. We are seeking high energy individuals to join our entrepreneurial team and culture to help us unlock our next phase of hyper growth. MYMC Values We pride ourselves on a set of values that tangibly guide our behaviours and decisions, so we can grow the business and enjoy a world-class working experience. Find a Better Way. Walk in our Customers' Shoes. Everyday. Challenge Ideas and Champion Solutions. Integrity is Stronger than Muscle. Enjoy the Journey! This is a hands-on role suited to someone who understands the full web development lifecycle, from backend logic to frontend polish, with an emphasis on scalable, high-performance code and ecommerce experience. Tech Stack Shopify (Liquid, Shopify CLI, Plus APIs) MongoDB React + TypeScript (Next.js) Node.js + NestJS Azure / GCP Dynamics 365 (F&O) – desirable for integrations Key Responsibilities but not limited to: Develop, customise, and maintain Shopify themes and Liquid templates Build and integrate scalable features using Shopify APIs (REST and GraphQL) Support development and integrations with Shopify Plus Write clean, maintainable frontend code in JavaScript/TypeScript and React Collaborate with design and product teams to deliver pixel-perfect, user-friendly experiences Participate in code reviews, QA, and continuous improvement of dev processes Work with our ERP and subscription platforms to support backend requirements and data flows Debug, troubleshoot, and optimise site performance across devices Skills & Experience: 4+ years of professional web development experience Strong Shopify Plus development experience within a multi-brand environment Solid JavaScript/TypeScript skills with experience in React Familiarity with headless Shopify or custom storefronts, ecommerce architecture, and subscription models Experience working with REST and GraphQL APIs within Shopify Ability to troubleshoot and independently debug frontend and backend issues ​​​​​​​Preferred Skills Previous experience with Shopify B2B offering or Shopify ecosystem tools (like Hydrogen, Oxygen, or Checkout Extensibility) Exposure to MongoDB or NoSQL databases Experience working on complex integrations with Shopify (ERP, CRM, middleware) Understanding of SEO, site speed optimisation, and accessibility best practices ​​​​​​​What we offer you: Enjoy discounted meals & $1 meals and snacks on site A fully equipped gym on-site for employees A hybrid working environment to suit your lifestyle Be part of a diverse and values-led culture that backs bold ideas Work with a team that cares about building great stuff – no bloated processes here"
21,"Integrations and Applications Manager","Imaging Associates Group","Location not found","Lead our digital journey and help redesign a modern IT ecosystem","85432433","https://www.seek.co.nz/job/85432433?ref=recom-homepage&pos=22#sol=c545601368b0d50ec4bc5d09236744a33f7fc236","True","About Us:Imaging Associates is a doctor-led, highly respected and rapidly growing radiology business with practices across the Eastern suburbs of Melbourne, Gippsland, and Wagga Wagga. IA prides itself on providing cutting edge diagnostic imaging services with a focus on clinical and patient excellence.We have built a strong reputation for the high-quality imaging service we provide, along with our friendly and welcoming patient-centred approach. We also work closely with several professional sports teams as the preferred supplier for their athletes.Join Our Team as Integrations and Applications Manager!We are embarking on an IT transformation journey with exponential growth projected across the company which presents a unique opportunity to redesign our IT ecosystem to support and enable our expansion plans. To support our Head of Technology and Digital Transformation we are looking for a dynamic and experienced Integrations and Applications Manager to help deliver this transformation strategy. This brand-new, hands-on role will see you manage two key areas of transformation: Enterprise applications and Interoperability. Key focus of the role includes, but not limited to:Transformation Initiatives Redesigning our infrastructure, cyber security and application suites. Embark on a cyber security and application life cycle maturity journey. Build robust IT processes to better support the organization and planned growth. BAU Responsibilities Installing, testing and maintaining IAs suite of applications and the interoperability between them. Overseeing radiology workflow integrations between IA and external clients. Implementing best-practice application lifecycle management processes. Change management and tiered application support. Leveraging partnerships with vendors, service providers and client IT Teams. Managing a team of application analysts and integration analysts.This is a rare opportunity to play a key role in reshaping the future of our business! What We’re looking for: Computer science/Information technology engineering degree or equivalent technology certification(s) Extensive experience working with clinical software application suites (RIS/PACS/EMR or similar applications) and/or their integrations, with some experience working as a lead/manager. In depth knowledge of HL7/FHIR message formats and experience working with interface engines like mirth, rhapsody, Kailo or Epic bridges. Exposure to imaging technology and Dicom formats is desirable. Extensive knowledge in software application lifecycle, patching, upgrades, interoperability and maintenance. Demonstrated experience in self-management in terms of time, deliverables and project outcomes Demonstrated experience in clear and concise communication Strong knowledge of emerging technologies, digital trends and industry best practices. Proven people leadership capability and an ability to influence and present at executive/senior leadership level What’s on offer? Work within a highly collaborative environment with an engaged leadership group ready to scale up and build a digital platform to support organizational growth Flexible work location: Remote/Box Hill support office Highly competitive remuneration and benefitsWant to find out more?We can’t wait to read your application! Apply now or, for an initial and confidential discussion, please contact Celeste Peters - Senior People & Culture <NAME_EMAIL>."
23,"Senior Vlocity Software Developers","BLACKROC","Location not found","Design robust Salesforce Vlocity solutions (Apex; LWC; Omniscripts; Flexcards)","85359509","https://www.seek.co.nz/job/85359509?ref=recom-homepage&pos=24#sol=fdcd547a2037e03aa531a76406c1adc67bdb3d5a","True","Contract: 12 Months + 12-Month Extension Location: Sydney, Melbourne, Brisbane, Adelaide, Perth or Darwin Work Arrangement: 2 days WFH per week Citizenship Requirement: Australian Citizenship is required for this role. About the Role BLACKROC is working with a leading organisation on a high-impact transformation initiative and is seeking multiple Salesforce/Vlocity Developers to join their growing Agile delivery team. You’ll be working on enterprise-scale Salesforce solutions using cutting-edge Vlocity technologies and DevOps practices. Key Responsibilities Design and develop Salesforce Vlocity solutions, including Apex, LWC, Omniscripts, Flexcards, Integration Procedures, and DataRaptors Ensure code quality, reusability, and performance through adherence to governance and best practices Contribute to daily Agile ceremonies and collaborate across architecture, DevOps, and UI/UX teams Support the deployment pipeline through all environments including production Share knowledge and support capability uplift within the team Essential Skills & Experience Strong hands-on experience with Salesforce Vlocity (Omnistudio), Apex, LWC, Flexcards, and DataRaptors Skilled in Object-Oriented Programming, frameworks, and design patterns Integration experience with REST APIs and Platform Events Salesforce certifications: Platform Developer I & II, Omnistudio Developer Experience with GitLab and package-based development Desirable Skills Familiarity with Jira, VSCode, and modern DevOps practices Experience across multiple full-cycle Salesforce CRM implementations Accessibility-aware development practices Please note: This role is only open to candidates with Australian Citizenship due to security clearance requirements. Please click the 'Apply' button to submit your resume and cover letter. For more information please contact <NAME_EMAIL> At BLACKROC Recruitment, we are committed to fostering diversity, equity, and inclusion. We welcome applications from individuals of diverse backgrounds, including those with lived experiences. If you require any adjustments during the recruitment process, please don’t hesitate to reach out to the above-mentioned BLACKROC Consultant."
24,"Product Owner (EdTech SaaS)","Global IT Factory","Location not found","10d ago","85227886","https://www.seek.co.nz/job/85227886?ref=recom-homepage&pos=25#sol=e754089655d23e1d7c3998b422fc5b136af3d352","True","Global IT Factory is a rapidly growing Australian EdTech SaaS products company at the forefront of delivering cutting-edge IT solutions in the education technology space. Our mission is to empower educational organisations by enhancing student experience, acquisition, and retention through our agile and high-quality curriculum offerings.As our organisation scales, we're thrilled to announce an opening for a EdTech Product Owner to join our vibrant and innovative team. This role will work in a high-energy, cross-functional environment, guiding the design and development of our SaaS-based products catered towards Higher Education/Vocational Education and Training organisations.Our team members are provided with robust mentoring, insightful guidance, and advanced technical tools from our experienced local management team.Role Summary: As a Product Owner, you will own specific parts of our platform, becoming the expert in those feature sets and ensuring our products meet the needs of our clients. You will work closely with development teams, clients, and other product owners to create and execute product roadmaps that align with both business goals and customer needs. This role reports to The Product Manager.Key Responsibilities:· Write user stories and acceptance criteria for enhancements to the platform.· Engage with clients and understand market needs to create the backlog and roadmap for the parts of the product you own.· Become the resident subject matter expert in those feature sets that you own, and how those feature sets address client business needs.· Collaborate with other product owners in peer reviews of stories and share knowledge about your feature sets.· Communicate with development teams to ensure they understand client needs and the required enhancements.· Assist with release management by contributing to story sizing and backlog prioritisation.· Present product updates and roadmaps to clients and other interested stakeholders.Key Attributes & Skills:· A passion for continuous innovation and improvement.· Thought leadership in the space you own, with the ability to generate innovative and creative solutions.· Enthusiasm and pride in creating cutting-edge solutions in the edtech space.· Willingness to work flexibly to communicate with our offshore development team.· Ability to work well in a team, maintaining a positive and ‘can-do’ attitude.· Strong communication skills, with the ability to build relationships with both technical and non-technical teams.· Strong problem-solving skills and ability to think critically. · Able to critically analyse complex technical solutions.Requirements:· Bachelor’s degree in Computer Science, Engineering, or a relevant field.· Proven experience as a Product Owner or in a similar role within a SaaS software development environment.· Strong understanding of Agile development methodologies.· Excellent communication skills, with the ability to convey product vision and strategy to both technical and non-technical stakeholders.· Ability to analyse market trends and translate them into actionable product roadmaps.· Strong organisational skills with experience in backlog management and prioritisation.· Knowledge of the Higher Education, Vocational Education sectors and associated technologies would be a bonus.What’s in it for you:· Fully remote working, but with opportunities to socialise and work in person with the team throughout the year.· Rapidly growing company, with a strong reputation for providing best-in-class Curriculum Management Software to our expanding customer base.· Mentoring from senior leadership and opportunities for career growth.· A fun and engaging team - we take the work seriously, but don’t take ourselves too seriously.· Exciting work with cutting edge technology.Although we have a preference for Melbourne-based candidates, we wholeheartedly encourage applications from exceptional individuals across Australia.If this sounds like you, apply today! We will be reviewing and contacting suitable candidates as applications come in.Apply by sending us your Resume and a Cover Letter addressing why you are interested in this role, and how your skills and experience could contribute to Global IT Factory’s Product Team.If you would like further information about the role, please contact Nathalie <NAME_EMAIL> or on 0449 563 862.Global IT Factory is an equal opportunities employer and values diversity in the workplace."
25,"Technical Solutions Lead (Workato)","Lookahead","Location not found","Work from home and set your own hours","84863761","https://www.seek.co.nz/job/84863761?ref=recom-homepage&pos=26#sol=68350ddfb837533efa8230215319cb750b6502f8","True","The role We are seeking a highly skilled and driven Tech Solutions Lead to join this team and take the leading role in defining how this company integrates into their customers’ broader ecosystems. About you You will be responsible for establishing the technical processes, standards, and best practices that ensure integrations are secure, scalable, and maintainable over time. This includes driving engineering discipline across testing, documentation, release management, and ongoing support to ensure high-quality delivery and operational security excellence. Partnering closely with their Pre-Sales Engineer and Solutions Team, you will provide governance and oversight of integrations and the backlog across enterprise platforms such as Payroll, HRIS, and Finance systems. Success in this role requires a strong background in enterprise architecture and project delivery, as well as solid expertise with HTTP (e.g. APIs and security), s automation platforms (Workato). The offer Work from home Work with a supportive team and manager Be part of a company that has a high employee retention rate Compensation up to $$180k + super Find out moreIf you're interested in having a conversation about this role, send me an email with your resume, and let me know why you're interested in this position. We can then schedule a call to see if there's a fit.If this role isn't quite right, but you're looking for other developer roles, feel free to get in touch anyway. <NAME_EMAIL> | www.lookahead.com.au"
26,"Zoho Developer - Global Energy Tech Player - Salary Negotiable - WFH","Insight Resourcing","Location not found","5d ago","85355569","https://www.seek.co.nz/job/85355569?ref=recom-homepage&pos=27#sol=d1cd8c4190464b4c4d539c5dc33301e57885a424","True","1KOMMA5° is a global EnergyTech powerhouse revolutionising the way homes produce, store, and consume energy. With a bold mission to enable carbon-neutral living, we integrate cutting-edge solar, battery storage, EV charging and intelligent energy software into one seamless solution.Backed by top-tier global investment and now operating in over 10 countries, 1KOMMA5° is one of the fastest-scaling clean energy companies in the world.With the Australian operation now entering a period of rapid growth, the business requires an accomplished Zoho Developer to play a key role in the design, development, configuration and maintenance of our Zoho applications with a strong focus on Zoho CRM and Zoho Creator. The ideal candidate will have proven experience building automated workflows, integrating third-party applications using Deluge scripting, and working with webhooks for real-time data exchange.You’ll work closely with cross-functional teams to streamline business processes, automate workflows, and build custom solutions that enhance operational efficiency. This is a hands-on role requiring strong technical skills, a problem-solving mindset, and a passion for clean energy innovation. Key Responsibilities: Configure and customise Zoho CRM, including modules, layouts, automation (workflows, blueprints), and dashboards. Develop and maintain applications using Zoho Creator. Write efficient and clean Deluge scripts to create complex business logic and workflows. Integrate third-party applications using APIs and webhooks. Collaborate with product owner and cross-functional teams to gather requirements and translate business needs into technical solutions. Troubleshoot, debug, and optimise existing Zoho applications and automations. Document processes, workflows, and custom solutions. Stay up-to-date with Zoho platform updates and recommend improvements. Required Skills & Experience: Proven experience configuring and customising Zoho CRM (modules, workflows, blueprints, dashboards). Hands-on experience with Zoho Creator including form creation, reports, workflows, and scripting. Strong proficiency in Deluge scripting across both Zoho CRM and Creator to implement custom business logic. Experience working with APIs, webhooks, and integrating third-party applications into the Zoho ecosystem. Ability to understand complex business processes and translate them into scalable technical solutions. Excellent problem-solving skills, with a keen eye for detail and optimisation. Strong communication and collaboration skills, especially in cross-functional environments. Desirable (but not required): Experience with other Zoho One applications (e.g., Zoho Desk, Zoho Projects). Experience in a similar industry (e.g., energy, services, SaaS). Bonus: Experience with PHP for server-side scripting or integration use cases. Bonus: Experience with Zapier for integration use cases. Why Join 1KOMMA5°? Purpose-driven work: Help lead Australia’s clean energy transition and drive real climate impact Career growth: Be part of a fast-scaling global company with significant career growth opportunities Innovation at your fingertips: Work with proprietary energy technology and cutting-edge smart systems Supportive culture: Work alongside passionate, high-calibre colleagues in a values-driven team Global network: Access to a growing international team with opportunities to collaborate and learn With centrally located offices across Gold Coast, Sydney & Melbourne, this role can be based anywhere as the position can be predominantly WFH. Ideally however it would be preferred if the successful candidate could be in the office atleast once per week for internal meetings etc. Salary package is negotiable and commensurate with candidate skills and experience.Insight Resourcing are the exclusive recruitment partner to 1Komma5 Australia. For any questions, please (email only) <EMAIL> Insight Resourcingwww.insightresourcing.com.au"
27,"Implementation Consultant -6 months Contract","Millbrook Group","Location not found","3d ago","85462134","https://www.seek.co.nz/job/85462134?ref=recom-homepage&pos=28#sol=dbb70345c021487b2cbfb848a95a0b82374561ac","True","About Millbrook GroupMillbrook Group is a Melbourne-based credit fund manager with a proven track record of delivering attractive returns to investors and co-investing in diverse investment opportunities. With a genuine focus on building personal relationships, we consistently uphold our core values of trust, integrity, innovation, and teamwork. Operating for over 20 years, we specialize in providing exceptional investment returns to various investors across AustraliaRole OverviewWe are seeking a results-driven Implementation Consultant for a 6-month contract to support the successful rollout of our loan management and investor systems and business processes across our financial services organisation. You will act as the primary liaison between business stakeholders, internal IT teams, and third-party vendors to ensure high-quality, on-time, and compliant implementation of critical systems.Key Responsibilities Implementation & Delivery· Lead the end-to-end implementation of systems, applications, or business solutions.· Conduct configuration, data migration, UAT support, and go-live planning.· Collaborate with IT, operations, and vendor teams to deliver fit-for-purpose solutions.Stakeholder Engagement· Gather, document, and validate business requirements with key stakeholders.· Translate business needs into functional specifications and implementation plans.· Conduct training and knowledge transfer sessions with end users.Project & Risk Management· Manage project timelines, deliverables, and reporting under the direction of the Project Manager· Identify implementation risks and propose mitigating strategies.· Track post-implementation issues and support resolution within the contract period.Documentation & Process Improvement· Produce user guides, standard operating procedures (SOPs), and implementation documentation.· Recommend process enhancements based on system capabilities and business needs. Key Requirements· Proven experience as an Implementation Consultant, Business Analyst, or Systems Analyst· Demonstrated experience in system implementations within the financial services sector.· Strong understanding of data integrity, testing cycles, and change management.· Excellent stakeholder engagement, communication, and facilitation skills· Ability to work independently and within cross-functional teams under tight timelines.· Proficient with tools such as JIRA, Confluence, Excel, and financial platformsDesirable Qualifications· Degree in Information Systems, Business, Finance, or a related field· Experience with financial platforms· Familiarity with APRA, ASIC, AML/CTF, or other financial regulatory requirements· Agile and/or Waterfall project delivery experienceWhy Millbrook Group?· Opportunity to lead digital transformation in a high-performing financial services firm.· Dynamic Work Environment: Be part of a tight-knit, collaborative team where your ideas and contributions are valued.To ApplyPlease send your resume and a brief cover letter outlining your relevant <NAME_EMAIL> before 5 pm on Friday, July 11, 2025. Remuneration will be commensurate with your experience."
28,"C++ & C# Developer","Simplified","Location not found","Hybrid Flexibility to work from home","85000788","https://www.seek.co.nz/job/85000788?ref=recom-homepage&pos=29#sol=57fe47e0d93890f4ecb3038927f2cb0fd34a44e2","True","Senior Software EngineerStrong C++ & C#Hybrid - 2 days WFH$150k to $160k base + super We are proud to partner with a leading financial solutions company in the APAC region that is dedicated to delivering innovative and reliable software applications to meet the evolving needs of our clients. With a focus on excellence and customer satisfaction, they provide a dynamic work environment where talented professionals can thrive and grow. The successful candidate will play a key role in developing and maintaining software applications using C++ and C#. You will collaborate with a team of skilled professionals to deliver high-quality solutions in the fintech industry. Key Responsibilities: Develop C++ applications to process various transaction types on payment gateways. Build C# applications for terminal configuration and software management. Implement encryption/decryption systems and message formatting for financial messaging protocols (e.g., ISO8583). Write clean, efficient, and well-documented code. Collaborate with the team to identify software requirements and develop project plans. Troubleshoot, debug and optimize software applications. Conduct thorough testing to ensure software quality and functionality. Qualifications: Bachelor’s degree in Computer Science, Software Engineering, or a related field. At least 3 years of experience in software development using C++. At least 3 years of experience in software development using C# Experience with object-oriented programming (OOP) concepts, data structures, and algorithms, ideally within the payments or fintech industry. Familiarity with CI/CD processes and/or experience with Azure DevOps. Knowledge of databases and tools such as SQL Server. Strong problem-solving and debugging skills. Excellent written and verbal communication skills. Ability to work collaboratively in a team environment. Familiarity with Linux or Unix-based operating systems. Benefits: Competitive salary package and additional perks. Opportunity to work on exciting and challenging projects. Dynamic and collaborative work environment. Opportunities for learning and professional growth. If you are a highly motivated and passionate C++& C# Developer looking to make an impact in the payments solutions industry, we want to hear from you! Please submit your application here! Join this company in shaping the future of financial technology!"
33,"API Integration Specialist","Northbridge Recruitment","Location not found","17d ago","85048492","https://www.seek.co.nz/job/85048492?ref=recom-homepage&pos=35#sol=c0df286c6453689c12430415f87464b45ba72527","True","API Integration Specialist – Thriving Tech Team. Looking to step into a role where your tech skills actually make a difference? We’re on the hunt for an API-savvy problem solver to join a close-knit implementation team on Sydney’s Northern Beaches. You’ll be the go-to for integrating third-party systems — think everything from eCommerce and POS to inventory and payments. If you love connecting the dots between platforms, making systems talk to each other, and getting stuck into API workflows, you’re the one.They're doing big things in the tech space — working with household-name brands (you've definitely used their services). The work is hands-on and diverse, the vibe is collab and energy — hit the beach or sneak in a surf before your stand-up. What you’ll be doing: Working on custom API integrations between our platform and a variety of third-party systems Troubleshooting, testing, and making sure everything runs smoothly behind the scenes Collaborating with clients and partners to understand what they need and how to make it happen Helping deliver solid, scalable solutions that actually work in the real world What we’re looking for: Experience with RESTful APIs (and ideally SOAP too) Comfortable with scripting languages (JavaScript, Python, etc.) and working with SQL Strong troubleshooting instincts and a natural curiosity to figure out how systems connect Someone who communicates well — with both humans and machines Bonus points if you’ve worked with hospitality tech, or fast-paced implementation This role’s perfect for someone who loves a challenge, likes wearing a few different hats, and doesn’t mind rolling up their sleeves. You’ll have autonomy, variety, and a supportive team that’s genuinely fun to work with. Sound like your kinda thing? We’d love to hear from you. <NAME_EMAIL>"
38,"IT Support","Patterson Cheney Cars and Trucks","Location not found","Work in a dynamic; fun culture with a supportive manager and team","85452678","https://www.seek.co.nz/job/85452678?ref=recom-homepage&pos=40#sol=287614a6348c06bcdff91b39b6722693de329a6d","True","Patterson Cheney Cars and Trucks in Keysborough is looking for an additional IT Support member to ensure we provide exceptional service to our team of over 500 users. This is an excellent opportunity for those seeking to enjoy what the automotive industry has to offer. About the role: You will report to the IT Manager and be afforded a high level of responsibility. You must have excellent capabilities in supporting our staff with technical issues. You’ll bring knowledge and passion for all things IT-related and make your mark in this exciting and diverse role, which includes: Respond to support requests received in person, via phone, email, and internal ticketing systems. Understand the level of disruption caused by issues and respond appropriately to ensure timely resolution. Communicate with staff regarding resolving issues and any available workarounds while problems are resolved. Work with other staff in the IT team to ensure issues are being addressed, but not double-handled Set up new equipment as required by the staff. Maintain user access to IT systems, including for new staff, staff who change roles, and staff who leave. Provide feedback to the IT Manager regarding issues raised, to prevent future occurrences. On-site support and remediation of IT issues across all sites. So, who are we looking for? You will be a well-rounded IT professional, a strong communicator, and have a service delivery mindset. The IT Manager and fellow team members unconditionally support this role. With an exciting road map ahead, you will be excited by this opportunity. You must have a relevant IT certification or equivalent experience, experience supporting a large and complex organisation, and, most importantly, enjoyment working with an IT team of like-minded individuals. Getting a little more technical, you'll have a good understanding of and previous experience working with: Windows Server Administration (AD, DNS, DHCP, etc.) Extensive Windows desktop support for Win 7, 10, 11 Microsoft 365, SharePoint, OneDrive and Teams, Microsoft Entra ID. Experience with Mobility, Remote Access, Networking and Cyber Security Experience supporting printers, peripherals, iPhone/iPad etc PowerShell VOIP support Preferred and would be highly regarded (not essential): o Car dealership experience o Pentana, Revolution or Tune support/experience o Certifications in MCP, MCSA, and MCSE would be highly regarded. The successful applicant will have excellent phone manners, good presentation skills, and the ability to communicate and relay information to various stakeholders. Due to the nature of this role, occasional travel to various sites across Melbourne will be required; therefore, you must have a current Australian driver's licence. Our amazing benefits. Competitive salary Full-time (Monday-Friday). Fantastic work environment, designed by a supportive manager with the team front of mind Extensive training, development and support Career growth opportunities throughout the group Access to dealership discounts Job security with a strong and growing, independent family-owned business Employee Assistance Program And above all, lots of support, opportunity and fun. How do I apply? Please attach a resume, which will be directed to our Talent Acquisition Specialist. The successful candidate will be required to undergo a national police check. Why join the Patterson Cheney Group? Patterson Cheney Group currently has over 850 employees. We are proud to be one of Australia's largest automotive businesses with multiple brands (Toyota, Mercedes-Benz, Mahindra, JAC UTE, Isuzu and multiple truck brands). We have served our customers since 1915, and our success has been built on a strong foundation of customer service. With independent, solid family business values and behaviours, we emphasise enjoying our work. We take pride in being an equal opportunity employer looking to employ qualified candidates of any gender, race, religion or age with full rights to work in Australia. We will not be taking agency candidates."
39,"Front End Engineer","Real Time","Location not found","Interested in joining a wonderful team that are redefining what's possible?","85399617","https://www.seek.co.nz/job/85399617?ref=recom-homepage&pos=41#sol=5d83b141ceaec4863e4add897f6459f56729cb2a","True","We’re curious… How would you rebuild storytelling?If you have an idea, they (you wouldn’t have heard of them yet... they’ve just closed a pre-seed round led by AirTree) are interested in you. This is a once-in-a-lifetime opportunity to join and lead a high-conviction, high-impact team on a mission, building products that amplify the stories of technology companies that are genuinely changing the world – from the bleeding edge of AI and Quantum Computing to the frontiers of Biotech and Space Exploration. If you’re a Front-End Engineer that wants to directly shape the user experience of a product that will redefine an industry, let's talk.What you'll be Building & Shaping �� Leading Front-End Development: You will be the sole owner and leader of all front-end efforts, directly impacting the aesthetic and user experience of the core product. Launching the First AI Creative Strategist: Your immediate, high-impact objective is to lead the front-end development and successfully launch the first-ever AI creative strategist… a tangible, high-visibility product. Crafting Beautiful & Intuitive Experiences: Your work will directly enhance existing branding guidelines while devising unexpected, superior design solutions that elevate the product's visual appeal. Building Connected Systems: You'll be building connected systems that demonstrate clear prioritization, ensuring a frictionless yet rewarding user experience. High Autonomy & Rapid Iteration: Operate with significant initiative and independent problem-solving in a fast-paced, sometimes wild startup environment. What You'll Bring to the Team �� Hands-on proficiency with Next.js, strong command of CSS and Tailwind CSS, and JavaScript skills. The Taste Factor: An eye for seamless UI/UX and beautiful design. Product-Driven Builder: Beyond technical skills, you’re curious as to how to create rewarding experiences. Collaboration & Communication: An engaging personality with a collaborative spirit that fits our ambitious and friendly culture. Beyond the Code - What else they Offer: A highly competitive base salary (>$160k) coupled with significant ESOP, offering substantial financial upside as they scale. A rare, early-stage opportunity to directly lead the FE of company built for hyperscale. Collaborate daily with supportive, friendly, brilliant founders and a team of truly exceptional builders. An environment that encourages radical candor, and provides unwavering support for your personal and professional growth. WFH / WFO in a vibrant Chippendale office, with amenities designed for well-being, (think sauna, ice bath, lots of social and supportive activities.) Opportunities for travel and direct engagement with the world's most fascinating, fast-growing companies. What's Next: If you're ready to lead the charge, define the visual and interactive future of what will be a wildly successful AI product, and contribute to a mission-driven, high-growth company, then this is your moment ♥️"
40,"Control Systems Software Engineer / Automation","Information Systems Integration LTD","Auckland Airport; Auckland","4d ago","85435303","https://www.seek.co.nz/job/85435303?ref=recom-homepage&pos=42#sol=67734f165986e021f818de0be294ad7d15ceb29f","True","Take the next step in your career with a company where you can grow, innovate, and thrive.About ISIInformation Systems Integration (ISI) has been providing solutions to New Zealand’s leading manufacturing companies for the past 25 years. Deploying complex and leading-edge technology to enable nimble and efficient manufacturing and processes. Providing management data and KPI’s in real time, giving a view into plant and manufacturing performance that empowers management through historical and real-world online data.About the RoleWe have an opening for a Control System Engineer / Automation Engineer at our Auckland office. As a Control Systems Engineer, you will be responsible for delivering control system projects from concept through to completion. This includes designing, developing, testing, commissioning, and documenting solutions that meet both engineering and business standards. You’ll work with technologies such as PLCs, SCADA, SQL Database systems, and various hardware and software platforms to support our clients' operational needs. Your time will be split between office-based development work, on-site commissioning and servicing, and providing ad-hoc on-call support as required. This role offers a dynamic mix of technical challenges and hands-on fieldwork in a collaborative and innovative environment.Key ResponsibilitiesDeliver automation services including design documentation, programming, and implementation aligned with project scope and standardsEnsure timely and budget-conscious project delivery with high-quality outcomes ready for client handoverPlan and execute installation, testing, and commissioning of control systems including SCADA and Database DevelopmentProvide ongoing service and support for key customer sites, including participation in on-call rostersMaintain effective communication with clients regarding project scope, timelines, and budgets, while identifying future opportunitiesAdhere to safety, quality, and environmental procedures, ensuring compliance with company and client standardsEvaluate completed work for continuous improvement in safety, quality, and environmental practicesMaintain internal systems including task tracking, timesheets, and adherence to company policiesAbout YouYou will be degree qualified in Engineering having experience in control systems design, programming and commissioningWe will highly regard candidates with strong skills and experience in Industrial Control and Automation (SCADA and Database Design) including design, software development, testing and commissioningCandidates with experience in Database development and administration will be highly valuedYou will have a keen interest in Software development and be able to code in java and visual basicQualifications and ExperienceClient-focused with strong communication skills.SCADA system design and development.Industrial communications, network design and development.Experience in the manufacturing sector such as Food & Beverage and Heavy IndustriesExperience with Rockwell software and Ignition SCADA"
41,"Application Support Specialist (IT Specialist)","RBP4 PTY LTD","Location not found","6d ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=43#sol=0731cc64ad5958976cd76283fd7eeb80199925f8","True","Key ResponsibilitiesERP / Application Support (60%)• Provide day-to-day support for SAP Business ByDesign users across various departments.• Troubleshoot and resolve SAP-related issues, assist with reporting, and guide users on process flows.• Maintain and manage user accounts, access rights, and workflow configurations.• Coordinate with external SAP consultants for SAP module updates, configuration changes, issue resolution, or enhancements.• Assist in UAT (User Acceptance Testing), document SOPs, create user training materials, and assist with onboarding.Internal IT/ MIS Support (20%)• Provide basic IT support for desktop/laptop issues, operating systems, and peripheral devices (e.g. printers, monitors).• Support fundamental IT hardware, system management, network administration, information security, and IT asset management.• Provide support for ad hoc IT projects, encompassing implementation, system upgrades, and testing of new software applications• Coordinate with external IT consultants for escalated support or system maintenance.• Ensure day-to-day smooth operation of office IT environment.System Administration & Support (10%)• Manage user accounts, permissions, and services across various enterprise applications, including but not limited to Office 365, QMS, and Endpoint Security System.• Provide end-user support and training and troubleshooting for these platforms to ensure smooth daily operations.Stakeholder Engagement (10%)• Act as the liaison between business users, and external consultants, especially SAP service providers.• Communicate effectively with stakeholders to understand business requirements and translate them into technical solutions.Key Requirements• Degree qualified in a relevant Information Technology discipline• Fluency in both Mandarin and English (spoken and written).• 3+ years’ experience in ERP or application support roles (SAP Business ByDesign or S/4HANA preferred).• Familiarity with business processes in finance and procurement.• Basic understanding of IT systems (Windows OS, networking basics).• Strong interpersonal and problem-solving skills; able to work with both technical and non-technical users.• Comfortable working independently in a small, multi-functional team.Desirable• Prior experience in biotech or life sciences industries.• Exposure to IT policies such as data security, access control, or audit support.• Experience supporting teams across different locations and working inmultilingual teams."
42,"Integration Developer- Sydney Olympic Park","4wd Supacentre","Location not found","Hybrid Role - 3 days at Sydney Olympic Park Office; and 2 Days Remote (optional)","84941348","https://www.seek.co.nz/job/84941348?ref=recom-homepage&pos=44#sol=ea54c070fae21e628b063e60da7344ee339e244a","True","Integration experts wanted. Connect the systems powering Australia’s adventure.Are you driven by innovation and thrive on building scalable, high-impact solutions? At 4WD Supacentre, we don’t just keep up with change, we drive it. As an Integration Developer, you’ll play a pivotal role in connecting our core systems and bringing strategic ideas to life. Working at the intersection of business and technology, you’ll help streamline operations, accelerate delivery, and enable our national retail business to scale efficiently. If you’re ready to shape the digital backbone of a fast-growing company, this is your opportunity to make a lasting impact. Join us at our Sydney Olympic Park Office NSW.About the RoleAs an Integration Developer at 4WD Supacentre, you’ll play a critical role in enabling the business to quickly adapt its strategy and embrace innovative, market-disrupting technologies with minimal risk. This hands-on technical position is focused on minimising the time to value and maximising return on investment by taking concepts swiftly to market.In this role, you’ll be integral to the implementation of the company’s high-growth strategy by architecting and building robust, scalable integrations using best-in-class technologies. You’ll help reduce implementation and maintenance costs while lowering the business's dependency on the ICT department.Your typical week will involve a balanced mix of responsibilities, including gathering business requirements (20%), architecting and designing solutions (20%), building integrations (50%), and identifying improvement opportunities and maintaining documentation (10%). You’ll work closely with internal developers and other stakeholders to bring strategic ideas to life.Ultimately, this role is about connecting the old world to the future of 4WD Supacentre; leveraging cutting-edge integration technologies that federate millions of transactions and connect hundreds of internal and external systems, ensuring a seamless, secure, and high-performing data ecosystem that delights millions of customers each month.Role Responsibilities:• Owning our Cloud Integration Platform (iPaas)• Establishing, documenting and enforcing Integration Principles/Security• Establish and maintain low code / no-code integration practices• Design and apply integration patterns to solve a business problem• Take ownership of transactional and analytical data integration• Implement solutions to transfer data with accuracy and minimal latency• Secure data connection endpoint to comply considering possible Cyber Security threats• Implement and design modern integrations between internal and external systems through the appropriate way for the purpose• Implement reusable integration with version control (for source code and API versions)• Implement highly scalable, high-availability Integration Bus in the cloudAbout you:You are a great team player, with this burning feeling inside to achieve more, to not settle for average. Have the thirst for knowledge, to experiment to achieve. You are someone who never says, ‘it cannot be done’, but rather leave no stone unturned to overcome difficulties.You'll bring:• Excellent knowledge implementing, REST API, SOAP Services, XML, CSV data dumps• Cloud based integration platform architecture experience• Knowledge of the integration “DOs” and “DON’Ts”• A high standard for designing solutions with documentation, security and error handling• Extensive experience with AWS• A very high coding standard and experience with complex software systems• The capability to analyse, explore and refactor legacy applications with uplifting technologiesRequired skills and qualifications• Master’s Degree in Computer Science• 5+ years’ in Agile Software Development• Experience with integrating systems 3 + yearsBeneficial skills and qualifications (but not essential)• Integration implementation in Mulesoft and Workato• Workato Pro III certificate• TOGAF 9.2 Certificate• Google Cloud Architect Certificate• Security Engineer or Associate Certificate• Workato Platform experienceWhat’s in it for you?• Competitive remuneration package of $120,000 - $165,000• Exclusive employee discounts on the Adventure Kings product range• Hybrid Role - 3 days per week at Sydney Olympic Park Office, and 2 Days Remote (optional)• Be part of a dynamic, rapidly evolving, fast-growing companyIf you want to be a part of our future, have the opportunity to grow then apply NOW!*Please note: Only applicants with full, unrestricted Australian working rights will be considered for this role. We are not offering visa sponsorship at this time. Recruitment agencies and third-party inquiries will not be accepted."
45,"Mid-Level Software Engineer (Backend Leaning)","The Onset","Location not found","Build a no-code SaaS Platform","85540567","https://www.seek.co.nz/job/85540567?ref=recom-homepage&pos=47#sol=886deae6e9e5d0ac2bcecbcedc5e685427eb5b0e","True","Workflow Automation Platform | Node.js, Golang, ReactSydney | Up to $130K + super | Hybrid (2 days in-office)Imagine building the infrastructure that powers how work gets done, not just for one company, but for dozens of global enterprises. That’s the opportunity here.You’ll be joining a fast-growing product company backed by Australia’s top investors, already partnered with global enterprise clients and expanding into the US. Their platform is mission-critical for customers - streamlining, automating, and scaling business operations.As the platform scales, we’re hiring a backend-leaning full stack engineer to help evolve and strengthen the core architecture - particularly in Go and Node.js.You’ll join a collaborative, tight-knit team in Sydney, working closely with experienced engineers and company founders. There’s meaningful architectural work to lead or contribute to, and your impact will be felt across the product from day one.What’s in it for you? Be part of a company scaling fast with real customers and technical complexityTackle meaningful architectural challenges - refactoring legacy systems, improving service boundaries, and building scalable backend solutions Contribute to a product that’s growing in both depth and reach, with autonomy to own backend projects from design to productionOpportunities to support across the stack - and to mentor or be mentored depending on your strengthsJoin a product-first company where engineers are involved in decision-making, not just executionWork in a flexible hybrid model - two days a week in the office, with the rest remoteAbout you2 - 4 years of experience as a backend or full stack engineer with strong Go or Node.js skillsExperience building APIs and backend services that are simple, scalable, and maintainableComfortable working with frontend code (React/TypeScript) when neededPragmatic thinker who avoids overengineering and values clean codeExperience in product-focused teams or startups where you’ve worked closely with product and designCollaborative, proactive, and eager to take ownership in a high-autonomy environmentThe stackNode.js, GolangReact, TypeScript, Tailwind AWS, Docker, KubernetesSound like you? Apply now or message Indigo Munro on Linkedin if you’re keen to know more!"
46,"Software Implementation Consultant","Great Automations Company Pty Ltd","Location not found","Build and deliver smart solutions that make a positive impact for businesses","85475627","https://www.seek.co.nz/job/85475627?ref=recom-homepage&pos=48#sol=7b6e5f4ae63ba937a3ad22086851e51ce1608c8f","True","Ready to shape the future of work with monday.com and cutting-edge AI technology? Join a high-growth team where your ideas matter, your curiosity is valued, and your career will thrive!About the RoleAs an Implementation Consultant, you’ll help organisations across Australia and New Zealand transform their processes using monday.com and the latest in AI. You’ll work hands-on with clients, deliver solutions, and continuously learn as you go.What’s in it for you?Work with the latest tech: Hands-on experience with AI, automation, and monday.com.Continuous learning: Lead your own development with certifications, training, and a say in what you learn next.Flexible work: Choose hybrid or in-office—whatever fits your life best. We value time in the office to collaborate as a team but is not required everyday.Supportive culture: Join a team that encourages initiative, celebrates innovation, and backs your growth.Career growth: Real opportunities to progress, specialise, or lead as we grow together.Learn the Business SideThis isn’t just an implementation role—it’s a front-row seat to how a fast-growing monday.com consultancy really works. You’ll get exceptional exposure to all aspects of business operations, directly alongside our Founder. Here’s what you’ll gain:Business Development: See how we attract and retain clients in a competitive industry.Financial Management: Gain real insight into budgeting, resource allocation, and profitability.Operational Strategy: Observe how key decisions are made as we grow and scale.Service Innovation: Experience how new services are created, packaged, and rolled out.Team Building: Learn what it takes to build and nurture talent from the ground up.Business Planning: Understand the rhythms of business planning, execution, and iteration.Mentorship: Work directly with the Founder, learning first-hand how a consultancy is run and scaled.You’ll also develop project management and client relationship skills while working across multiple industries. For an ambitious professional, this is a unique opportunity to gain practical business education that could launch your own venture in the future—or fast-track you to senior leadership within our company.What you’ll doMap client workflows and design effective monday.com + AI solutionsSupport the end-to-end delivery of projects, from planning to go-liveConfigure automations and integrations, troubleshoot challenges, and unlock platform potentialDeliver engaging client training to drive user adoptionBuild strong relationships with clients—be a trusted advisor through their digital transformationShare ideas and help improve how we workAbout you2+ years experience in consulting, project delivery, SaaS, or software implementationHands-on experience with monday.com is desired, but not requiredExperience with CRM or customer service platforms such as Salesforce, HubSpot, Zendesk, Airtable, Microsoft Dynamics 365, Freshdesk, or similar is highly regarded.Interest or experience in AI, automation, or related technologiesExcellent communicator—able to make complex tech simpleOrganised, proactive, and ready to manage multiple projects at onceFast learner, tech-savvy, and passionate about business improvementExperience with Agile, Waterfall, or hybrid methodologies a plusmonday.com, Salesforce or AI certifications are a bonus, not a mustReady to make an impact?Apply now with your CV and a brief cover letter telling us what excites you about the role.We value diversity and encourage people of all backgrounds to apply."
49,"Platform Engineer – Software Deployment Tools","Westbury Partners","Location not found","Join a low-latency; high-performance tech culture.","85295260","https://www.seek.co.nz/job/85295260?ref=recom-homepage&pos=51#sol=1c1d14de798abb5ac1ada657355bd62e205ce3b9","True","Be part of a Sydney-based High Frequency Trading firm with global influence. Our client owns mission-critical systems, moves fast, and continuously delivers. Their work is valued, visible, and essential to business success. What You’ll Do:Join a high-impact engineering team responsible for scalable deployment infrastructure supporting tens of thousands of services. Your tools will help deploy and operate the core trading software used every second around the globe.Your responsibilities will include:Writing high-performance Go services for deployment automationCollaborating with teams across trading, quant, and infrastructureImproving reliability, throughput, and ease of deploymentRunning and maintaining tools in production across global environmentsShipping code daily with automated testing pipelinesSupporting live systems during Sydney business hoursAbout You:1–3 years’ experience in backend or systems softwareStrong in Go, Python, or JavaExperience with Kubernetes, Linux, Docker, and CI/CDEnjoy building resilient systems and writing clean, testable codeConfident working with global stakeholders and teammatesLove performance tuning, automation, and elegant infrastructureYou’ll thrive in a collaborative, impact-driven environment.Ready to take ownership of systems that keep global trading moving? Step into a role where software meets scale and speed.#platformengineering #golang #devopsengineer #ci_cd #softwaredeployment #tradingtech #sydneyjobs #backendengineering #kubernetes #linuxengineering #softwarecareers #automatedtesting #distributedsoftware #devopsculture #highperformancetech"
51,"Game Designer","Grinding Gear Games","Henderson; Auckland","22h ago","85528860","https://www.seek.co.nz/job/85528860?ref=recom-homepage&pos=54#sol=d348ba3ea35c4acf59aab371b26bd948f04a1b7b","True","We seek experienced Game Designers to join the Path of Exile development team.Location: On-site in Henderson, Auckland, New Zealand*Relocation assistance (for the right candidate if needed)Full-time position - 40 hours a weekThe position includes the following duties:Designing gameplay systems, monsters, bosses and items for Path of ExileEditing metadata and scripting to implement your designsCommunicating your designs to artists and programmers to allow them to create what you have designedWe are seeking someone with:3+ years of experience with production, quality assurance or game design work on a commercial online game project OR a bachelor's degree in game development or similarHigh creativity and a keen eye delivering engaging, immersive experiencesExperience with game editors, scripting and design toolsStrong critical thinking and analytical skillsAbility to receive and apply feedback in a highly iterative environmentExcellent communication skills and a proven ability to work within a teamA passion for narrative driven gameplayKnowledge of the Path of Exile universeFamiliarity with the fantasy genre in games, movies and literaturePortfolio, and/or reel demonstrating relevant skills required.Must be willing to complete a test if requested to do so.Pluses:Familiarity with 3D, animation and visual effects pipelinesExperience with MMORPGs with a focus on in-game economiesHow to applyIf you meet the above requirements, please email your resume, portfolio and cover <NAME_EMAIL> with your name and role you are applying for in the subject line.No phone calls or agencies, please.What we offerA huge selection of free drinks for all the staff to enjoy while working. Think of a convenience store's range of drinks a few metres from your desk! Our rec rooms are stocked up daily with healthy snacks and a rotating range of seasonal fresh fruit and yoghurt, and catered meals are offered every week as part of our Monday mid-morning meetings.When the work is done, we invite all staff to attend our expansion launch parties and Christmas parties to celebrate the effort they've put towards Path of Exile."
54,"Senior Software Engineer (Sensor Fusion) x 2","TheDriveGroup","Location not found","3d ago","85456965","https://www.seek.co.nz/job/85456965?ref=recom-homepage&pos=57#sol=7740c73cbda0efb5cd7d3b3464139d04ff45f1aa","True","An opportunity is available for an experienced Senior Software Engineer with Algorithm Development experience to lead the advancement of high-performance tracking and sensor fusion systems for real-time defence applications. This role is critical in developing next-generation capabilities that detect and track drone swarms in complex, fast-evolving environments. Working within a cross-functional engineering team, the role focuses on writing efficient, production-ready code for data-intensive applications. The successful candidate will be instrumental in shaping the evolution of real-time sensor fusion pipelines used in mission-critical deployments globally. Key Responsibilities Design, implement, and optimise algorithms for multi-object tracking and real-time sensor fusion Profile and improve performance-critical code using techniques such as multi-threading, GPU acceleration, and native language bindings Review current research in multi-object tracking to keep algorithm performance at the cutting edge Build and maintain CI/CD pipelines to support code quality, performance, and regression testing Collaborate with stakeholders to investigate and resolve system-level bugs and edge cases Follow best practices in refactoring, testing, and documentation to ensure maintainability and scalability Work closely with peers across AI, embedded, RF, and systems teams as part of an agile, high-performance group Skills and Experience Degree in Computer Science, Mathematics, or a related technical field Minimum 5 years’ experience in professional software development environments Advanced C++ skills with experience in Python and/or Go (particularly in creating C++ bindings) Familiarity with CMake, unit testing frameworks, and code profiling tools Experience with data-intensive, low-latency, or GPU-accelerated systems (e.g., TensorRT, CUDA) Strong foundation in algorithms, data structures, and applied mathematics or physics Comfortable developing in Linux and using containerisation tools such as Docker Experience in CI/CD environments and automated testing pipelines Desirable Qualities Background in sensor fusion, signal processing, or object tracking Familiarity with observability stacks like Prometheus and Grafana Exposure to AI/ML techniques and data-driven model deployment Motivated by performance optimisation, real-world results, and technical excellence Comfortable working autonomously in high-impact, fast-paced project teams This role presents a unique chance to work on defence technology with real-world applications, contributing to the development of scalable, intelligent tracking systems deployed globally. It is ideal for an engineer who thrives on complexity, performance, and cross-disciplinary collaboration.You can contact <NAME_EMAIL> for more info, or click Apply and send through your details and I'll be in touch."
55,"System Analyst / Technical Assessor (SaMD)","Calleo","Location not found","Canberra / Sydney / Melbourne / Brisbane locations available","85365154","https://www.seek.co.nz/job/85365154?ref=recom-homepage&pos=58#sol=d607d29051a6b1063941752ad36c7061313753c8","True","OverviewOne of our Federal Government Clients are in the process of assessing Software as a Medical Device (SaMD) products and providing high level summaries and findings.Due to the nature of the role, candidates must be Australian Citizens to be found suitable. Key Responsibilities Assess information provided by software vendors or developers, highlighting gaps, issues and areas of risk.Research information about software products.Assess technical artefacts provided to identify whether they are fit for purpose, complete and technically sound.As part of the team, respond to specific and complex email and phone enquiries from internal and external stakeholders, seeking appropriate clearance where required.Research and write reports on specified areas of medical device software products.Contribute own expertise by providing technical input in preparation of reports and other policy implementation undertaken by the team Candidates with skills and experiences in the following are encouraged to apply Technical Skills Experience in a commercial or government IT software development environment.Assesses technical artefacts to identify whether they are fit for purpose, complete and technically sound.Experience providing technical input for reporting and other policy implementation undertaken by the Section. Analysis and Research Undertakes assessment, research and analysis of information about software products from diverse sources and formulates conclusions in line with policy.Maintains familiarity with medical device regulations relating to software. Stakeholder Engagement and Teamwork Works autonomously or as part of a team, responding to specific and complex email and phone enquiries from internal and external stakeholders, seeking appropriate clearance where required.Demonstrated ability to translate technical information for others using appropriate, language.What is on offerCanberra / Sydney / Melbourne / Brisbane offices Hybrid and remote work available. 12 month contract with extension options. If you are interested in the above job opportunity, please apply through the links provided or please feel free to contact Raj for more details. <EMAIL>.au0422 375 690 Due to the nature of this role, this job is open to Australian Citizens only. Candidates who currently hold or able to obtain Federal Government Security Clearances are highly desirable.Calleo is an equal opportunity employer and we encourage applications from all people including Aboriginal and Torres Strait Islander peoples. Follow Calleo on LinkedIn and visit our website to keep up to date on all our current job vacancies:www.linkedin.com/company/calleoresourcingwww.calleo.com.au"
56,"Moodle Application Developer","Talenza","Location not found","21h ago","85530884","https://www.seek.co.nz/job/85530884?ref=recom-homepage&pos=59#sol=14d8b7b040d7cc313ee2cc973c5626ce1b8452f2","True","Our NSW Government client is hiring for a highly capable Moodle Application Developer to join the organisation.Role Title: Moodle Application DeveloperStart Date: ASAP Contract Length: 9 monthsDaily Rate: $800 - $900 +SuperLocation: Eveleigh (2015) on-site and WFH hybrid. Key Responsibilities:Develop and customise Moodle functionality, including plugins, themes, and APIs.Implement automation for enrolment, credentialing, and reporting processes.Develop and maintain integrations between Moodle and other systems.Conduct testing and troubleshooting of Moodle-related development activities.Ensure Moodle application security, including secure coding practices and compliance with organisational policies.Work collaboratively with administrators, IT teams, and business stakeholders to align development with business needs. About You:Experience developing and customising Moodle applications using PHP, MySQL and JavaScript.Experience in integrating Moodle with other enterprise applications.Strong understanding of Moodle architecture, APIs, and plugin development.AWS services (EC2, RDS, S3, networking).Knowledge and experience of authentication and access management (SSO, MFA, IAM).Strong communication and documentation skills.Ability to troubleshoot and optimise Moodle performance. Whats Next?Candidates must be based in or around the Greater Sydney Area and willing to work on-site as required Additionally, you must possess existing full working rights within Australia.If you feel this opportunity matches your skills and previous experiences, please apply with your CV."
57,"Software Tester","Extend Recruitment","Location not found","Global Leader in Energy Market Trading and Analytics","85360995","https://www.seek.co.nz/job/85360995?ref=recom-homepage&pos=60#sol=976beb91b8478a2265408f4880fc7bd4a5a918ac","True","The Company: We are delighted to be in active recruiting mode on behalf of a client who is experiencing tremendous growth and success. They are a multi-award winning trading and risk management software provider to the energy and commodities markets. With offices in Australia, UK and USA, they have a strong international client base with platform installs in over twenty countries. Backed by a reputable energy exchange company, this business is continuously recognised as a global leader in energy and commodity pricing and analytics. The Role: To add to the almost-dozen new hires we’ve facilitated for this organisation in recent months, we are now recruiting an experienced Software Tester, a newly established role within the Application Development team, who will join the Sydney group on a full-time hybrid basis. Some of the key responsibilities will include: Overseeing the platform’s numerical libraries and platform versions Designing, implementing, and executing new test cases. Reviewing and updating existing test cases. Performing functional and regression testing duties. Database configuration and application testing of software deliveries for clients and demonstration environments. Work in close cooperation with quantitative developers, analysts and researchers. Documenting test processes. Required Skills/Attributes: To be successful in this role, you must be passionate about both the client’s customer and commercial success and ensuring that the company produces robust, well-tested software. You will be self-motivated, be able to learn quickly, and have the ability to adapt to new challenges and situations. Additionally, you will likely have and be able to demonstrate: Bachelor’s degree (Computer Science, Maths, Actuarial Studies) 3 years plus of testing experience in a commercial environment. Experience in testing quantitative and technical elements software products. Experience with SQL Server. Excel add-in and Web browser testing experience. Experience with source control, defect-tracking tools and automated testing tools. Experience with (or demonstrable interest in) automation testing tools for APIs and Web. Experience with (or demonstrable interest in) quantitative methods and finance would be an absolute advantage as far as your candidature is concerned.The remuneration package is competitive and commensurate with experience. It is expected the successful candidate will likely have an immediate start. Before You Apply Please Note: You must have unrestricted rights to work in Australia. The role will allow you work several days from home, but you will be Sydney based or have easy access to the Sydney CBD when required A test of your technical skills will be part of the selection process. What I always suggest to applicants is the following: Don’t ask yourself “Can I do this job?”. Ask yourself instead “Will I be one of the strongest applicants to be considered”? I am interviewing now so please apply online, email <EMAIL> or contact Jorge Albinagorta on (+61) 0439 781 736. I would love to share more with you if you are genuinely interested in the role and you feel you meet the position criteria. As an Equal Employment Opportunity employer, Extend and my client provide equal opportunities to all employees and applicants without regard to race, colour, religion, sex, national origin, age, disability, genetic information, veteran status, sexual orientation, gender identity, or any other legally protected characteristic. Thank You!"
59,"Mulesoft Engineer","Talenza","Location not found","3d ago","85462659","https://www.seek.co.nz/job/85462659?ref=recom-homepage&pos=62#sol=56e4e7df2a24dd14d44ebda0bfac839f654f0e9b","True","$750 per day: 6-month initial contract with potential extensions3 + years Mulesoft experience integrating with Salesforce environmentsHybrid working, 3 days in Melbourne CBDOrganisation: Join a 100% Australian based professional services with no offshore resources. Many companies ""say"" they put their people first, but they truly do with their ""white to black belt academy"" focussing not only on career development but mental and physical health as pillars for success. They also believe in the holacracy philosophy, decentralised management and trusting your people to be the new way of working for our generation. Role: As a Senior Mulesoft Developer you will:Design and implement enterprise grade integration solutions adhering to system and data security best practices and documented design patterns and development standards Assist in establishing best practice MuleSoft development standards Capable of understanding business requirements, working with end-users and developing and deploying integrations.You will have:5 + years of hands-on software development experience including integration solutions3+ years' experience with MuleSoft Anypoint platformUnderstand the Salesforce Data Model for Financial Services.Experience with Salesforce bulk data load via Mulesoft.Experience with AWS Redshift, DynamoDB, Redis.Experience with JWT and Okta authentication processes.Knowledge of Mulesoft best practices for Salesforce Integration.Experience with Jenkins and/or Azure DevOps.Financial services experience advantageousExperience in technical design, build and maintenance of applications for on-premise and cloud systemsStrong capability of understanding business requirements, design and management of end-to-endStrong knowledge and proven implementation of EAI/SOA best practices, development methodologies & standards and security.Proven experience developing APIs aligned to TMF and MEF using API led architectural pattern.Working experience integrating with Salesforce, ServiceNow,Knowledge on event driven architectures, platforms, and experience with Kafka, SQS, SNS, ActiveMQ SES, Lambda and other AWS Service offeringsMinimum cert - Mulesoft Developer level 2"
60,"Service Analyst/Coordinator","NTT Australia","Location not found","6d ago","85296956","https://www.seek.co.nz/job/85296956?ref=recom-homepage&pos=63#sol=57f821d4e9de4a9c700770b07fdd4a4f78837c2d","True","Job Title: Service Analyst/CoordinatorContract Type: 9-Month Contract (High Possibility of Extension)Location: Bella Vista, NSWWork Arrangement: Hybrid (3 Days Onsite per Week) About the Role We are seeking a motivated and detail-oriented Service Analyst to join our IT Infrastructure team. In this role, you will work closely with Senior Managers, Product Owners, partners, and vendors to support the delivery of end-user computing services. You will provide both business and technical expertise to ensure the smooth operation and continuous improvement of IT infrastructure services. Is innovation part of your DNA? Do you want to enable a connected future for people, organizations, and society? Join our growing global NTT team and you’ll be part of the world’s largest ICT company (by revenue). We’ve combined the capabilities of 28 remarkable companies to become one, leading technology services provider. Together, we help our people, clients, and communities do great things with technology to create a more secure and connected future. We employ 40,000 people across 57 countries. By bringing together the world’s best technology companies and emerging innovators, we work together to deliver sustainable outcomes to businesses and the world. Innovation is part of our DNA. We believe it’s key to what makes us different. So, we strive to move forward, challenge the status quo, and drive excellence through the technologies we integrate and the services we deliver around the world. The result is connected cities, connected factories, connected healthcare, connected agriculture, connected conservation, connected mobility, and connected sport. Together we enable the connected future. Responsibilities: Service Strategy Gather and document business requirements for service development and improvement. Collaborate with Product Owners to translate requirements into product specifications. Conduct financial analysis of infrastructure services (e.g., costing, pricing, benchmarking). Support the annual budgeting process and assist in business case development. Contribute to the development of the IT strategic roadmap. Service Design & Transition Define key service level metrics in collaboration with Service Management and Product Owners. Develop dashboards to monitor service performance, ensuring quality and cost-effectiveness. Provide input into capacity management and optimize technology usage and consumption. Support analysis and evaluation of new product selections. Maintain and enhance the IT service catalogue. Continuous Improvement Analyze service quality trends and recommend improvements. Coordinate the production of monthly infrastructure reports, covering strategy, design, and service enhancements. Requirements: Skills & Competencies Proactive, solution-oriented mindset with a strong drive for results. Excellent analytical and problem-solving skills. Financial literacy for cost-benefit analysis and business case development. Proficiency in creating data visualizations and dashboards. Skilled in accessing, manipulating, and interpreting data to generate insights. Strong communication and interpersonal skills. Ability to build and maintain effective working relationships. Awareness of emerging IT trends and adaptability to new technologies. Solid business acumen. Experience Strong experience working with infrastructure technologies. Proven track record in project delivery and product selection. Desirable Familiarity with analytics and insights tools. Questions? Reach out: <EMAIL> be considered for this opportunity click the 'Apply' button, or for more information about this and other opportunities please contact Kumar Kuchipudi. Please quote our job reference number: 646215.About usWorkforce Solutions is a division in the Australia and New Zealand region of NTT Ltd. and includes the former Viiew as part of its broader expertise and capabilities. The Workforce Solution division specialize in workforce-as-a-solution services, allowing clients the flexibility to augment or strengthen their team with a specialized workforce who offer deep experience and expertise in digital, data, security, infrastructure, project solutions and business transformation. Our highly experienced and skilled teams are supported by extensive training and enablement, ongoing technical assessments and our global network as part of NTT Ltd. All team members of Workforce Solutions are selected for the depth of their knowledge and experience in their particular field, as well as their professionalism and ability to integrate into our client’s workplace environment. To ensure the best match, we encourage clients to be involved in selecting their Workforce Solutions consultants.NTT Ltd. is a leading global technology services company. Working with organizations around the world, we achieve business outcomes through intelligent technology solutions. For us, intelligent means data driven, connected, digital and secure. As a global ICT provider, we employ more than 40,000 people in a diverse and dynamic workplace that spans 57 countries, trading in 73 countries and delivering services in over 200 countries and regionsNTT is proud to be an equal opportunity employer with a global culture that embraces diversity. We are committed to providing an environment free of unfair discrimination and harassment. We do not discriminate based on age, race, color, sex, religion, national origin, disability, pregnancy, marital status, sexual orientation, gender reassignment, veteran status, or other protected category."
63,"Software Developer","AIMCRAFT PTY LTD","Location not found","10d agoViewed","85227466","https://www.seek.co.nz/job/85227466?ref=recom-homepage&pos=66#sol=caeb0c42b6d55955a80295576d0988db77c3f0b9","True","About UsAIMCRAFT PTY. LTD. is a Melbourne-based digital development company specialising in responsive website design, mobile app development, e-commerce solutions, and WordPress-based CMS platforms. We deliver smart, user-focused digital experiences to help businesses succeed across industries. About the RoleWe are currently seeking a full-time Software Developer to join our growing team. This role is ideal for a technically capable and collaborative developer who is passionate about building high-quality web and mobile applications with a strong user experience focus.Key ResponsibilitiesDesign, develop, and maintain responsive websites and mobile applications tailored to client requirements, using modern frameworks such as React, Vue.js, or Flutter, ensuring seamless cross-platform functionality and optimal performance.Collaborate with other developer and project administrator to convert wireframes and prototypes into fully functional digital products, ensuring a high standard of usability, accessibility, and visual appeal.Build, configure, and extend e-commerce websites using platforms such as WooCommerce and Shopify, including secure payment gateway integration, shopping cart optimisation, and third-party service connectors (e.g., shipping APIs, inventory systems).Develop customised CMS-based solutions, with a strong focus on WordPress, including theme development, plugin integration, and performance tuning to support scalable and client-specific content management workflows.Identify and address front-end and back-end performance issues, conducting code reviews, browser/device compatibility testing, and implementing caching or optimisation strategies to improve load speed and responsiveness.Manage the full software development lifecycle, from requirement analysis and technical specification drafting to coding, version control, deployment, and maintenance, while ensuring alignment with client expectations and industry best practices.Prepare thorough technical documentation for websites, mobile apps, and CMS systems to support both internal development continuity and client knowledge transfer.Participate in QA processes including test plan creation, automated and manual testing, bug fixing, and regression testing to ensure product stability across updates and enhancements.Support project planning by contributing accurate time estimates, identifying potential risks, and recommending scalable technical approaches aligned with project goals.Continuously evaluate new technologies, frameworks, and development tools, sharing insights with the team and applying selected innovations to improve product quality and development efficiency.Provide post-deployment technical support, troubleshooting issues, and implementing updates or customisations based on user feedback and evolving client requirements.Engage in regular team meetings and client communications to report progress, clarify project scope, and align development priorities with business objectives. Requirements· Minimum 1 year of hands-on experience in software development, web development, or mobile app development within a professional environment.· A Bachelor’s degree or above in Information Technology, Computer Science, Software Engineering, or a related discipline.· Proficiency with version control tools, software testing methodologies, and CI/CD deployment pipelines.· Solid understanding of modern front-end and/or back-end frameworks, with the ability to deliver clean, maintainable, and scalable code.· Strong analytical and problem-solving skills, with a keen eye for detail and performance optimisation.· Ability to communicate effectively with both technical and non-technical stakeholders, and to collaborate within a cross-functional development team.· A proactive attitude towards learning and adapting to new technologies or project requirements. Why Join AIMCRAFTYou will work in a collaborative, innovative, and supportive environment where your technical ideas and contributions are valued. We focus on producing meaningful digital products and helping our team grow professionally."
64,"Implementation Lead","SDS OPERATION PTY LTD","Location not found","Drive pre-sales and post-sales implementation processes within the team.","85526755","https://www.seek.co.nz/job/85526755?ref=recom-homepage&pos=67#sol=901b1b2277c31337014fec509f75c6f98f7e1cd2","True","Based in Crows Nest and supporting the Implementation Manager, this newly created role is critical to SISS Data Services’ ongoing success by connecting sales support, solution design and project delivery. Having just completed a round of significant capital raising, we are looking to build on our success in providing a range of products that meet the needs of a diverse range of clients and use cases. Joining SISS Data Services at this time will provide you with an opportunity to: Build on your existing skill set, working across a variety of products and clients. Drive pre-sales and post-sales implementation processes and projects within the team. Work flexibly within a relaxed hybrid environment working from home 1 day per week Join a vibrant organisation experiencing amazing growth and change in an evolving sector. SISS Data Services is Australia’s leading provider of secure financial data. With over 10 years’ experience, we have earned the trust major Australian banks, leading software platforms and their customers. We are at the forefront of the Open Banking transformation that is occurring within the finance industry, providing secure and dynamic solutions that revolutionise how businesses handle their banking data. As Implementation Lead you will: Lead end-to-end product implementations from presales through to go-live. Work closely with Sales, Service, and Product teams to understand client needs and requirements. Deliver outcomes for clients through building strong relationships and networks. Provide ongoing technical support to clients when required.To be successful, you will need to apply product expertise alongside strong communication and project coordination skills. Further to this, you will need: Relevant qualifications with a strong background in software or similar implementations. The ability to uncover, develop and advise clients on options that best suit their use case. Excellent problem-solving skills and an ability to manage priorities across multiple projects. Experience with ERP, accounting or adjacent software would be an advantage."
65,"Technical Project Manager/Scrum Master","Good to Great Schools Australia Limited","Location not found","Remote – full time or part time working arrangements","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=68#sol=26a3444506b7a9c518c584cf66aec517ba864820","True","Work from Home Role (unless based in Cairns)About the OrganisationGood to Great Schools Australia (GGSA) uses a school improvement framework to support schools to make their improvement journey from Poor to Fair, Fair to Good and Good to Great.About the Business Unit Product Development deploys leading edge practices to drive the organisation’s projects, navigating them through our innovation model. The team works across the strategy, design, build, implement and operate phases to deliver new products and services that delight our customers. About the Role The Technical Project Manager will contribute to the organisation’s objectives by leading the innovation and product projects to ensure that innovation projects and organisation tasks are effectively prioritised, planned and delivered. Essential requirements of this position are leadership, planning, collaboration, written and oral communication skills and the flexibility to undertake a range of activities based on the changing needs of our small but growing organisation. The Project Manager will advise project teams involved in the delivery of innovation projects on the innovation model systems, processes, practices and tools to achieve results in line with the GGSA vision. This particular role may occasionally work with GGSA partners and other stakeholders Essential criteria Project management and/or business qualifications;Experience of the Queensland (or other states and territories) education sector; policies, structure, framework.Managed an education program;Successful delivery of project outcomes in a complex environment including the use of project management methodology and risk management;Managed a project team of 5 or more full time employees who had other teams reporting to them;Ability to think strategically through complex issues taking into account the bigger picture and longer-term outcomes;Demonstrated experience in influencing the work and direction of a high-functioning project team/work group;Proven ability to develop strong networks with diverse key partners with demonstrated negotiation skills and the ability to sell difficult/contentious concepts;Evidence of a successfully completed Police Check.Desirable criteria Evidence of a current valid manual drivers licence;Willingness to travel away from base location to regional and remote areas of Australia;Possess a Blue Card (or if successful provide evidence of obtaining/applying prior to commencement);Registration with a recognised national project management affiliation;Understanding of the political and legal framework in which the organisation operates;"
67,"Automation Specialist - UiPath","Milestone Information Technology","Location not found","Immediate start","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=70#sol=d49c82a1f140b03e69c0db3a9954c38dfc72477f","True","Milestone IT is working with a prominent and forward-thinking healthcare organisation based in Melbourne to find an experienced Automation Specialist. This exciting opportunity will see you drive automation initiatives across a large, multi-site health network that values innovation, inclusivity, and digital transformation.About the Role:As an Automation Specialist, you will play a key leadership role in delivering intelligent automation solutions using UiPath and PowerAutomate. You'll be involved in everything from stakeholder engagement and solution design to mentoring developers and shaping the strategic direction of automation within a growing digital innovation team.Key Responsibilities: Lead the end-to-end development of RPA solutions using UiPath and PowerAutomate. Work with business stakeholders to gather requirements and map business processes. Design scalable, secure, and maintainable automation architectures. Provide technical guidance and mentorship to developers. Ensure adherence to governance frameworks, development standards, and data security. Prepare documentation including Process Definition Documents (PDD) and Solution Design Documents (SDD). Contribute to the organisation’s broader digital roadmap by identifying and recommending emerging technologies.Skills and Experience Required: Proven experience in developing and deploying RPA solutions using UiPath, PowerAutomate, or similar. Strong understanding of automation frameworks, architecture, and integration patterns. Demonstrated experience leading automation initiatives and mentoring development teams. Ability to translate complex business processes into technical solutions. Excellent communication and stakeholder engagement skills. Familiarity with data privacy, security, and compliance in automation.Highly Desirable: UiPath Advanced Developer or PowerAutomate certification. Experience working in healthcare or with EMR systems. Exposure to UiPath AI modules (AI Center, IXP, Document Understanding).Why Apply? Join a digital innovation program that’s transforming how healthcare services are delivered. Collaborate with experts in a supportive and inclusive team environment. Enjoy work/life balance and flexibility while contributing to meaningful change.If you're a proactive automation expert looking to make a tangible impact within a values-driven organisation, we’d love to hear from you.Apply today through Milestone IT and take the next step in your automation career. To apply online, please click on the appropriate link below. Alternatively, for a confidential discussion, please contact Tina M on 03-96706682"
68,"IT Manager","Sharp & Carter Digital and Technology","Location not found","Based in South Eastern suburbs","85366541","https://www.seek.co.nz/job/85366541?ref=recom-homepage&pos=71#sol=3d813c4cce36dd3c8b21285138fccf723dbc8018","True","We are seeking an IT Manager to oversee the day-to-day operations of an organisation’s IT department, ensuring smooth functioning of networks, systems, and infrastructure. This is a smaller subsidiary part of a larger group, so you will need to remain hands on and provide general day to day IT support for 50 users. You will also play a key role working closely with the Managing Director with project management, budgeting and operational responsibilities just to name a few. This role would suit an experienced engineer looking to step up or an existing IT Manager with similar experience in a smaller organisation. Responsibilities and Key Accountabilities: Managing IT Operations: Overseeing the day-to-day operations of an organisation’s IT department, ensuring smooth functioning of networks, systems, and infrastructure. Vendor management: Close collaboration and management of technology vendors including contract negotiations Technology Selection: Evaluating and selecting appropriate hardware, software, and technology solutions to meet the company’s needs. Security and Compliance: Ensuring the security of data and systems, implementing security protocols, and ensuring compliance with regulations and best practices. Project Management: Overseeing IT projects, from planning to execution, and ensuring they are completed on time and within budget. User Support: Providing support to end-users, troubleshooting issues, and ensuring high levels of customer satisfaction. Disaster Recovery and Business Continuity: Developing and maintaining plans for data backup, recovery, and continuity in case of IT system failures. Reporting and Documentation: Keeping records, generating reports, and communicating IT performance and status to upper management. Requirements: Bachelor’s degree in information technology, Computer Science, or related field. Strong knowledge of IT systems, infrastructure, networks, virtualization and Microsoft administration Certification(s) in ITIL, CISSP and/or PMP is preferred. Proficiency in project management methodologies. Excellent problem-solving and analytical skills. Knowledge of cybersecurity and data protection principles."
69,"Group Head of AI & Data","Tasmea Corporate Services Ltd.","Location not found","Opportunity to lead AI adoption and shape the digital future of Tasmea","85371070","https://www.seek.co.nz/job/85371070?ref=recom-homepage&pos=72#sol=495b0fe3cd1ac5abe8d32d42c3f5adf21fce090e","True","Accelerate transformation. Drive insight. Lead innovation.Tasmea is seeking a strategic and results-driven Group Head of AI & Data to lead enterprise-wide transformation through advanced analytics, automation, and insight. Reporting directly to the Executive Team, this high-impact leadership role will embed AI-enabled decision-making, streamline systems, and drive measurable improvements across our national portfolio of industrial businesses.About the RoleIn this newly created position, you will:Lead the selection, deployment, and governance of AI tools and platforms across the Group.Build and embed a cross-functional, insight-driven digital capability.Integrate predictive analytics and machine learning into pricing, forecasting, and operations.Improve resource and asset utilisation through data-driven decision-making.Champion ethical AI use and uphold robust data governance standards.Drive commercial performance, operational efficiency, and strategic innovation.About YouYou are a top-tier digital leader with:A track record of delivering enterprise-level AI, data, or transformation programs.Strong commercial acumen and experience delivering measurable outcomes.Leadership skills to embed system-wide change and build digital maturity across teams.Deep knowledge of data strategy, platform integration, and predictive modelling.A mindset focused on innovation, accountability, and continuous improvement.What We OfferExecutive package: Competitive salary + Super + STI + LTIDirect impact across 20+ companies in a growing national groupLeadership role with flexibility and support from the Executive TeamOpportunity to lead AI adoption and shape the digital future of TasmeaAbout TasmeaTasmea is a collective of specialist companies delivering engineering, maintenance, and industrial services across mining, energy, utilities, and construction. Our Group is powered by the values of People, Safety, Skill, Service, Care, and Systems—and driven by the transformation of insight into impact.Are you ready to lead the future of data, insight, and innovation?Apply now and help us shape a smarter, faster, and more intelligent Tasmea."
70,"IT Delivery Manager | Performance and Process | ITIL","Robert Half","Location not found","6d ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=73#sol=046531f423879553ffbfbc2e7946ac953b91467b","True","The CompanyLeading education provider who have demonstrated impressive growth. To enable further growth our client has recently restructured their IT function and created this new position. The RoleYou will be responsible for the delivery of IT services, optimising the performance of IT operations by working closely with service delivery teams, analysing operational performance, and recommend strategies to ensure continuous improvement.You will focus on:Improving service desk operationsStreamlining processesManaging vendor contractsDocumenting and managing IT assetsChange ManagementDeveloping key performance indicators (KPIs) to measure and enhance the effectiveness of IT services.On offer is the opportunity to work alongside the Head of IT to drive change in a growing organisation within a newly created role.This position offers hybrid working solutions, with an expectation of attending the office 3 days a week. Your ProfileYou will have extensive experience in delivering IT solutions and services, having led IT Operations and operated within ITIL environments to drive process optimisation and performance management.You will have proficiency in process mapping and continuous improvement within a large, complex environment and lead change.Working closely with service desk operations to identify trends, maintain knowledge bases and analysing metrics is also experience you will bring.ITIL certification would be beneficial. Apply TodayPlease send your resume by clicking on the apply button.Learn more about our Melbourne recruitment services: http://www.roberthalf.com.au/recruitment-agency-melbourne Job Reference No: 06810-0013254024 - TWBy clicking 'apply', you give your express consent that Robert Half may use your personal information to process your job application and to contact you from time to time for future employment opportunities. For further information on how Robert Half processes your personal information and how to access and correct your information, please read the Robert Half privacy notice: https://www.roberthalf.com/au/en/privacy. Please do not submit any sensitive personal data to us in your resume (such as government ID numbers, ethnicity, gender, religion, marital status or trade union membership) as we do not collect your sensitive personal data at this time."
72,"Technical Business Analyst","AC3 Pty Limited","Location not found","Step into a visible role driving outcomes across a broad landscape","85451284","https://www.seek.co.nz/job/85451284?ref=recom-homepage&pos=75#sol=8a976a34df0d6c76727e997471ecc9b346538763","True","About the role We're looking for an experienced Technical Business Analyst to support a federal government client on a strategic piece of work: delivering a 3-year Technology Roadmap and Capability Strategy across their infrastructure portfolio. This is a key role focused on collecting and synthesising inputs from across enterprise infrastructure environments – including cloud, data centre, network, IAM, digital workplace, and vendor management – to support strategic decision-making and build a cohesive roadmap. You will be responsible for Collect and consolidate inputs across cloud, on-prem, storage, network, IAM, workplace, and related infrastructure areas Lead strategic workshops and facilitate inputs from architects, engineers, and commercial leads Map contracts, platforms, and services against timelines and capability streams Identify major decision points (e.g. renew/replace/migrate) and ensure they're time-boxed with supporting context Deliver a strategic roadmap artefact covering 12/24/36 month views Work closely with internal teams to ensure roadmap aligns with business priorities and technology direction What you'll need to be successful Strong background as a Technical Business Analyst in large enterprise or, preferably, in public sector environments Broad understanding of infrastructure domains including cloud (Azure/AWS), on-prem, networking, storage, IAM, EUC Comfortable engaging with a range of stakeholders from engineering to commercial Comfortable navigating complex, decentralised organisations and independently pull together a clear, structured output from lots of moving parts Experienced in turning complex technical/commercial inputs into strategic artefacts Excellent documentation and stakeholder engagement skills Nice to have Previous experience in government or regulatory environments Familiarity with large-scale vendor/contract landscapes Baseline or NV1 security clearance Before applying - This hybrid role requires majority on-site work in Sydney CBD. Only candidates based in NSW or willing to relocate promptly will be considered."
74,"Process Business Analyst","Talenza","Location not found","8d ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=77#sol=7842c5fb05875161ae5858ae4685202e6690fc74","True","Junior-Mid Level Process Business Analyst | BankingLocation: Sydney (Hybrid - 2-3 days in office) Rate: $500-$700 per day | $100,000-$120,000 + super (FTC)Step into a high-growth role within a leading Australian bank. We're looking for a motivated and hands-on Process Business Analyst to join a dynamic operations-focused transformation program. This is a fantastic opportunity for someone with 3-5 years' experience looking to build on their BA skills in a supportive, fast-paced banking environment.Key Responsibilities:Analyse and document current state (AS-IS) operational processesAssist in designing and documenting future state (TO-BE) processes and workflowsEngage with operations and business stakeholders to gather requirements and identify pain pointsSupport process improvement initiatives across risk, compliance, and customer operationsWork closely with senior BAs, project managers, and SMEs to drive continuous improvementMaintain process documentation and support implementation planningIdeal Candidate:3-5 years' experience as a Process Business Analyst or Process AnalystExposure to banking or financial services operations (e.g. contact centres, processing teams, customer service)Strong understanding of operational processes and service deliveryWorking knowledge of process mapping tools (Visio, Lucidchart, or similar)Excellent communication, stakeholder engagement, and documentation skillsTeam player with a willingness to learn and growWhy This Role?Gain exposure to major transformation projects in bankingMentorship from experienced senior BAs and program leadsFlexible hybrid working arrangementsCompetitive daily rate - great step up for junior to mid-level candidatesCandidates must be based in Sydney with full work rights and willing to attend the office 2 days per week. Interstate or overseas applications won't be considered."
79,"Business Analyst","Talent – Specialists in tech; transformation & beyond","Location not found","12 Month Contract + Likely Extensions","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=82#sol=53b90050eae474589b81ca9308e44a37575ad73f","True","Talent International is searching for an experienced Business Analyst to join our Queensland Government client based in Brisbane CBD on a 12 month contract with potential for extension.// Initial contract until 24/07/2026 + Likely Extensions // 2 Days WFH Per Week // Brisbane CBD Location This role sits within a newly established directorate focused on Information Governance and Digital Enablement, working across a range of strategic and operational initiatives designed to uplift data quality, business processes, and reporting capabilities across the department.The role: You'll collaborate with senior project managers, change managers, and fellow BAs as part of a dynamic team embracing Agile and Lean methodologies. This isn't your typical technical BA role - it's ideal for someone who wants to engage in broader business strategy and transformation.Responsibilities:Lead and contribute to business analysis activities across strategic and operational initiativesSupport application development and backlog prioritisationContribute to roadmap development and planning activitiesFacilitate workshops and lead stakeholder engagementTranslate complex information into clear, concise documentation and user storiesRequirements: 5+ years of Business Analyst experience, ideally within Queensland GovernmentStrong communication skills, both verbal and writtenExperience with Agile and Lean methodologies (Scrum certification desirable)Ability to build strong relationships and navigate evolving environmentsData analysis skills (SQL Server or similar) highly regardedHow to Apply: To find out more, please ""Apply for this job"" or contact James Grierson or Mats <NAME_EMAIL> / <EMAIL> over 30 years Talent has been redefining the contracting experience with industry leading support, exclusive contractor benefits & a world-class digital platform ENGAGE to access it all. Apply today to see how we can elevate your careerFor a list of all vacant positions, please see our website www.talentinternational.com."
80,"Senior Developer & Application Support","Cox Purtell Staffing Services","Location not found","Core Microsoft stack work with strong application ownership","85421409","https://www.seek.co.nz/job/85421409?ref=recom-homepage&pos=83#sol=69e0128325faf3d1ef60215af09eeb3a3fe9ca4c","True","Senior Developer - .NET | Dev & App Support📍 Sydney CBD (Hybrid) | Long-term contractThis role defines full-stack Microsoft engineering with a strong focus on application support, custom development, and platform stability. From building out new functionality in .NET and Azure - to maintaining the complex integrations and Dynamics 365/CE-based business applications that power the organisation - you'll work across every layer of delivery.You'll join a lean, high-impact team inside a large and complex enterprise. It's the kind of environment where your code and your troubleshooting skills both matter - and where you'll be trusted to own application health, performance, and continuous improvement end-to-end.🛠 What you'll work with:* .NET Framework & .NET Core - app logic, integrations, legacy support* C# - backend services, REST APIs, logic layers* MS SQL Server - core enterprise database* BizTalk Server - current primary enterprise integration layer* Dynamics 365 / CE & SharePoint - critical business platforms you'll support and extend* Azure App Services & Logic Apps - cloud-native builds and workflow automation* Azure DevOps - CI/CD and repo management* ReactJS - in play on newer front-end builds✅ What you'll be doing:* Maintain and support core applications across both custom code and OTB platforms* Build new features and integrations in .NET, Azure, and Dynamics* Investigate, troubleshoot, and resolve issues in production systems* Collaborate closely with Tech Leads and BAs to shape technical outcomes* Uphold quality through structured testing, documentation, and peer reviews💡 Why this role:* Core Microsoft stack work with strong application ownership* Deep involvement across support, build, and integration* Contract role with the longevity of perm - 5+ year programs* Sydney CBD base with hybrid flexibility (Weds anchor day)📍 Location: Sydney CBD (Hybrid)🗓 Working Model: Wednesday anchor day in the office. Other days flexible.📄 Contract: 12-month rolling💰 Rate: Up to $725 p.d.📩 Interested? Send me your CV - let's talk."
81,"Service Delivery Manager","Cognizant Technology Solutions Australia Pty Ltd","Location not found","Central Location w/ great employee perks","84840486","https://www.seek.co.nz/job/84840486?ref=recom-homepage&pos=84#sol=8c971b0d7e642d703ffb0c332dc7b7f162ffaf7c","True","About Cognizant: What makes Cognizant a unique place to work? The combination of rapid growth and an international and innovative environment! This is creating many opportunities for people like YOU — people with an entrepreneurial spirit who want to make a difference in this world. At Cognizant, together with your colleagues from all around the world, you will collaborate on creating solutions for the world's leading companies and help them become more flexible, more innovative, and successful. Moreover, this is your chance to be part of the success story. What your day-to-day will look like and the experience to succeed: Responsible for the end-to-end successful execution of large multi-year program, serving as the single point of contact for both the client and CT leadership regarding program execution. Thought partner for the client, helping to strategize and structure transformation initiatives. Role will involve integrating multiple service lines to ensure the customer's program objectives are met, driving program-level activities, and leading offshore delivery team. Strategize and structure transformation initiatives to drive automation and Gen AI initiatives Drive program-level activities, including detailed planning, risk management, mitigation, scope, change & budget management, and release management. Lead cadence and steering committee meetings with the client. Engage in meaningful conversations with the clients technical and management SMEs, including architects and managers. - Define, track, and optimize program success criteria, metrics, and KPIs. Proven experience in successfully leading multi-year, multi-team, large programs Experience working in an onsite-offshore model with delivery teams spread across geographies. Strong conflict management and dispute resolution skills. You'll receive an excellent salary and benefits package for your knowledge, expertise and flexibility. At Cognizant, taking care of employees is a priority: You can pursue innovative career tracks and opportunities· You can enhance your professional development through education and dedicated training· We’ll give you the skills you need to keep pace with the changing workplace while our compensation, benefits and wellness packages help you stay healthy and plan for the future. 1 day Volunteering Leave per year Beyond minimum Pay Parental Leave Next Steps: If you feel this opportunity suits you, or Cognizant is the type of organization you would like to join, we want to have a conversation with you! Please apply directly with us. For a complete list of open opportunities with Cognizant, visit http://www.cognizant.com/careers. Cognizant is committed to providing Equal Employment Opportunities. Successful candidates will be required to undergo a background check."
82,"IT Support Technician","Logic Solutions","Location not found","1d ago","85526415","https://www.seek.co.nz/job/85526415?ref=recom-homepage&pos=85#sol=cf65f2e97fbae37d3520b24be09bb847b3cc43ab","True","Does technology run in your blood? Are you passionate about learning new technologies and furthering your career? Look no further! We are here to help you.Our team are provided with an excellent work environment and our dynamic approach to service keeps IT exciting.Duties & Responsibilities• Manage inbound support calls, identify & qualify client issues• Undertake troubleshooting and rectification of client issues based on service levels• Undertake incident tracking with event log monitoring, alert handling and reporting• Utilise internal systems to support, monitor and maintain client systems such as updates, patching, anti-virus/ anti-spam, back up & disaster recovery• Report activities and logs related to client system issues• Undertake Server/ PC audits & manage IT inventory• Undertake Systems Patch Management• Liaise with third party vendors on behalf of clients• Attend training as required and have a commitment to on going professional developmentWe are searching for an individual who has• I.T Qualifications or relevant work experience.• A good understanding of IT Service Delivery• Experience in trouble shooting and solving complex problems.• Excellent communication skills and presentation• Desire to work with a dedicated and flexible team• Efficient time management skillsI.T Specific Skills• Windows Server Technologies / Windows Desktop Support• Microsoft Cloud Technologies (Microsoft 365, Azure)• Networking / Internet Technologies• Hardware / System Building• Microsoft Office Functionality and integration"
84,"Sr Technical BA (Azure Integration) - Melbourne","AYAN INFOTECH PTY LTD","Location not found","7d ago","85251982","https://www.seek.co.nz/job/85251982?ref=recom-homepage&pos=87#sol=8cb43b7092cc1297354070176dfb095c35683c03","True","Ayan Infotech are looking for a Senior Technical BA for an Azure Integration program for one of our clients in Melbourne. Due to the nature of the work involved, we can only consider candidates who hold full unrestricted work rights in Australia.Title: Sr Tech BA Location: Melbourne (Hybrid - 2 days in office) Type: Permanent full timeRequired Skills and Experience:Experience: 10+ yearsRequirements Gathering & Story WritingProficiency in capturing API/Integration-centric requirements from business stakeholdersWriting clear, testable user stories and acceptance criteria in JiraStakeholder CommunicationTranslate technical API/Integration concepts into business-friendly language (optional)Act as liaison between product owners, engineering, QA, and external partnersProcess ModellingCreate and interpret data flows, sequence diagrams, and integration mapsIdentify upstream/downstream dependenciesAPI/Integration Concepts & ToolingStrong understanding of RESTful APIs, JSON, XML, HTTP status codesHands-on experience with Postman, Swagger (OpenAPI), and API documentation toolsUnderstanding of API versioning, security (OAuth, API keys), throttling, etc.Azure DevOpsUsing Azure Boards for backlog refinement and sprint planningFamiliarity with Azure Pipelines and release workflowsManaging work items, queries, and dashboardsIntegration KnowledgeFamiliar with Azure API Management (APIM), Logic Apps, Function Apps or Service BusUnderstand integration patterns and data transformation conceptsData SkillsAbility to read/write SQL queries for data validation or reportingComfortable working with logs or payloads for troubleshootingDevOps & Cloud AwarenessUnderstanding of CI/CD pipelines and deployment stages (Dev ? QA ? Prod)Awareness of infrastructure-as-code, containers (Docker), and microservicesFamiliarity with monitoring/logging tools (e.g., Azure Monitor, App Insights)Soft SkillsAnalytical thinking and attention to detailAdaptability in fast-moving Agile environmentsStrong verbal and written communicationProactive problem-solving and critical thinkingWe will be able to contact only shortlisted candidates. We thank you in advance for your interest in one of our job ads.www.ayaninfotech.com"
85,"Senior Frontend Developer - Perth","DXC Technology Australia & New Zealand","Location not found","Suitable candidates must hold Australian Working Rights","85313387","https://www.seek.co.nz/job/85313387?ref=recom-homepage&pos=88#sol=4cf7758de4f645245be8b56429fcc8f1b599f44e","True","DXC Technology (NYSE:DXC) - where brilliant people embrace change and seize opportunities to advance their careers and amplify customer success. People are the heart of our business. Senior Frontend Developer – MiningWe are seeking an experienced Senior Frontend Software Developer to join our Perth delivery team. The successful candidate will deliver complex, high-performance web applications. Working as part of a cross-functional squad, they will bring expertise in React and Redux to build intuitive and responsive user interfaces that use real-time data and geospatial content.The Senior Frontend Developer will lead technical delivery of major features, set standards for performance and usability, and mentor others in frontend engineering best practices.Responsibilities:· Build performant, reusable UI components using React and Redux Implement an interactive 3D map and layers using Babylon.js to support various features.· Collaborate with Tech Leads and Architects on architectural decisions and frontend design patterns.· Drive high standards in code quality and component re-use across several environments.· Contribute to testing strategy, including regression, unit, and integration testing.· Identify and resolve frontend bottlenecks and performance issues.· Mentor junior frontend developers and contribute to a culture of continuous improvement.Skills:· 3+ yearsʼ experience as a Software Developer or similar role.· Substantial experience with the following is a key requirement: ReactJS, TypeScript and CSS· Experience C# .NET Core, WebGL.· Experience with game development or related technologies is highly regarded.· Experience with microservice architectures and containerisation: Docker, Kubernetes.· Familiarity with Linux systems advantageous."
86,"Multiple Cloud Engineers (Remote)","Entain Group PTY LTD","Location not found","Fully remote supported — work from anywhere you choose in Australia.","85221827","https://www.seek.co.nz/job/85221827?ref=recom-homepage&pos=89#sol=230781459ebfc69d3b40674423f59cc783f25414","True","Start you game…The Cloud Team is responsible for designing, building, and supporting a highly available and fault-tolerant platform for our customer-facing and corporate systems, with a focus on autoscaling and observability. A typical day involves a mix of project and business-as-usual work, where you will be delivering solutions utilising established and bleeding-edge technology. Our favourite time of year is Spring Carnival, where our platform experiences a massive surge of traffic, and all our hard work is put to the test! Culminating in the race that stops the nation, we operate at a massive scale with hundreds of servers running thousands of service instances to handle millions of requests per second.Where And How You Can Work...This position can be based in any of our offices across Australia or New Zealand. We embrace flexibility by offering a hybrid working model, allowing employees to spend two days a week in the office to promote work-life balance and accommodate individual preferences. This role is open to remote candidates across Australia & New Zealand.Your Day-to-Day... Work effectively both as an individual and as part of a project team to deliver cloud-based projects. Design elastically scalable and highly available systems that utilise modern, cloud-based architectures and solutions. Deploy applications to cloud environments with Infrastructure as Code and Config as Code patterns and toolsets. Deploy monitoring and alerting solutions to track resource utilization, performance metrics, and platform/application health. Ensure all work delivered complies with regulatory requirements, and security best practices and has a strong focus on privacy. Collaborate with other Cloud Engineers, Site Reliability Engineers, and Software Engineers including mentoring, sharing knowledge, and writing documentation. Confirm and clarify requirements, including offering alternative paths to achieve a better outcome. Debug, diagnose, and troubleshoot faults and manage incident responses. Contribute documentation and process improvements to streamline efficiency. What You'll Bring... Knowledge of core AWS services (IAM, VPC, EC2, S3, RDS, etc.). Experience with Infrastructure as Code tools (Terraform in particular, but experience with others such as Ansible, Puppet, Pulumi, etc. is also highly regarded). Experience with containerisation (Docker) and Linux. Experience with Kubernetes (and other CNCF landscape tools). Strong scripting skills (Shell, Python, etc.). Understanding of TCP/IP networking and TCP and HTTP reverse proxying. Strong troubleshooting and investigation skills (DNS, TCP, and HTTP/CDN, etc.). Experience with CI/CD tools (GitLab, Argo CD, etc.). Entain Lifestyle... Embrace your need for a work-life balance through our 9-day fortnight. Celebrate the joy of starting a family with 12 weeks of paid parental leave. Indulge in a range of wellness benefits through our wellness calendar, including heavily subsidized gym memberships and other paid-for services. Enjoy the convenience of a free onsite barista and a fully stocked fridge and pantry, ensuring breakfast and lunch are taken care of. Company-wide events designed to honour and celebrate the outstanding achievements of our exceptional team. We LOVE Entertainment, and Racing and Sports are at the heart of everything we do. So, it only makes sense that we provide access to staff giveaways to sporting and racing events. Who we are? At Entain Australia & New Zealand, we're on a mission to revolutionise the world of sports and gaming venue entertainment. We're the home of Australia and New Zealand's best gaming and tech talent, with a passion for pushing boundaries and creating unforgettable experiences. We have a host of leading brands under the Entain umbrella – our customers can experience the best of sports betting and entertainment with Ladbrokes Australia and neds, where together they provide thrilling avenues for sports enthusiasts and racing aficionados alike. Through Entain Venues, our venue partners offer non-stop entertainment through our line-up of brands from the Australian Poker League and SportsPick to the challenges of InnQUIZitive and many more. We're also proud to support racing and sport in New Zealand through our 25-year strategic partnership with TAB NZ. The TAB brand in New Zealand connects enthusiasts to the heart of the action, whether it's horse racing, greyhounds, or a variety of thrilling sports. A culture where you belong As a global employer, Entain is committed to providing a safe, fun, and inclusive culture where our people feel like they truly belong. We are a multicultural business that values, celebrates and respects individual differences, so whatever your sexuality, gender, gender identity, ability, age, race, religion, or belief, you will have a voice here, and the space to do your best work. Our diverse internal networks provide support for you to express your views and make a positive difference. For more information, please reach out to <NAME_EMAIL> or hit apply now."
87,"Functional Application Support Analyst (Aged Care Application)","Epicor Software (M) Sdn Bhd","Location not found","18d ago","85036119","https://www.seek.co.nz/job/85036119?ref=recom-homepage&pos=90#sol=3386948b884eee7d209e989a4b572b065fdb2485","True","About Epicor At Epicor, we’re truly a team. Join 5,000 talented professionals in creating a world of better business through data, AI, and cognitive ERP. We help businesses stay future-ready by connecting people, processes, and technology. From software engineers who command the latest AI technology to business development reps who help us seize new opportunities, the work we do matters. Together, Epicor employees are creating a more resilient global supply chain. We’re Proactive, Proud, Partners. Whatever your career journey, we’ll help you find the right path. Through our training courses, mentorship, and continuous support, you’ll get everything you need to thrive. At Epicor, your success is our success. And that success really matters, because we’re the essential partners for the world’s most essential businesses—the hardworking companies who make, move, and sell the things the world needs.Competitive Pay & Benefits Health and Wellness: Comprehensive health and wellness benefits designed to support your overall well-being. Internal Mobility: Opportunities for mentorship, continuing education, and focused career goal setting, with 25% of positions filled internally. Career Development: Free LinkedIn Learning licenses for everyone, along with our Mentoring Program to boost your personal development. Education Support: Geographically specific programs to balance the cost of education with the benefits of continued learning and personal development. Inclusive Workplace: Collaborate with a diverse team in an inclusive, global workplace that fosters innovation and celebrates partnership. Work-Life Balance: Policies built on mutual trust and support, encouraging time off to rest, recharge, and reconnect. Global Mobility: Comprehensive support for international relocations and permanent residency processes. About the role:As an Application Support Specialist at Epicor, you would enjoy analyzing customer issues, both big and small. You’ll rapidly assess, diagnose, and troubleshoot application inquiries to solve customer challenges. As a member of our global support team, you will aim for excellence and efficient resolutions.Your days will be filled with helping our customers identify and troubleshoot application support incidents via service ticket or e-mail. What you will be doing: Confer with customers via phone, email, or chat to diagnose technical/system problems, troubleshoot, repair, and test solutions, fully documenting and escalating if necessary. Follow procedures to troubleshoot client-server software application issues involving Microsoft SQL and web servers, Respond to situations where standard procedures have failed to isolate or correct system/software failures using customer-centric interpersonal skills and technical product knowledge. Stay up-to-date with software updates/changes to provide timely and accurate solutions to customers. Address concerns from other departments such as customer experience or sales. Contribute to and use the department’s knowledge base articles and FAQs. What you will likely bring: 3+ years applicable experience and demonstrated success/knowledge. Experience in application support role in aged care application/finance/manufacturing.Strong customer experience skills and knowledge of specific system application fundamentals and business processes. Strong problem-solving and troubleshooting skills, with experience in a support environment with high call volumes. SaaS or e-Commerce experience, with knowledge of Microsoft SQL, ERP applications, or analytical tools being a plus."
88,"Head of Applications and Data","xceltium","Location not found","Lead strategy across applications; data and integration.","85269657","https://www.seek.co.nz/job/85269657?ref=recom-homepage&pos=91#sol=2148805e2757d9fbf4be599301161e1767ffee11","True","Ready to lead transformation, simplify applications, and help a business work smarter?Step into a role where your expertise doesn’t just keep things running - it drives growth, empowers smarter decisions, and positions a business to set new standards for its industry.As Head of Applications & Data, you’ll set and deliver a multi-year strategy to transform fragmented systems and siloed data into a cohesive, modern, and business-driven technology ecosystem. You’ll lead the rationalisation of a broad, SaaS-heavy application portfolio - removing overlap, enhancing security, and ensuring every platform is future-ready and delivering true business value.You’ll support enterprise architecture initiatives, helping build a unified, scalable foundation that enables agility, advanced analytics, and future AI innovation. This isn’t just about updating systems - it’s about enabling a truly data-driven, AI-powered future backed by a CEO who wants to invest if it makes commercial sense. You’ll strengthen ESG reporting, build the groundwork for predictive insights, and unlock faster, safer, and smarter ways of working.In this role, you’ll also guide and develop a small, close-knit team (currently one direct report, with potential to grow), empowering them while staying hands-on when needed. Working alongside a highly respected CIO who values mentorship, growth, and new ideas, you’ll be supported to expand your own leadership and technical horizons. Designed as a true 2IC position, this role offers a structured personal development plan and a clear and genuine path toward a future CIO role if that’s your ambition.What makes this opportunity unique is the breadth and exposure it offers. In a scaling, medium-sized business, you won’t be confined to narrow technical lanes - you’ll stretch across strategy, architecture support, business partnering, governance, and operational improvement. You’ll develop a truly holistic leadership skill set that will set you apart long-term, whether you continue here or step into future roles elsewhere.All of this in a business that truly values its people. They have a 94% employee engagement score, a strong family-first mindset, and are a finalist in the 2025 Best Place to Work Awards. You’ll enjoy hybrid flexibility, wellness programs, five extra leave days, and a culture that celebrates growth, curiosity, and balance.What you’ll shape and deliverYou’ll lead application strategy and rationalisation, partnering with senior leaders to ensure a consolidated, secure, and business-focused portfolio. You’ll drive data and integration strategies to unify systems, enable better reporting, and build a strong foundation for advanced analytics and AI. Your business analysis expertise will help design modern, scalable solutions that transform processes and empower better decision-making at every level.What you’ll bring· 5+ years’ experience leading a functional IT value stream - ideally across Enterprise Applications, Data, or Integration. Perfect for a Business Systems Manager ready to step into their first Head of role, or for a current Head of looking for their next exciting transformation and challenge.· Proven ability to manage and optimise application portfolios in close partnership with business leaders.· Strong expertise in integration and solid understanding of data architecture and warehousing.· A hands-on, humble, and people-focused leadership style - able to flex seamlessly from strategy to technical detail.About the companyA privately owned, highly respected Australian business with nearly 50 years of success delivering complex, impactful projects across multiple sectors. Combining Tier 1 capability with a genuinely people-first, humble culture, they are now embarking on a major digital and data transformation to enable future growth and strengthen market leadership.Next stepsIf your CV isn’t quite ready, that’s fine - send what you have or contact Steven Fulop at xceltium on 0418 994 446. Every application will receive a prompt response."
