Job_Number,Title,Company,Location,Posted,Job_ID,Link,Quick_Apply_Available,Description
1,"Senior Software Developer","Propellerhead","Auckland CBD; Auckland","We prioritise technical excellence; curiosity; and forward-thinking design","85360175","https://www.seek.co.nz/job/85360175?ref=recom-homepage&pos=1#sol=632f11ba639d8a7cfaf83ee0e12bd6ac388ef65e","True","The Role:As a Senior Software Developer in our core delivery team, you'll help craft interesting, challenging systems that are beyond routine feature work, alongside a team of highly skilled peers who value software craftsmanship and autonomy.You’ll work in an environment that prioritises technical excellence, curiosity, and forward-thinking design. We are looking for someone who is excited by emerging technologies, specifically has an interest in edge computing and distributed architecture, and wants to make a tangible difference in the way people interact with digital systems.We offer a culture built on trust, continuous learning, and meaningful impact. You'll join a team that values integrity over ego, and clarity over complexity.Propellerhead operates a hybrid working model (work from our office and from your own home). While you must be based in Auckland, we warmly welcome applications from Developers across New Zealand and internationally who are planning to relocate to Auckland (please specify relocation plans in your application).If you are ready to work on real problems with real impact, and help lay the groundwork for what comes next - we would like to hear from you.Role Responsibilities:Defining, together with your team, and in line with our innovation strategy, the set of technologies and programming languages that would best serve the solution you are designing and building.Active participation in all aspects of software solution design, including code structure, deployment architecture, DevOps, automated testing and integration of third party systems.Collaborating with client stakeholders and with fellow Propellerhead Developers, Analysts, and Solution Architects, to create a shared understanding of the solution design and roadmap delivery in line with agreed budgets.Constructing software at every layer of the stack, from user interfaces, back end services, data repositories, interoperability, infrastructure-as-code, deployment pipelines, automated testing and system monitoring.Providing second-line support of the systems you build and actively seeking ways to keep your software secure, performant and reliable.Passing on your learning of new technologies or techniques to the broader organisation as part of our knowledge sharing programme. Skills and Experience Required:7 + years of experience as a Software Developer, including experience working with enterprise-level complexity.High proficiency in .NET and ReactJS.Experience in C#, JavaScript or TypeScript, HTML, CSS, Node.js, Angular or similar front-end frameworks.Hands-on experience deploying to Azure and/or AWS, including participation in building CI/CD pipelines.Experience with asynchronous services and/or stream processing.Experience with Kubernetes and/or TerraformPrevious experience with Go or RustDemonstrable experience in software design for architecture with highly interdependent features.Experienced using techniques such as user stories to express the work to be done as business outcomes.You can produce clear and concise design documents.You have excellent written and verbal communication skills to translate between requirements and technical tasks.About Propellerhead:Propellerhead is a leading software services company that designs, delivers, and maintains large-scale, custom-built digital platforms across a range of technologies. We place emphasis on producing technology which is meaningful, has value, and can make a difference in the world. We foster long lasting and trusting relationships with all our clients, and we have worked with a diverse range of commercial, non-profit, and government organisations over our 20 years in the market. How We Build Software:We build software by structuring solutions in a product-centric manner and focusing on continuously delivering improvements from a backlog of features. These features are taken from real stakeholder needs linked to clear objectives. As a result, our digital platforms are a mixture of custom components and external services which produce a powerful and engaging experience when combined.Benefits: Be part of an innovative world-class team, with a culture of learning and collaboration.Contribute to interesting, diverse, and commercially minded work for well-respected clients.Opportunity for professional growth and development.Fortnightly knowledge sharing sessions, with lunch.Competitive salary. Employee Share Purchase Scheme offering.This role is Auckland based, some office attendance is required. Work within our beautifully designed office and/or from home (hybrid working model).Propellerhead is an equal opportunity workplace which is free from discrimination. Cover Letter:Please note - only applications with a cover letter will be reviewed.It's very important to us that you include a cover letter with your application - it's your chance to tell us more about who you are and what you value. While we love AI and all its wonders, we kindly ask that you refrain from using it in your application. We're excited to learn about your unique experiences and ideas, so please apply as your authentic self! Please also include your relocation plans if currently living outside of Auckland, New Zealand."
3,"Junior / Intermediate Frontend Web Developer","NetYourJob","Albany; Auckland","Modern tech stack and the tools you need to do your best work","85286305","https://www.seek.co.nz/job/85286305?ref=recom-homepage&pos=4#sol=8cdcbd8364a2e241493518067bc7c95ef5db4dca","True","North Shore, Auckland, Full Time, IT JobsPlease Quote Reference Number 12539Key role bringing our online presence to lifeModern tech stack and the tools you need to do your best workBe part of a dynamic team based on the North Shore We're Hiring! Are you ready to help shape the digital future of a company with real impact?Tactical Solutions is on the hunt for a passionate, capable, and creative Junior / Intermediate Front-end Web Developer to be based in our North Shore, Auckland office and bring our online presence to life - one intuitive, elegant interface at a time.We're not your typical tech team. At Tactical Solutions, we protect public safety by delivering proven, world-class operational solutions. We take our responsibility seriously - but we're also a team that thrives on innovation, trust, and collaboration. If you want to work somewhere with meaning, where you can grow your career while building excellent digital experiences, read on.What You'll Do:As a key member of our in-house development team, you'll play a pivotal role in consolidating multiple websites into one seamless BigCommerce platform, ensuring our digital customer experience is second to none.Your day-to-day will include:Building responsive, accessible interfaces with HTML, CSS, JavaScript, and ReactJSMigrating content from WordPress to BigCommerceApplying best-practice UI/UX principles to create user-first web experiencesCollaborating with sales, marketing, and tech teams to bring business needs to lifeMaintaining and optimising our product catalogue and BundleB2B setup on BigCommerceManaging product sync between NetSuite and BigCommerce using Celigo integrator.ioDesigning email templates and automation workflows in Mailchimp (transitioning to Dotdigital)Creating wireframes, mockups, and prototypes using Figma, Adobe Creative Cloud, or SketchUsing AI tools like ChatGPT or GitHub Copilot to streamline and elevate your development workflowContributing to code reviews and helping us continuously improve how we workAbout You:You're a detail-oriented developer with a sharp eye for design and usability. You love clean, scalable code and know how to balance innovation with business needs.We're looking for someone who:Has 1-3 years' experience in frontend development or strong portfolio workIs confident using ReactJS, JavaScript, HTML5, and CSS3Has experience with CMS and e-commerce platforms (BigCommerce a big plus!)Is familiar with tools like Vite, Webpack, or GruntUnderstands modern UI/UX principles and responsive designCommunicates clearly - whether in person, on paper, or in codeWorks well in a team, but is also self-driven and accountableHas great problem-solving skills and takes real pride in the quality of their workHas a good sense of humour, positive energy, and a genuine ""team first"" mindsetBonus points for experience with Oracle NetSuite, Celigo integrator.io, or BundleB2B.What We Offer:A full-time permanent position (40 hours per week) with a growing and stable New Zealand companyA modern tech stack and the tools you need to do your best workA collaborative, supportive team with strong leadership and visionFlexibility when needed, with a focus on outcomes over hoursOpportunities to grow your skills across web, eCommerce, marketing tech, and automationA workplace culture that values professionalism and peopleImportant to know:You'll need a clean police check before starting, and occasionally may be required to work outside regular hours or travel within New Zealand. We offer a safe, inclusive, and respectful environment in line with all NZ Health and Safety standards.Ready to Apply?If you're excited by the opportunity to create great digital experiences, help build something meaningful, and work with a team that values your contribution - we'd love to hear from you.Apply now with your CV, a short cover letter, and a sample of recent work or a portfolio link."
4,"Systems Administrator","Connexa Ltd","Auckland CBD; Auckland","Modern new offices near Victoria Park","85148426","https://www.seek.co.nz/job/85148426?ref=recom-homepage&pos=6#sol=20290a6be08a264ff6e8f37543494e4950aecaaa","True","Connexa is New Zealand’s leading mobile tower infrastructure company, driven by a mission to enable a more connected digital future for Aotearoa New Zealand. We do this by designing, building and managing mobile sites across the country for telecommunications providers (like Spark and 2degrees) to expand their coverage efficiently and sustainably. Our tower portfolio of 2,400 covers Cape Reigna to Bluff and is the largest in New Zealand.Kiwis’ mobile data use has increased dramatically in recent years and demand for mobile services continues to grow. The Connexa team is focused on ensuring Aotearoa is keeping up with this increasing demand, both today and into the future, by building an additional 800 towers over the next eight years to support the required growth in coverage and capacity.Working at Connexa will challenge you, broaden your horizons and give you the opportunity to make your mark on a totally new segment of New Zealand’s telecommunications industry. As a System Administrator at Connexa you will be the go-to expert, at the heart of our core systems; managing service requests, handling configuration updates, supporting projects and end-users, system maintenance, and continuous improvement of our systems. Utilising your strong technical background, problem-solving skills, and the ability to work effectively in a multi-stakeholder environment; you'll champion user adoption, engage with colleagues at all levels, stay ahead of the curve with the latest system releases, and collaborate with cross-functional teams to create seamless integration solutions.We’re building a great culture at Connexa together, and your role will play a key role in that.Key Responsibilities include: Primary liaison & user support System monitoring and optimisation Project support and BAU Activities Reporting and data management Collaboration and vendor management Requirements 3-5 years of experience in system administration and integration. Salesforce Administrator certification or equivalent accreditation is highly desirable but not required. Strong Excel skills, including advanced functions, pivot tables, and macros. Proficient in SQL, with a key focus on querying databases to improve data quality and perform data analysis. An understanding of Microsoft SharePoint and experience in administering and supporting it. The ideal candidate can use PowerShell and/or REST APIs to automate SharePoint administration tasks. Strong project and time management skills, with the ability to handle multiple tasks in a multi-stakeholder environment. Commitment to delivering excellent customer service, focusing on outcomes and user solutions. Ability to translate technical language into plain English for non-technical audiences. Excellent interpersonal communication skills, with a proven ability to engage effectively with a range of stakeholders and users of varying IT knowledge. High attention to detail, with the ability to create quality documentation and reports. BenefitsIn return for your efforts, you will be rewarded with a competitive salary, Southern Cross health insurance, life insurance, marram holidays as well as an annual wellbeing day.Working at Connexa will challenge you, broaden your skillset and give you the opportunity to make your mark on a totally new segment of New Zealand’s telecommunications industry.To express your interest APPLY online today!"
5,"Software Developer","BrightSpark Recruitment","Rodney & North Shore; Auckland","Great North Shore location","85448865","https://www.seek.co.nz/job/85448865?ref=recom-homepage&pos=7#sol=6ada9e942f1b69203d11d44e1174b4e548959929","True","ABOUT THE COMPANY:This SaaS start-up has a one of a kind product and to say they are going places would be an understatement. Having secured impressive funding, they are in growth mode and have an enviable client list. With an awesome working environment and culture, this is a company you'll definitely want to be a part of.ABOUT THE ROLE:Working in a small but growing development team, you will work with an extremely impressive CTO. This is a chance to work on cutting-edge technology, in a fast paced environment and in an exciting domain.ABOUT YOU:We are seeking someone with at least 2-3 years of relevant experience, and who is self motivated and keen to learn. For you, working in tech is not just a job, but a passion. You'll need to bring skills in all, or at least most, of the following:- .NET Core - TypeScript - MongoDB - Kubernetes - AWS - Terraform - React.js - FlutterWHAT'S IN IT FOR YOU?This is an opportunity to be part of one of New Zealand's most exciting start-ups. They offer the chance to work on challenging projects, building innovative solutions. This is a full time in office role on the North Shore. WHAT'S NEXT?This will be a popular role, so apply today! For more information please contact Lisa Cooley on 021 029 81422."
8,"Senior Golang Engineer","Bidfood Limited","Epsom; Auckland","Shape Go adoption in a global business","85021650","https://www.seek.co.nz/job/85021650?ref=recom-homepage&pos=11#sol=f0b0f6ed48cc57bc8d441a37f93b095a8f3d57a5","True","About BidOneBidOne is a local New Zealand company with a truly global presence. Every day, thousands of businesses around the world place orders with their wholesale suppliers through our ecommerce and CRM platforms, spanning five continents.At BidOne, we care about building solid software – and strong teams. We have a collaborative, supportive culture with a shared sense of responsibility for delivering technology that matters. Our software reflects the real needs of the foodservice businesses we support, and because we sit at the heart of their operations, reliability and trust are core to everything we do.“Never lose an order” isn’t just a slogan – it’s how we work, together.The RoleWe’re looking for a Senior Golang Engineer to join us on contract as we modernise and replatform our ecommerce system. This is a 24-month fixed term contract with potential to extend, based in Auckland with a hybrid working model.You’ll be part of a friendly and capable engineering team working on something that really matters: rebuilding our backend architecture using Go, modern cloud-native tooling, and a clean, scalable microservices design.While this is a contract role, you’ll have a voice in shaping how Go is adopted across the business – from patterns and practices to mentoring others new to the language. This role would suit someone who enjoys both writing quality code and helping others do the same.Key ResponsibilitiesTechnical LeadershipContribute to defining Go standards and patterns as part of our broader replatforming journeyShare your experience to help other engineers grow – particularly those new to GoLead by example in code quality, collaboration, and deliverySupport architecture and design discussions with practical, Go-specific insightsMicroservices DevelopmentDesign and build Go-based microservices that are secure, scalable, and easy to understandBuild APIs and asynchronous components in line with best practicesEnsure observability, performance, and maintainability are baked in from the startEngineering PracticesSupport modern practices like test-driven development, domain-driven design, and CI/CDUse AI-assisted tools (Cursor AI, GitHub Copilot, ChatGPT) to enhance quality and productivityParticipate in code reviews, documentation, and continuous improvement conversationsCollaboration & DeliveryWork closely with product owners, QA, DevOps, and other engineers to deliver customer valueProvide technical guidance in a supportive and inclusive wayHelp build a positive, respectful culture of learning and engineering craftWhat We’re Looking ForMust-Have5+ years of professional Go development experienceExperience designing and building distributed systems and APIsUnderstanding of microservices, REST, and event-driven architecture (Kafka, NATS, Pub/Sub)Hands-on experience with Docker, Kubernetes, CI/CD workflows, and automated testingA collaborative working style and a willingness to mentor othersAbility to support engineering standards in a growing teamNice-to-HaveExperience with GraphQL or Search as a ServiceFamiliarity with observability tools (OpenTelemetry, Prometheus, Grafana, Datadog)Experience with SQL and NoSQL databasesPrevious experience in ecommerce, foodservice, or supply chain platformsWho You AreA thoughtful engineer who values clean, maintainable codeA system thinker who enjoys breaking down complexityA clear communicator who builds trust through knowledge-sharing and empathyComfortable mentoring others, and open to learning from them tooFocused on outcomes and collaboration over ego or hierarchyWe strongly encourage applications from anyone who is underrepresented in tech. If this role sounds like you but you’re not sure you tick every box — we’d still love to hear from you.What We OfferA high-impact role in a major replatforming initiativeLong-term contract stability with a modern tech stackHybrid working environment (3 days in-office time in Auckland required)A respectful, collaborative team with a no-blame cultureThe opportunity to help shape how Go is used across a global businessA company that values people just as much as codeHow to ApplyPlease submit your application through the following URL: https://bidone.bamboohr.com/careers/71?source=aWQ9Mg%3D%3DOnly applications received via this link will be considered."
9,"IT Systems Administrator","Youtap Ltd","Auckland CBD; Auckland","Auckland CBD Location","85181566","https://www.seek.co.nz/job/85181566?ref=recom-homepage&pos=12#sol=5172582ad70d73fb6228c2eab2b1e23007d8ee07","True","Youtap Technology is seeking a detail-oriented and proactive IT Systems Administrator to join our dynamic team. In this role, you will be responsible for maintaining, configuring, and ensuring the reliable and secure operation of our IT infrastructure across both cloud and on-premises environments.You will support day-to-day IT operations, manage Microsoft cloud services, implement system enhancements, and provide user support across our business. A strong understanding of cybersecurity and compliance is essential, as you will help maintain our PCI-DSS and ISO27001 standards.If you have been involved in the installation of AWS, MS Azure or Google GCP Cloud environments and the implementation of cloud solutions (Kubernetes Clusters, Helm Carts, PostgreSQL databases etc) that is a plus, although the primary role is as a IT systems and network administrator for Youtap.Youtap provides secure, banking-grade software solutions to banks and financial institutions. We require candidates to hold relevant Microsoft and/or Cybersecurity certifications. Please include all certifications in your application.Note: Applications are open to New Zealand citizens and permanent residents only.We check for AI Generation, all AI Generated CV's and applications will be automatically disqualified.RequirementsKey Responsibilities: Microsoft Cloud Administration Manage and maintain Microsoft 365 applications, including Azure Active Directory, Microsoft Intune, and Microsoft Defender. Administer user accounts, access policies, device management, and security protocols. Manage license administration for third-party applications such as Jira, Confluence, Bitbucket, Figma, and others. Endpoint Management Set up and manage desktop PC imaging, deployment, patching, and software updates. Manage user access and licensing for development, design, finance, and project management software. Ensure endpoint security and compliance across all devices. Cloud Installations (AWS, MS Azure, Google GCP) Any experience with cloud setups is a plus including the deployment of Kubernetes clusters and Helm Charts and the implementation of PostgreSQL databases. Network Management Configure, deploy, and maintain Ubiquiti routers, switches, and related network infrastructure. Set up and manage VPN access for employees using Ubiquiti platforms. Manage secure VPN connectivity to Youtap customers and across global office locations. Cybersecurity Monitor for security threats, perform vulnerability assessments, and implement remediation plans. Set up and manage antivirus solutions (e.g., Microsoft Defender) and intrusion detection/prevention systems. Ensure timely security patching across all systems and endpoints. Team Leadership Provide technical oversight and day-to-day supervision of two network engineers based in Indonesia. Assign tasks, track deliverables, and mentor junior technical team members as needed. Documentation & Compliance Maintain up-to-date IT infrastructure documentation, security policies, and operational procedures. Ensure compliance with industry standards including ISO 27001 and PCI-DSS, as well as internal IT governance policies. Support & Troubleshooting Provide Level 2 and Level 3 IT support, resolving escalated technical issues promptly. Coordinate with external vendors for support and issue resolution when required. Required Skills & Experience: Minimum 5 years of experience in IT systems and network administration. Minimum Certifications Required; Microsoft Certifications (Azure AD, Security Operations), CompTIA Security+ Expertise in Microsoft 365 suite, Azure AD, Microsoft Intune, and Microsoft Defender. Understanding of Software Development applications and associated support applications. Hands-on experience with Ubiquiti network hardware (routers, switches, UniFi Controller). Proven capability managing desktop imaging and endpoint patching. Solid understanding of VPN technologies, network security protocols, and cybersecurity management. Excellent communication and leadership skills, capable of managing a small offshore team. Working Environment Position involves coordination with offshore teams; occasional out-of-hours work may be required. Ability to travel occasionally to offshore offices if necessary. BenefitsThis position is a full time role and suitable package will be negotiated with the individual"
10,"Senior Front-End Developer","Younity","Location not found","React / NextJS / Headless CMS","85512267","https://www.seek.co.nz/job/85512267?ref=recom-homepage&pos=13#sol=039b60b9655d698816504e965fc4260a94bcb1e2","True","About the OpportunityWe’re working with one of New Zealand’s most recognisable retail organisations on a large-scale digital transformation — modernising some of the country’s most visited online platforms. As they move away from legacy systems like Sitecore/.NET, they’re rebuilding their customer-facing websites from the ground up using React, NextJS, and a new Headless CMS. This is your chance to join the front-end stream of a strategic programme of work that will shape digital experiences used by millions of Kiwis every week. What You’ll Be Doing Building high-performing, scalable websites using ReactJS and NextJS Integrating with a new Headless CMS (ContentStack preferred) Supporting the migration from a monolithic CMS to a composable JAMstack architecture Collaborating with cross-functional teams across design, product, and engineering Contributing to the evolution of front-end best practices and reusable components What You’ll Bring Deep hands-on experience with React and NextJS (ideally both) Experience working with Headless CMS platforms – ContentStack, Contentful, Sanity, Storyblok, etc. Familiarity with Vercel or other modern hosting/deployment tools Experience working in enterprise-level or high-traffic website environments Strong communicator, team player, and solutions-focused thinker Why You’ll Love This Contract This is a high-impact role where your work will directly contribute to a nationwide digital upgrade. You'll work in a future-focused tech stack with support from talented engineers and product teams. It’s the kind of work you’ll be proud to have on your portfolio. Key Details Contract Length: 6 months (likely to extend into 2026) Start Date: ASAP (within 2 weeks ideal) Location: Auckland-based | 3 days per week onsite If you’re a React/NextJS expert looking to make an impact on large-scale digital platforms — this one’s for you. Apply now. Please note, a valid NZ work visa is required to apply for this role."
11,"Data Analyst","Grinding Gear Games","Henderson; Auckland","Flexible work hours","85154829","https://www.seek.co.nz/job/85154829?ref=recom-homepage&pos=15#sol=77164e6ed7790048d45d8fae8f5ece9aafc85dd7","True","Grinding Gear Games are seeking a Data Analyst to join our team.Location: On-site in Henderson, Auckland, New ZealandRelocation assistance (for the right candidate, if needed)Full time position - 40 hours a weekThe position includes the following duties:Extract and analyse user data from different sourcesRun database queries across different mediums for requested dataWorking with all teams around Grinding Gear Games to understand what information they need and turning this into a plan to collect and learn from relevant data.Present data and analysis for reviewWe are seeking someone with:Two or more years experience in data analysis rolesStrong knowledge of SQL, ideally PostgreSQLExperience in scriptingPluses:Experience with our game, Path of ExileHow to applyIf you meet the above requirements, please email your resume and a cover <NAME_EMAIL> with your name and role you are applying for in the subject line.No phone calls or agencies, please."
12,"Data Engineer","The Instillery","Auckland CBD; Auckland","Innovative culture and flexible work","85291826","https://www.seek.co.nz/job/85291826?ref=recom-homepage&pos=16#sol=c9ea8e44606abe62ca406c708e812ca4b1518b67","True","We are open to candidates in Auckland, Hamilton and Wellington.We’re on the hunt for a Data Engineer - a smart problem solver, cloud-native thinker, and all-round data wrangler. This is a hands-on role for someone who knows how to turn messy data into meaningful insights, thrives on solving technical challenges, and is comfortable working across multiple cloud platforms and client environments. You’ll be part of our high-performing Cloud & DevOps team, building modern data platforms and supporting our customers with their data journey.We’re a technology company that provides multi-disciplined professional services and business consultation across infrastructure, applications, cloud, security and modern work solutions to some of New Zealand's best organisations. At The Instillery, we like people who challenge the status quo, ask “why not?”, and take real ownership of what they do.What you’ll be doing:Designing and building modern, cloud-native data platforms and data pipelines using tools like Fabric, Synapse, Databricks and ADF. Proficiency in scripting languages such as Python, with a focus on automation and orchestration. Solving complex data problems by turning raw information into scalable, fit-for-purpose solutions for our clients.Supporting pre-sales by shaping technical approaches and writing clear, practical scopes of work.Collaborating with clients and internal teams to deliver secure, high-performing platforms that enable real-time insights and data-driven decisions.Continuously improving – from optimising performance and reliability to staying current with emerging cloud and data technologies.What we’re looking for:Tertiary qualifications in Computer Science, Information Systems, Statistics, Informatics, or relevant industry expertise .5+ years of experience building and managing cloud-based data solutions (Azure, AWS, GCP – we like them all).Proven track record with ETL tools, scripting (Python), data modelling and cloud analytics tooling. Someone who gets modern data architecture – data lakes, delta lakes, and real-time processing.Comfortable with APIs, DataOps, version control, and translating business needs into working systems.A natural communicator, someone who can engage confidently with both technical and non-technical stakeholders.A proactive mindset – you're curious, collaborative, and not afraid to jump in and figure things out.What’s in it for you:Work with some of Aotearoa’s most forward thinking organisations on high-impact data projects.A collaborative, high energy environment where your skills will be valued and stretchedFlexibility, trust and autonomy – no micromanaging, just good humans doing great work.Professional development, tech certs, and plenty of growth opportunities. A solid salary, wellbeing perks, your birthday off, and a team that actually celebrates the wins.If you're ready to build better data solutions, be part of a future focused engineering team, and work somewhere that values curiosity, hustle and heart – apply now. Let’s make some data magic happen."
13,"Systems Engineer","M2M Recruitment","Auckland Airport; Auckland","12 Month Fixed Term Contract","85385109","https://www.seek.co.nz/job/85385109?ref=recom-homepage&pos=18#sol=35483ed59640c22604b9e2d6deabe9a2ec0b944e","True","Systems Engineer 12 Month Fixed Term Contract | Immediate Start | Auckland Airport LocationA leading transportation provider is looking for a hands-on, technically skilled Systems Engineer to support their national operations. This is a fixed-term role (initially 12 months with potential to extend) that blends BAU support with exciting project delivery across cloud, cybersecurity, and business systems.If you're someone who thrives in a busy, collaborative environment, and knows your way around Azure, M365, and system performance then read on!About the role:You'll join a tight-knit Shared Services IT team, providing technical support and leading system improvements that keep things running smoothly across Aotearoa. This role reports to the Infrastructure Manager and supports nationwide users and systems.Key responsibilities:Deliver remote and on-site IT/ICT support (cloud + on-prem)Manage system performance, service desk escalations, and BAU operationsSupport cloud infrastructure (Azure), Microsoft 365, Entra ID, and IntuneExecute data/reporting initiatives and implement new business systemsMaintain cybersecurity posture and technical documentationBuild relationships across depots and senior leadershipContribute to nationwide IT projects and enhancementsProvide occasional after-hours supportWhat you'll bring:3+ years as a Systems Engineer (or similar role)Solid experience with Azure, M365, Windows Server, and Active DirectoryStrong knowledge of SQL queries, data modelling, and reporting toolsA proactive, problem-solving mindset and excellent communication skillsAbility to manage competing priorities and work independentlyNice to have:Experience with SSIS/SSAS modules, SharePoint, Intune, Power BIBackground in transport, logistics or other regulated environmentsWhat’s on offer:A varied role where you’ll flex across BAU and project delivery Room to grow – gain exposure to enterprise-level infrastructure and cloud strategyA collaborative, forward-thinking team that values initiative and ideasNational impact – support systems that keep NZ’s public transport movingReady to roll?Apply now with your CV or reach out to Diella from M2M Recruitment for a confidential chat."
14,"Senior Java Developer","Talent Army - Winners of Innovation in Recruitment & Recruiter of the year 2023","Auckland CBD; Auckland","Auckland Based (Hybrid Role)","85526557","https://www.seek.co.nz/job/85526557?ref=recom-homepage&pos=19#sol=4a50f725ef0ef2a7c0ba2728735b1597317fe1c8","True","About the Company: Our client is a global leader in digital trading platforms, serving customers in over 70 countries. They are dedicated to providing independent, neutral, and transparent market solutions that support efficient trading practices worldwide. About the Role:This is a rare opportunity to step into a pivotal engineering role at a company that’s reclaiming full ownership of its product and platform development.Having previously relied on vendors, they’re now building an in-house engineering function from the ground up, and this role sits right at the heart of that transformation. As a Java Full Stack Software Engineer, you will design, develop, and maintain web applications and APIs. You’ll work across the full stack with Java (Spring Boot), React, and MySQL, delivering scalable, high-performing solutions in a collaborative and innovative environment. Skills & Experience: 5+ years of experience with Java (Java 17/21, Spring Boot 3). Proficient in front-end development with React, TypeScript, and Tailwind CSS. Strong knowledge of RESTful API development and integration. Experience with relational databases (MySQL) and writing optimized SQL queries. Hands-on experience with CI/CD pipelines and monorepos (Turborepo). Familiarity with testing frameworks like JUnit, Cypress, or Playwright. Agile experience (Scrum or Kanban). Why Work Here? Innovative, collaborative culture with a focus on continuous improvement. Global exposure working with international teams and clients. Opportunities to work with modern tech stacks. Competitive salary and benefits. Flexible working options, including Work from Home. Emphasis on health, safety, and employee wellbeing. Ready to make an impact? Apply now and we would love to chat in more detail."
15,"Analyst","Build People Ltd","Auckland CBD; Auckland","$95K salary + career development opportunities","85212323","https://www.seek.co.nz/job/85212323?ref=recom-homepage&pos=21#sol=c74a809cc82898ed048992d7c365f45a921ce58a","True","My client is a large and progressive supplier in the hardware and building industry with many recognisable brands and a national presence. With strong existing relationships, and an excellent culture based on the training and development of all staff, this is a business you want to get behind. Together, we’re on the hunt for an Analyst who doesn’t just love data but knows how to bring it to life, influence stakeholders, and support smart decision-making across the business. This isn’t your typical behind-the-scenes Analyst role – we’re looking for someone who thrives in a fast-paced, collaborative environment and can confidently work with senior managers and cross-functional teams across the business. So, if you're someone who can translate numbers into clear insights, enjoys a bit of banter, and is ready to take ownership of your role, I’d love to hear from you! About the role: Designing and putting the data infrastructure in place for the business Developing and maintaining Power BI dashboards and reports Compiling, analysing, and interpreting sales data to identify trends, opportunities, and areas for improvement Contributing to the development of sales strategies by delivering actionable insights and performance analysis to guide the sales team Preparing regular reports and insights on product master data, purchase volumes, supplier stock levels, costings, supply, and broader supply chain metrics Providing analytical support for key category initiatives, including core range reviews, pricing strategies, and supplier performance evaluations Assisting the sales team with day-to-day support, including sample coordination, order processing, and backend administrative tasks About you: You are a commercially minded Analyst with proven experience in a Sales, Commercial, or Business Analyst role You are a confident and assured personality with the ability to effectively present information and make it user-friendly You have the commercial ability to identify patterns and gaps in data, look for efficiencies, and identify sales opportunities You have strong Excel and data visualisation skills – confidence with Power BI is essential English is your first language – excellent written and verbal communication is essential Benefits: An ambitious candidate will recognise the benefits of this opportunity and will be excited by the full scope of the challenge! You will report directly to the CEO, who will provide you with excellent leadership and support. This is a unique role where your impact on the business will be significant, so in this respect, it is an incredible career move for the right person! How to apply: Please apply now with your CV and I will contact you if your background ticks the boxes for what we're after."
16,"Senior IT Support Engineer","Absolute IT Limited","Manukau & East Auckland; Auckland","Senior IT Support role with career progression opportunity","85433529","https://www.seek.co.nz/job/85433529?ref=recom-homepage&pos=23#sol=367521a3f005baf7cc7f255f32f61029278f1d09","True","Senior IT Support role with career progression opportunitySupportive, fun, and high-performing team cultureCompetitive remuneration plus health insurance About the CompanyOur client is a fast-growing Managed Services Provider (MSP) supporting businesses across New Zealand and Australia. With a collaborative and forward-thinking culture, they’re committed to delivering exceptional customer experiences and fostering professional growth.They are now seeking a Senior IT Support Engineer with a broad technical skill set and a passion for customer service. This role is ideal for someone who enjoys solving complex problems, thrives in a fast-paced environment, and is looking to grow into a Systems Engineer role.What You'll DoDeliver high-quality Level 2 support across a wide range of technologies, with some exposure to Level 3 tasks.Act as a trusted advisor to clients, ensuring a smooth and professional support experience.Troubleshoot and resolve issues related to Windows Server, networking, Azure, Microsoft 365, and security tools.Collaborate with internal teams to escalate and resolve more complex issues.Contribute to continuous improvement initiatives and knowledge sharing.What You’ll Bring3+ years of experience in an IT support role, within an MSP environment (MSP experience is a must. Strong technical knowledge across:Windows Server & Active DirectoryNetworking (DNS, DHCP, firewalls, routing)Microsoft 365 & Azure (VMs, virtual networking, Azure AD, WVD, PaaS)Security tools and endpoint protectionExcellent communication skills – both written and verbal – with a customer-first mindset.A proactive, solutions-focused attitude and the ability to explain technical concepts clearly to non-technical users.A desire to grow your career into systems engineering and beyond.Why You’ll Love It Competitive salarySouthern Cross Health InsuranceCareer progression into Systems Engineering rolesOngoing training and professional developmentA supportive, fun, and high-performing team cultureHow to Apply: Click APPLY and include your CV and cover letter OR for further information <NAME_EMAIL> or <EMAIL>. Reference number: 143069. Please note, we can only consider applicants that are legally entitled to work in New Zealand at the time of application."
17,"Senior Analytics Developer","Walkerscott Limited","Wellington Central; Wellington","4d ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=24#sol=28baaf60206011d1894acba0f4b4a0822a4c83bd","True","About Walkerscott We’re an action oriented and passionate Microsoft Business Applications and Analytics partner. Our focus is to help businesses leverage Microsoft technology to drive growth, innovation and efficiency in their businesses. We want engaged passionate individuals who want a working environment that will help them be their best. Through a love of innovation, flexibility, big ideas and an opportunity to challenge convention our people thrive. We’re an agile, action-oriented consultancy who pride ourselves in being the team that gets stuff done. In short, we exist to make clever happen. The RoleThis role is responsible for technical leadership, documentation and development of solutions within our Analytics practices across New Zealand and Australia. As a senior member of our Analytics practice, you have a responsibility for the technical design and development of project deliverables aligned with our best practices & for ongoing support and documentation tasks for our clients. In your capacity as a senior developer, you will be supported by the Practice Lead to work towards the overall objectives of the practice and under their guidance ensure a high level of customer satisfaction and solution quality. Success of this role will be determined by the four metrics of, quality of project deliverables, technical documentation of deliverables, technical leadership with our clients and adherence to Walkerscott policies & ways of working. Required Competencies Technical Expertise: 5+ years working on commercial analytics projects across Australia and NZ countries. 5+ years working in a consultancy developing Microsoft reporting solutions. An understanding and passion for the Microsoft Dynamics 365 Business applications. Experience in reporting and data extraction from ERP & CRM (business) systems. Understanding of financial reporting. Commercial experience in Microsoft Fabric. Organisational: Strong desire to help people and proactive approach to providing support. Uncluttered and highly organised working style. Ability to work on multiple projects at the same time. Eye for detail and a focus on quality. Good organisational task management and prioritisation skills. Communication: Proactive communication style that ensures all stakeholders are well informed of project progress. Empathic style and good listening skills to be able to build rapport and bonds with your team and customers. Ability to adapt working style from supportive to authoritative as required. Ability to communicate effectively, both written and verbal, across geographically and culturally dispersed teams. Quality Focussed: Excellent attention to detail in all activities including communications, systems, project documentation and deliverables. Ability to deliver quality work to deadlines. Tackle problems head on with a solution focussed mindset. Ability to create, work to and lead others in Walkerscott best practices."
18,"Insights Analyst","SKY TV","Auckland CBD; Auckland","Turn data into strategy across content; product; and marketing","85384905","https://www.seek.co.nz/job/85384905?ref=recom-homepage&pos=26#sol=148b1a4f3080bae8344eb54b3325a67359917204","True","KO WAI MĀTOU I ABOUT SKY Welcome to Rangiata | Sky, the ultimate entertainment destination that connects Kiwis to the stories and sport they love, in ways tailored just for them. Our Sky crew work tirelessly to ensure we are Aotearoa's most engaging and essential media company. We're incredibly proud of our achievements, but that doesn't mean we're satisfied. As innovators, we constantly challenge ourselves to redefine boundaries and adapt to the ever-changing marketplace. Come be your authentic self at Sky, where we actively listen and adapt to create shared experiences that excite both our customers and our crew. As an Equal Employment Opportunity (EEO) employer, we proudly champion inclusivity and diversity as our core values, and we are open to applicants of all gender identities to feel welcomed and supported during the application process. We have included a ""Diverse"" category for those who identify as non-binary, genderqueer, genderfluid, or any other gender identity outside the binary system of male/female. TE TŪRANGA I THE ROLE: We’re looking for a curious, analytical, and creative thinker to join our Data & Business Intelligence team as an Insights Analyst. In this role, you’ll transform data into meaningful insights that shape strategy and drive business decisions across Sky. You’ll be at the forefront of analysing audience behaviour, content performance, and market trends to provide actionable insights to stakeholders across content, marketing, partnerships, digital, product, and finance. With the freedom to shape the scope of the role, this is an exciting opportunity to make it your own, build trusted relationships, and help Sky deepen its understanding of what really moves the needle. We’re not just looking for someone to crunch the numbers — we’re after someone who can tell the story behind them. You’ll be supported by a collaborative and curious team that loves solving problems and thinking big — and you’ll work in an environment that champions experimentation, innovation, and growth. This isn’t just an analyst role — it’s your chance to lead insight-driven change across Sky. Key responsibilities will include: Analyse complex datasets and build visual dashboards to uncover key insights that support content, marketing, product, and commercial decisions Develop predictive models, audience segmentations, and churn forecasts to support business performance Translate data into clear narratives and actionable recommendations that influence decision-making Collaborate across teams to design and implement measurement frameworks and A/B testing strategies Champion innovation through identifying growth opportunities, behavioural trends, and data-driven improvements. NGĀ PŪKENGA ME NGĀ WHEAKO I WHAT YOU WILL BRING : We’re looking for someone who thrives on turning data into impactful insights, is commercially savvy, and confident working across a fast-paced media environment. You'll bring technical expertise, strong communication skills, and a passion for storytelling through data. ESSENTIAL 3+ years’ experience in analytics, data modelling, and visualisation Proficiency in SQL and either R, Python, or similar for statistical analysis Experience with Snowflake or similar cloud data warehousing Hands-on experience with customer segmentation, predictive modelling, and data storytelling Confident using tools such as Power BI for dashboards and data visualisation Strong interpersonal skills – comfortable working with senior stakeholders and across teams PREFERRED Experience with Nielsen audience measurement Knowledge of media, entertainment, or subscriber-based industries Familiarity with modern data stack tools such as Segment and dbt Bachelor’s degree in Data Science, Analytics, Statistics, or a related field KO NGĀ HUANGA I WHAT’S IN IT FOR YOU? As part of the Sky crew, you’ll enjoy a range of great benefits – from free Sky, Sky Sport Now and Neon, to discounted Southern Cross health insurance and YMCA gym memberships. We offer free onsite parking at our Mt Wellington and Albany office, and we’re big on supporting your growth with exciting career development opportunities. ME PĒHEA TE TONO I HOW TO APPLY If this opportunity excites you and you want to join us in sharing stories, possibilities, and joy, apply online today. Please note that you must have the right to work in New Zealand or hold a valid work visa for at least 12 months from joining Sky. We’re reviewing applications as they come in, and once we’ve found the right addition to our Sky Crew, we’ll blow the final whistle on this ad. If you’re ready to get off the sidelines and into the action, apply now. At Sky, we recognise that traditional recruitment processes don’t always work for everyone. If you need any adjustments or accommodations to help you through the recruitment journey, we’re here to support you — just reach out to <NAME_EMAIL>. Please note, this email address is for support purposes only and shouldn’t be used to submit job applications. RECRUITMENT AGENCIES - Sky has established partnerships with a small number of preferred recruitment agencies, and we will not engage with unsolicited communication outside of this pool."
19,"Front-End Technical Lead(React)","RWA People","Location not found","Immediate Start; Well Known brands in NZ Market","85456016","https://www.seek.co.nz/job/85456016?ref=recom-homepage&pos=27#sol=48bd8790fb210b1190b88b6a2aae3f2dd78eb582","True","Our client is looking for Lead Front End Developer for a web and mobile e-commerce platform responsible for leading the development and maintenance of high-quality user interfaces, mentoring a team, and ensuring technical feasibility of designs. This role requires strong expertise in React.js, JavaScript, HTML, and CSS, as well as experience with responsive design and mobile-first development. Collaboration with UX/UI teams and backend developers is also crucial. Key Responsibilities: Develop and maintain e-commerce applications: Design, develop, and deploy scalable and high-performance e-commerce applications using React for web and React Native for mobile platforms (iOS and Android). Implement user interfaces: Create intuitive and responsive user interfaces for product catalogs, shopping carts, checkout processes, and user accounts. Integrate with APIs and backend services: Connect front-end applications with e-commerce backend systems, payment gateways, and other third-party services. Optimize performance and user experience: Ensure applications are fast, responsive, and provide a seamless user experience across different devices and platforms. Collaborate with cross-functional teams: Work closely with designers, product managers, and backend developers to translate requirements into functional features. Write clean and maintainable code: Adhere to best practices for code quality, testing, and documentation. Troubleshoot and debug issues: Identify and resolve technical issues and bugs in a timely manner. Required Skills and Qualifications: Proficiency in React.js and React Native: Strong understanding of core concepts, component-based architecture, state management (e.g., Redux, Context API), and lifecycle methods. Experience with e-commerce platforms: Familiarity with common e-commerce features and functionalities. JavaScript proficiency: Excellent command of modern JavaScript (ES6+), including asynchronous programming and functional programming concepts. Front-end development skills: HTML5, CSS3, and responsive design principles. API integration: Experience consuming and integrating with RESTful APIs. Version control: Proficiency with Git and collaborative development workflows. Problem-solving and debugging skills: Ability to analyze and resolve complex technical challenges. Strong communication and teamwork skills: Ability to collaborate effectively with other team members. If this sounds like the opportunity you have been waiting for and you have the skills listed above, please apply today!!!Click Apply Now or call RWA on 09 579 7929 for more information.Only people with the right to live and work in New Zealand may apply for this role.Don't forget to visit our website for more vacancies and job opportunities: www.rwa.co.nzJob Reference number: SD-3941690"
20,"ICT; Systems and Applications Administrator","Lightforce Ltd","Auckland CBD; Auckland","Market leading company within solar industry","85384977","https://www.seek.co.nz/job/85384977?ref=recom-homepage&pos=29#sol=639e95033cae63291b2fdde3a855926a25819e3a","True","At Lightforce Solar we’re redefining the solar industry with a passion in creating a robust, resilient and sustainable ecosystem for NZ through our various solar-focused offerings including solar sales, installation, and power retailing partnerships. As we expand across New Zealand, we proudly serve residential, commercial, and agricultural markets with environmentally responsible energy systems. Our mission is to empower Kiwis to harness the sun’s energy, reduce their carbon footprint, and support sustainable communities. At Lightforce, we believe that clean energy drives thriving communities, and we are committed to building a brighter, greener future for all. About the role:Purpose of the role is to provide operational helpdesk and general administrative support by working with external IT vendors consistently, efficiently and reliably, and to provide System Administration to ensure Lightforce staff has the correct access, licenses and hardware to carry out their job efficiently. You can look forward to the following and more: User & Access Management Automation of Systems, Processes & Tools System Integration & CRM Field & Solar Support Security, Projects & Reporting About you:You bring 2+ years of experience in the solar industry, with a solid understanding of solar installations, products, and technical fault resolution— making you confident in supporting field teams and solar monitoring platforms like GoodWe and Sungrow. • You’ve worked extensively with Simpro, managing user licenses and optimizing its use to streamline solar installation processes. You’re comfortable training and supporting field staff to get the most out of the platform. • You have hands-on experience with Microsoft 365, Active Directory, and Azure AD, and understand the importance of proper user access and security group alignment. • You’re skilled in PowerShell scripting and have used PowerApps and Power Automate to simplify workflows, reduce manual tasks, and improve system efficiency. • You’re familiar with Microsoft Dynamics CRM and have supported its integration with platforms like Simpro and Xero, ensuring smooth data flow and system performance. • You’ve worked in customer-facing roles, bringing excellent communication and interpersonal skills to every interaction—whether it’s resolving a support ticket or coordinating with external IT providers. • A Bachelor’s degree in IT, Computer Science, or equivalent experience backs your technical foundation, and knowledge of cloud platforms like Azure or AWS is a bonus. • You’re detail-oriented, proactive, and always looking for ways to improve systems and support business goals through smart IT solutions.If you have what it takes, apply now!"
21,"Senior Software Engineer","Absolute IT Limited","Wellington Central; Wellington","Core Java & Java EE expertise required","85449954","https://www.seek.co.nz/job/85449954?ref=recom-homepage&pos=30#sol=4bee34e360ea952907b5682e4fae876708d1d13c","True","About the Role Our public sector client are searching for a Senior Software Engineer on a 6 mo. contract to join a small, agile team (4–5 developers) working on project-based workstreams. You’ll be instrumental in designing and building enterprise Java applications, with a preference for cloud-first development. This is a great opportunity to influence architecture, guide best practices, and contribute to an AWS-focused migration. Day-to-Day ResponsibilitiesDevelop and maintain enterprise-level Java applicationsContribute to architecture, design patterns, and DevOps practicesBuild and maintain RESTful services and microservicesCollaborate with cross-functional teams in a fast-paced project environmentSupport the team’s AWS migration effortsSkills and Experience RequiredSolid experience in Core Java and Java Enterprise Edition (must-have)Strong knowledge of Spring, Hibernate, XML, SQLExperience with JSP, JavaScript, CSSSkilled in building REST APIs, microservices, and working with messaging brokersFamiliarity with Maven/Gradle, Jenkins, GitCloud experience – AWS highly desirableStrong problem-solving and multi-tasking skills, with a collaborative mindsetHow to Apply Click APPLY and include your CV and cover letter. Please note, we can only consider applicants that are legally entitled to work in New Zealand at the time of application."
24,"Technical Support Specialist","Freerange Works Ltd","Howick; Auckland","Join a close-knit team; NZ-owned business making a real difference","85421935","https://www.seek.co.nz/job/85421935?ref=recom-homepage&pos=35#sol=1df30483edc2731c0556e8d017c7f91fcfce1b23","True","Are you a technically minded problem solver who thrives on helping others, and wants to feel good about the work you do? We’re looking for a Technical Support Specialist to join our small, friendly team in a role that offers flexibility, purpose, and the opportunity to take real ownership.About the CompanyStringTM is a Total Mobility platform provider approved by Waka Kotahi New Zealand Transport Agency and local councils. The Total Mobility Scheme is administered by various Regional Councils providing subsidised private hire or taxi transport to people who have an impairment which prevents them from undertaking a journey on public transport in a safe and dignified manner. The platform has been running now for over 10 years and covers the whole country from Whangarei to Invercargill. It includes both mobile and EFTPOS technology in the vehicle as well as cloud-based software so accurate GPS information can be collected and lodged with councils for subsidy claims.About the RoleIn this newly-created role, as our Technical Support Specialist you’ll be the key technical contact for drivers and councils. You'll troubleshoot technical issues, manage support tickets, liaise with councils, and lead a small support team. You'll need a strong technical support background, great problem solving and communication skills, and a genuine desire to help others. You’ll also manage one Administrator and help streamline technical operations and administration, continuously identifying efficiencies and improvements.What You’ll DoProvide software and equipment support to our customersLiaise with council contacts to support driver complianceTroubleshoot and resolve, or escalate, technical issuesLead and support an AdministratorImprove internal processes and documentationTake ownership of the technical support functionWhat You’ll BringPrevious experience in technical support, ideally in software user supportA problem-solving mindsetGreat communication skills and a strong customer service ethicConfidence liaising with both technical and non-technical usersAttention to detail and ability to manage tasks independentlyWhy Work With Us?We provide flexibility - work part-time, school hours (6 hours per day, Mon-Fri)We’re Howick-based - work close to homeFeel like you’re part of a worthy cause – our platform helps Kiwis with mobility impairments access safe, subsidised transportWe’re a proudly NZ-owned growth business, established in 2014Join a tight-knit, supportive teamTake ownership in a role that’s yours to learn and growCompetitive remunerationIf this sounds like the role for you, and you’re eligible to work in New Zealand, please click ‘Apply Now’."
26,"Power BI Developer","JOYN","Avondale; Auckland","Varied and exciting BI role within a growing company!","85185762","https://www.seek.co.nz/job/85185762?ref=recom-homepage&pos=38#sol=8a71bcf203de435cce7494e0f37f4118e3a9dee5","True","CloudSquared is a fast-growing Managed Service Provider who puts our customers, and their business requirements at the heart of what we do. On the back of steady, sustainable growth, we're expanding our Data team to bring in an experienced Power BI Developer who thrives on turning complex data into elegant, actionable insights for clients across industries.If you're passionate about transforming data into decisions—and just as confident in front of stakeholders as you are inside Power BI—this role is for you.What You’ll DoAs a Power BI Developer at CloudSquared, you'll sit at the intersection of data, design, and delivery. You'll create data-driven solutions that help businesses make smarter decisions faster. From building compelling dashboards to integrating APIs and automating workflows, your impact will be visible and valued.Your Responsibilities:· Build and maintain scalable, insightful Power BI dashboards and reports with clear data narratives.· Design end-to-end business apps using Power Apps (Canvas and Model-Driven).· Automate workflows and processes using Power Automate (Microsoft Flow).· Integrate multiple data sources including SQL Server, SharePoint, Dataverse, Excel, Azure, and third-party APIs.· Develop and manage REST APIs and web services to support dynamic, integrated solutions.· Partner directly with clients and internal stakeholders to capture requirements, prototype, and iterate.· Optimize for performance, usability, and cross-platform access.· Follow best practices around security, governance, and Power Platform lifecycle management.· Document your solutions thoroughly for maintainability and knowledge sharing.What You BringYou're equal parts developer, data strategist, and problem-solver. You know how to ask the right questions, build trust with clients, and turn ideas into impactful BI solutions.Must-Have Skills & Experience:· 4+ years’ experience working with Power BI and Power Apps in a professional setting.· Advanced expertise in data modeling, DAX, and Power Query (M language).· Solid background working with REST APIs, webhooks, and third-party service integrations.· Strong grasp of ETL workflows and working with both structured and unstructured data.· Experience with SQL Server, Dataverse, SharePoint, Excel, and Microsoft 365 integrations.· Understanding of application lifecycle management, Power Platform environments, and role-based access control.Why Join CloudSquared?· Growth without the bureaucracy – We’re agile, ambitious, and client-obsessed.· Real impact – Your work won’t disappear into a black box. You’ll see it live and in action.· Smart, supportive teammates – Join a high-performing team that’s generous with knowledge and serious about results.· Flexible and balanced culture – our people work smarter not harder with flexibility around hours and hybrid work arrangements.Ready to turn business data into real-world impact? Apply now and become a key part of CloudSquared’s growing Data team."
29,"Head of Engineering/Technical Lead","Franklin Smith","Location not found","5d ago","85362450","https://www.seek.co.nz/job/85362450?ref=recom-homepage&pos=42#sol=b56cc11a17e82aa18b59ba2b256cd0105417f3e9","True","Head of Engineering/Technical Lead Location: New Zealand (NZ Work Visa or Residency Required) We’re looking for a dynamic and technically sharp Head of Engineering to lead a high-performing software team in a fast-moving SaaS environment. This is an exciting opportunity for someone with an entrepreneurial mindset who thrives in a hands-on leadership role and wants to leverage the latest in AI and automation to push product innovation and team productivity to the next level. If you’re passionate about scalable architecture, modern development practices, and mentoring technical talent—this role is for you. What You’ll Be Doing - Team LeadershipMentor and support a team of capable software engineers.Drive team alignment on priorities, goals, and project delivery.Remove roadblocks, support professional development, and build a culture of continuous improvement.Make smart decisions by understanding business context and product objectives.Technical DirectionBring strong architecture and design skills to the table.Provide expert-level guidance in both front and back end development.Champion the use of AI tools and agents in streamlining development processes.Write clean, scalable code—particularly in Angular and TypeScript.Lead technical problem solving, reviews, and best practice adoption.Stay ahead of emerging tools, frameworks, and cloud technologies.Project ExecutionManage timelines and delivery with agility and precision.Collaborate with stakeholders to define scope and refine project goals.Navigate risks, issues, and changing priorities with calm and clarity.Support cross-functional coordination across teams and departments.Communication & CultureCommunicate clearly with team members, stakeholders, and executives.Encourage open collaboration and idea-sharing.Lead by example and foster a positive, high-performing team environment. Tech EnvironmentLanguages & Frameworks: TypeScript, Angular, React, Node.jsDatabases: MongoDB, DynamoDBCloud & Infrastructure: AWSTools: Git, Jira, CI/CD pipelines, Claude, Cursor What You Bring6+ years in full stack development, with solid experience across modern JavaScript frameworks.Confident in object-oriented principles (OOP) and SOLID design.Proven track record designing scalable web applications.Knowledge of document and NoSQL databases.Passion for AI, with a strong grasp of agents, models, and automated development tools.A collaborative spirit, high accountability, and a results-driven mindset.Experience with cross-platform app development is a bonus.You must be a New Zealand resident or hold a valid NZ working visa. Why Join You’ll be joining a company on a growth trajectory across the ANZ and Pacific regions, working on meaningful products that support businesses around the world. This is a chance to lead from the front, make a genuine impact, and fast-track your career as you help shape a forward-thinking tech team using cutting-edge tools and practices."
31,"C# Developer / Systems keeping people safe / Remote option","Enterprise Technology Recruitment Ltd","Location not found","4d ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=45#sol=bbc78c25a0aa26d9dc318ea8205d4400aa21b0da","True","The Company:The company is an innovative, fast-growing, Kiwi-owned technology company dedicated to developing cutting-edge monitoring and data collection systems that protect the health of employees and communities worldwide. They are a medium-sized, rapidly expanding company specialising in IoT based solutions serving global markets. Privately funded and driven by a strong sense of mission, their products consistently outperform competitors and are positioned for significant growth.They believe that people are their greatest asset, and they place immense importance on hiring individuals who share their values and commitment to creating a positive, collaborative, and growth-oriented environment. As they embark on an ambitious product roadmap, they are excited to welcome a passionate and capable C#.Net Software Engineer to join the team and contribute to their mission.The Opportunity:They offer an exciting opportunity for an experienced C#.Net developer with at least 6 months professional experience since graduating with a relevant degree.You will have the chance to work on a modern tech stack, including .NET and you will be encouraged to work as broadly across all the company’s technology streams as you feel comfortable with.They continually seek new ways to enhance their capabilities and deliver faster, better tools to customers. What We’re Looking For:We are seeking a developer who has demonstrated academic excellence and has gained professional experience as a developer. You will be skilled in C# and .Net. We value individuals who possess excellent communication skills and thrive in a collaborative team environment.What We Offer:In return, you will join a nurturing and supportive environment, with the opportunity to be mentored by some of Auckland’s best developers. We are committed to your development and growth as a software engineer and offer exciting projects, career development opportunities, and the chance to make a real difference.With a globally distributed client base and a rapidly growing business, the company offers not only interesting and varied work but also a competitive salary, flexible working arrangements, and a workplace culture that is second to none.This role is Hybrid with 1-2 days in the office pwEligibility:To be considered for this role, you must hold New Zealand Citizenship or Permanent Residency and be based in Auckland.Excellent spoken English is essential.Apply Now:If you’re eager to take an exciting career next step in a dynamic and supportive team, apply now and be part of a company that’s making a real impact on the world.Apply now to find out more or call Barry or Craig on 0274992733"
33,"Senior Developer - Sharepoint","Datacom","Wellington Central; Wellington","Training and growth opportunities","85532795","https://www.seek.co.nz/job/85532795?ref=recom-homepage&pos=48#sol=aa9c1821aaf5e3231324e180e615ed65eb0f40ca","True","Our Why Datacom works with organisations and communities across Australia and New Zealand to make a difference in people’s lives and help organisations use the power of tech to innovate and grow. About the Role (your why) A Senior SharePoint Developer is required to join Datacom’s high-performing Enterprise Information Management team—an expert group that leads the way in Enterprise Content Management, SharePoint Online development, Microsoft 365 integration, and automation solutions. This team is trusted by government agencies and large enterprises across New Zealand to deliver scalable, secure, and compliant digital workplaces. You'll be joining a team known for their speed, technical excellence, and ability to navigate complex regulatory environments while enabling clients to get real value from their unstructured data.We have offices across New Zealand and Australia, but we’re looking for someone based in New Zealand. We have a flexible work culture - we like to bring people together in person when we can, but we are mindful of the benefits of working from home for work/ life balance. We therefore leave it to you and the team you join to figure out what works best! We’re looking for someone who has deep technical knowledge, loves solving problems, establishes rapport with customers quickly, and is a team player. What you’ll do As the Senior SharePoint Developer, you will be responsible for: Designing and developing solutions for customers to solve business problems, Running manual and automated SharePoint build processes using our established patterns and practices, and Assisting with migrations of content into SharePoint Online. What you’ll bring We’re looking for a seasoned SharePoint Developer with deep expertise in building, configuring, and extending SharePoint Online environments. You’ll be confident working across Microsoft 365, with strong experience in Power Platform and integration work. You'll also bring an eye for quality, reusability, and maintainability in solution development—delivering value to our clients quickly and effectively.Core Skills (Must-Have) Strong requirements gathering and stakeholder engagement skills End-to-end SharePoint Online development and configuration Building modern intranets using out-of-the-box and supported third-party components Integration of SharePoint with other systems and services Development of PowerApps and Power Automate solutions using SharePoint and Dataverse Development of custom web parts and extensions using SharePoint Framework (SPFx), PnP, React, and REST APIs for modern SharePoint Online solutions Experience with SharePoint migrations (legacy to online, and tenant-to-tenant) Familiarity with tools like AvePoint or ShareGate for migration and governance Proficient in using PowerShell for automating SharePoint Online administration, deploying solutions, managing permissions, and streamlining Microsoft 365 operational tasks Solid understanding of Microsoft 365 ecosystem and its extensibility Desirable Skills (Nice-to-Have) Experience contributing to pre-sales: including RFP/RFT responses, estimates, and solution design Ability to create and present solution demonstrations and proof-of-concepts Exposure to agile delivery methods and tools like Azure DevOps Technical documentation experience, including solution design and as-built documentation Why join us here at Datacom? Datacom is one of Australia and New Zealand’s largest suppliers of Information Technology professional services. We have managed to maintain a dynamic, agile, small business feel that is often diluted in larger organisations of our size. It's our people that give Datacom its unique culture and energy that you can feel from the moment you meet with us. We care about our people and provide a range of perks such as social events, chill-out spaces, remote working, flexi-hours and professional development courses to name a few. You’ll have the opportunity to learn, develop your career, connect and bring your true self to work. You will be recognised and valued for your contributions and be able to do your work in a collegial, flat-structured environment. We operate at the forefront of technology to help Australia and New Zealand’s largest enterprise organisations explore possibilities and solve their greatest challenges, so you will never run out of interesting new challenges and opportunities. We want Datacom to be an inclusive and welcoming workplace for everyone and take pride in the steps we have taken and continue to take to make our environment fun and friendly, and our people feel supported. Requirements#LI-Hybrid"
34,"Business Analyst - Operations","Private Advertiser","Mount Wellington; Auckland","10d ago","85211977","https://www.seek.co.nz/job/85211977?ref=recom-homepage&pos=50#sol=24ba3aa560df0b3dd742c94dff16b19f6504c5e2","True","About the RoleIs Math's your thing? Do you love data analysis problem solving?Well we have the role for you!We are seeking a talented and enthusiastic Business Analyst - Operations to join our Finance team. This is a full-time position where you will play a crucial role in analysing our business operations and implementing operational improvements. As a Business Analyst - Operations, you will have the opportunity to develop your skills and expertise and learn about operations, transport and manufacturing.What You'll Be DoingWorking alongside the operations side of our business including transport/warehousing/manufacturing – so you will need to have a genuine interest in these areas and a willingness to learn.Analysing financial data, trends and performance to provide insights into business operations and identify areas for improvement.Helping analyse the numbers, providing detailed analysis to the management team and explaining what the numbers mean.Problem solving - identifying and defining business problems and then recommending solutions to address themDeveloping and using financial models to support business cases, evaluate potential investments, and forecast future performance. Learning how the business works - spending time in other departments helping analyse and continuously seeking opportunities to enhance efficiency and drive innovation within the organisation.What We're Looking ForA degree in Business, Mathematics, Finance or Accounting.Strong analytical and problem-solving skills with the ability to think critically and present information in an easy to understand way.Excellent communication and interpersonal abilities to effectively collaborate with other teams.Ability in business analysis techniques, data analysis and ""plain English"" explanations of the numbers.Passion for continuous learning and a desire to contribute to the growth and success of the company.What We OfferWe have a young, committed team and as a Business Analyst - Operations, you will have access to ample opportunities for professional development, including training programs, mentorship and the chance to work on cross-functional projects. We also offer a competitive salary, a comprehensive benefits package and a supportive, collaborative work environment. We offer life and trauma insurance, a wellness bonus and family day.About UsWe are a well-established 100% NZ owned company with over 380 staff and a turnover of over $300 million - we are located in multiple locations over NZ and Australia. We are operating in a growing marketplace and have ample opportunities for the right person. If you are excited about the prospect of joining our team as a Business Analyst - Operations, we encourage you to apply now!"
35,"MuleSoft Technical Lead","Cognizant New Zealand Limited","Auckland CBD; Auckland","4d ago","85428861","https://www.seek.co.nz/job/85428861?ref=recom-homepage&pos=51#sol=3c3d69db8e3aa17bdfbd14e1d61bb70311476885","True","We are seeking an experienced Technology Lead who can collaborate with Customer SMEs to define To Be Business Processes & Prepare Functional Specifications. The lead will act as the point of contact for Client for functional aspects act as a bridge between offshore team and ClientResponsibilities Design reusable assets, components, standards, frameworks, and processes to support and facilitate API and integration projects Design, develop, and maintain MuleSoft APIs and integrations Collaborate with cross-functional teams to gather requirements and deliver solutions Implement best practices for API development, testing, and deployment Provide technical guidance and mentorship to junior developer. Create/Manage Basic CI/CD Pipelines (Github, Teamcity, Octopus(Any Equivalent CI/CD tool) Good hands on experience in developing APIs using Mulesoft platform components including API Manager, CloudHub, AnyPoint Studio, Dataweave and AnyPoint Messaging Queue (MQ).Developing solution features following MuleSoft development best practices, ensuring solution performance and system scalability.Should have knowledge on various out of the box connectors usage, custom connector development and custom API policy development.Testing and improving performance of the APIs in terms of response time, reliability and resource usage.Must have relevant experience in troubleshooting, triage, root cause analysis and performance monitoring of Anypoint API platform.To apply for this role, your soft skills, expertise and experience should include:5+ years of experience in software development with at least 3 years focused on Mulesoft integration solutions.Proven experience in leading development teams and managing integration projects. Proficiency in Mulesoft Anypoint Platform, including Anypoint Studio, API Manager, and CloudHub. Strong knowledge of RESTful and SOAP web services, RAML, XML, JSON, and data transformation. Familiarity with integration patterns, microservices architecture, and SOA. Experience with Java, Spring, and other related technologies is a plus. Excellent problem-solving and analytical skills.Strong communication and interpersonal skills.Ability to work effectively in a team-oriented and fast-paced environment."
36,"Senior Service Desk Engineer","Securecom Ltd","Ellerslie; Auckland","3d ago","85447253","https://www.seek.co.nz/job/85447253?ref=recom-homepage&pos=53#sol=7375941cc79cd09e6e0ac70540dd127c65144a98","True","Are you a tech-savvy problem solver with a passion for mentoring and customer service?At Securecom, we’re all about delivering exceptional IT support with a smile. We’re looking for a Senior Service Desk Engineer who thrives in a fast-paced environment, loves solving problems, and enjoys helping others grow.This is your chance to be a key player in a high-performing team, where your technical expertise and leadership will make a real impact.The Senior Service Desk Engineer plays a critical role in managing a high volume of support calls and service tickets while maintaining a consistently high standard of quality and customer service. With a target of 85% first-call resolution, this role requires exceptional technical troubleshooting skills, a strong customer-first mindset, and the ability to thrive in a fast-paced, high-pressure environment.As a senior member of the team, you will also be responsible for supporting and mentoring our graduate engineers. This includes assisting with escalations, knowledge sharing, and contributing to their development through structured training and hands-on guidance. You will act as a key support to the Integrated Operations Manager, helping to uphold service standards and lead by example in both performance and professionalism. so they know what they are getting into as well as Professional SkillsExcellent verbal and written communication skills.Strong analytical and troubleshooting ability.Ability to multitask and work in a high-pressure, fast-paced environment.Strong interpersonal skills; able to collaborate with diverse teams and clientsWhat You’ll Be Doing:Customer Support & Incident ResolutionProvide top-tier technical support via phone, remote desktop, and ticketing systems.Manage a high volume of tickets and calls while maintaining service excellence.Lead initial investigations for P1/P2 incidents and manage escalations when needed.Keep documentation accurate and up to date in ConnectWise.Team Leadership & MentoringCoach and mentor graduate and junior engineers.Conduct regular check-ins and contribute to performance reviews.Own training plans and foster a collaborative, learning-focused culture.Administration & DocumentationMaintain accurate records and support queue management.Assist with customer reporting and ensure process compliance.Performance & DevelopmentMeet or exceed KPIs and service standards.Stay current with new technologies and maintain certifications.Contribute to service improvements and internal knowledge sharing.Hours: Rotating shifts between 6:00am – 6:00pm, Monday to Sunday (flexibility for weekends and nights required)What We’re Looking For:✅ Minimum 3 years’ experience in a Service Desk or Level 2/3 support role✅ Strong knowledge of Windows, macOS, and basic Linux environments✅ Proficiency in networking (TCP/IP, DNS, DHCP, VPN) and Microsoft 365/Azure AD✅ Experience with ConnectWise, Automate, and remote support tools✅ Excellent communication, troubleshooting, and multitasking skills✅ Previous mentoring experience is a plus✅ Certifications in Microsoft, CompTIA, or similar are highly desirableWhy Join Securecom?Vibrant Culture: Pool tables, arcade games, and a team that genuinely enjoys working togetherCareer Growth: Clear pathways for advancement and leadership developmentCutting-Edge Tech: Work with the latest Microsoft enterprise technologiesSupportive Team: Collaborate with skilled professionals who care about quality and serviceLearning-Focused: A great place to build your IT career and expand your expertiseReady to take the next step?If you're a proactive, customer-focused engineer who’s ready to lead, mentor, and make a difference—we want to hear from you!👉 Apply now and become part of the Securecom story."
39,"Senior BI Analyst","Digital Garage","Auckland CBD; Auckland","Modernise BI tools and reporting in a high-impact enterprise setting","85526554","https://www.seek.co.nz/job/85526554?ref=recom-homepage&pos=56#sol=f20571bbc34dcff252aad4dca3622d162e435a99","True","Why you’d like it: Lead BI innovation at scale in a major retail business. Enjoy strong flexibility, visible impact, and career growth across ANZ. The Story: This well-known retail group is on a journey to transform the way they use data. With heavy investment in BI, AI, and cloud platforms, they’re growing their NZ-based analytics team and looking for a Senior BI Analyst to modernise reporting tools, improve data accessibility, and influence smarter business decisions through self-service and automation. Company Profile: A large-scale retail organisation with a strong trans-Tasman presence and a growing NZ analytics function. This is a collaborative, high-trust environment where data plays a central role in decision-making. The business is embracing GenAI, cloud tech, and continuous upskilling—and you'll have the opportunity to work with senior stakeholders and access broader ANZ career pathways. Your Role: Reporting to the BI Manager, you’ll join a small, senior team responsible for: Uplifting BI reporting and dashboards across the NZ business Driving Group-first metrics and enabling self-service analytics Migrating from Tableau to Looker and embedding best practice Partnering with Data Engineering and Group BI teams Bringing innovation, AI and automation into BI delivery Benefits: Competitive salary + short-term bonus Health insurance + a range of discounts Hybrid working - 3 days onsite, 2 from home Learning & growth opportunities across NZ and AU Your Fit: You’re a senior-level BI Analyst with 5+ years’ experience across the full BI lifecycle. You’re highly proficient in SQL, with strong Tableau or Looker skills, and ideally bring Python for automation. You thrive in a collaborative setting, can confidently engage stakeholders, and are passionate about innovation, GenAI and modern BI tools. A background in retail, FMCG, or consulting is a plus.Apply today for more info!"
41,"Jade Software Engineer","Find IT Recruitment","Auckland CBD; Auckland","Key role working on critical banking platforms","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=59#sol=66936b98670c93d751643015688e78706cc7f889","True","Your new company: This New Zealand-owned bank is unique in being 100% owned by a philanthropic trust. With profits reinvested into local communities, they blend innovation, impact, and customer care through strong digital and service offerings. Your new role: As a Software Engineer, you’ll contribute to the ongoing development and support of enterprise-grade business applications using JADE. You'll be part of a cross-functional agile team focused on secure, scalable systems that underpin core banking operations. Responsibilities: Design, develop, and maintain JADE-based applications and APIs Collaborate with agile squads to deliver reliable, secure systems Contribute to ongoing enhancements of platforms and services Maintain standards for performance, data integrity, and security Help resolve system incidents and support 24/7 operations where required Requirements: Commercial experience developing in JADE Experience in agile software delivery environments Exposure to enterprise or core banking systems Strong focus on secure, high-availability application development Willingness to participate in operational support Perks and benefits: Contribute to meaningful, community-first outcomes Work with a collaborative, supportive engineering team Flexible working options and a strong focus on wellbeing Join a truly New Zealand-owned organisation If this sounds like you then HIT APPLY NOW! You must have a valid working visa for NZ."
43,"Analytics Engineer","The Cause Collective","Manukau; Auckland","5d ago","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=61#sol=11b603502dfa190c8057d04f893bfbb370a6a8b7","True","Tātou Collective is a Whānau Ora commissioning agency supporting Pacific families across Aotearoa. The agency works with families with the greatest needs to strengthen wellbeing, build resilience and self-determination. We are looking for an experienced Analytics Engineer to design and maintain robust data pipelines and deliver clear, accessible insights to our teams and partners. You’ll work with cloud technologies, develop BI products, and contribute to the design of new data-driven tools and CRM solutions.This role is ideal for someone who combines technical skill with cultural understanding, thrives in a collaborative environment, and is passionate about using data to drive social impact. Key requirements:Proven experience designing cloud data pipelines and infrastructure as code.Strong skills in Python, SQL, and BI tools.Ability to translate complex data into clear insights for diverse audiences.Understanding of Pacific data sovereignty and cultural contexts.Critical thinking skills with a focus on continuous improvement. Some out-of-hours work and travel will be required. Join us in making a difference.To learn more or apply <NAME_EMAIL>"
45,"Digital Design Developer","VPR","Waitakere & West Auckland; Auckland","Combine design & coding skills for IT products & services digital/print co.","85538660","https://www.seek.co.nz/job/85538660?ref=recom-homepage&pos=64#sol=729df8fe53ece119503eecc32559f6c4a7b7a4b5","True","Digital Design DeveloperMy client has a proven history in providing digital, print and mailing solutions to a wide range of clients.The role is office based and is part of a dedicated technical and design team reporting to the technology manager. The purpose of the role is to provide innovative and progressive IT products and services for digital and print communications, which meet internal and external criteria.To fit the team, you will have a positive, flexible, 'can-do' attitude, be team spirited and able to deliver results. As it is important to you to meet and surpass standards you will have a high attention to detail, a thirst for learning and a passion for meeting deadlines.To be considered for the role you will offer the following technical skills. HTML, CSS, Java Script, SQL, Adobe InDesign, and MS Office suite. Additional skills and experience such as graphic design, Adobe Photoshop, XMPie, Pres Connect C#/.Net, Type Script, RESTful API and Visual Basic will be advantageous and considered highly.Remuneration is exp linked sitting around $65 to 75K, however those with higher salaries and exp. should also apply. Also note the company is close to the motorway and offers onsite free parking.The role requires a good command of oral and written English to understand client requirements and specifications along with the ability to work as part of a team.It is only open to those who have the right to work full time in N.Z. and no overseas applications will be accepted unless you are intitled to work here.Please send an up-to-date CV, along with a cover letter, which if appropriate includes evidence of your work permit and your current remuneration.Contact Viv Poppelwell, Director VPR Ltd. Email Viv, <EMAIL> or call Viv on 0274974997 to discuss what you bring to the company."
46,"Senior Solutions Architect","Presto Resourcing","Auckland CBD; Auckland","Great company culture with career progression","85178521","https://www.seek.co.nz/job/85178521?ref=recom-homepage&pos=65#sol=9cbbdeae1ff2cf7fd5b5a6a1c52282a301cbdc97","True","Our client has an immediate requirement for a seasoned professional Senior Solutions Architect to join their successful Software Services company here in Auckland. Your role will be highly engaged in various work streams covering a multitude of programmes and projects across New Zealand nationally. Working Internally and externally with customers & Vendors to drive efficiency, simplification, standardisation and improving business outcomes. To be successful in this role, you will need to have the following skillsets and experience: 5+ Years commercial experience working as a Solution Architect Infrastructure experience in Telco, firewalls, ideally a managed services provider. Experience with software & integration, API's, working with SW Architects Security & Cyber Security, having previously worked with security architects to define & work through the security domain. Experience working on large projects with stakeholders across multiple organisations Stakeholder engagement, ability to present, run workshops ,engage, simplify tech with senior stakeholders is key. Design scalable solutions. Excellent comms, approachable & easy to work with. We are only accepting candidates that have the right to work in NZ."
48,"EUC Engineer","Lexel – Resourcing - Connecting IT Talent with Opportunity","Auckland CBD; Auckland","Be part of a high-performing; fun team making a real impact in healthcare","85313912","https://www.seek.co.nz/job/85313912?ref=recom-homepage&pos=67#sol=f8fe01d56b406a1fe112a6d53c1e5b8caec6d945","True","This role offers more than just technical challenges — it gives you the chance to use your skills in an environment that truly makes a difference. We’re looking for a customer-focused EUC Analyst with strong enterprise experience and a passion for technology and service excellence. If you enjoy working with modern Microsoft platforms, thrive in a collaborative team, and want to grow your career in a meaningful setting, this could be the perfect fit. What’s in it for you? Be part of a tight, collaborative team known for its camaraderie and “exceptional banter” Work across a modern Microsoft stack: Intune, SCCM, Azure AD, M365, Teams and more Contribute to a mission-driven organisation that improves the lives of Kiwis Structured onboarding program and clear development pathways Generous base salary, benefits Hybrid flexibility with a mix of onsite and remote work (typically 1–2 days WFH) What you’ll be doing: Providing Level 2/3 EUC support for end-user devices, apps and services Supporting and managing device configuration and endpoint security using Intune, SCCM/MECM and Conditional Access Contributing into the continuous improvement initiatives to improve the experience for our customers Troubleshooting across the whole EUC landscape - Active Directory, M365, utilizing management apps and collaboration tools Utilizing automation and scripting to solve problems without disrupting users Collaborating with a range of clinical and business users, balancing urgency with empathy Creating documentation and maintaining knowledge base entries Participating in small project delivery and contributing to service improvement Growing your career on a people-focussed technical team What we’re looking for: Solid enterprise-level EUC experience in a Microsoft Windows environment Confident with SCCM, M365, AD/Entra ID, Group Policy, Intune, and Conditional Access Policies Familiar with PowerShell scripting (or keen to deepen your skills) Strong grasp of ITIL practices and use of tools like ServiceNow Calm under pressure with excellent customer service and communication skills Permanent residency or NZ citizenship Bonus points for: Experience in the healthcare sector or other large public organisations Exposure to Teams/SharePoint governance or Exchange Online Application packaging and deployment experience A collaborative, team-first approach — and a sense of humour Sound like you? Apply now and bring your skills to where they’ll make a real impact — while continuing to grow in a supportive and forward-thinking team."
49,"IT Support Engineer","CallCentre People","Auckland CBD; Auckland","11d ago","85178538","https://www.seek.co.nz/job/85178538?ref=recom-homepage&pos=69#sol=847ce008a43aafca0ebbceb72499bb176707480d","True","We are looking for a technology-oriented and solution-focused IT professional to become a part of our vibrant team. In this role, you’ll play a key part in maintaining and enhancing our IT infrastructure, supporting users, and ensuring the seamless operation of critical systems. This is an exciting opportunity for someone who thrives on problem-solving, values innovation, and is eager to contribute to the digital growth and security of a forward-thinking organization.Our client is a specialist SaaS provider of contact centre solutions. We deliver modern, cloud-based applications that help businesses transform customer experience and contact centre performance. Our clients include leading brands across a variety of sectors, with a strong focus on innovation and customer service.Role Overview:We are seeking a skilled IT Support Engineer to join our team. This role provides technical support across both internal systems and external customer platforms. You will be responsible for supporting hardware, software, and cloud-based solutions for internal staff, as well as providing remote support for our customers using our client’s SaaS applications and cloud telephony services.This is a varied role, requiring excellent problem-solving skills, a broad understanding of cloud-based technologies (especially Microsoft and AWS), and the ability to communicate clearly with both technical and non-technical users. The role also includes participation in an after-hours on-call support roster.Key ResponsibilitiesProvide day-to-day IT support for internal staff across hardware, software, and cloud-based systemsSupport Office 365, Exchange Online, Windows Servers, and other Microsoft technologiesManage and troubleshoot AWS services, including Amazon Connect telephonyDeliver remote support for our client's SaaS applications and cloud telephony platforms to external customersHandle support tickets and queries in a professional and timely mannerAssist with on-boarding new users and devicesParticipate in an after-hours on-call roster for critical supportDocument support procedures and maintain knowledge base articlesWork closely with development and operations teams to escalate and resolve issuesRequirements:Proven experience in an IT support or help-desk roleGood working knowledge of the Microsoft technology stack: Office 365, Exchange Online, Windows ServerFamiliarity with Amazon Web Services (AWS), cloud-based telephony and contact centre environments (beneficial)Strong troubleshooting and problem-solving skillsAbility to communicate clearly in English, both written and spokenExperience supporting web-based applications and remote usersAbility to manage and prioritize tasks effectivelyFlexible and willing to work after hours as requiredBenefits:Work with a modern, forward-thinking SaaS companyExposure to leading cloud technologies and contact centre solutionsSupportive team environment with opportunities for professional growthFlexible working arrangementsCompetitive salary based on experienceIf this sounds like you then we want to hear from you today!"
50,"Senior Analytics Engineer","SKY TV","Auckland CBD; Auckland","Shape enterprise data with cutting-edge tools like dbt & Snowflake","85149942","https://www.seek.co.nz/job/85149942?ref=recom-homepage&pos=70#sol=8b27793c5d32cb7e49b554f5ba8a26897208dd20","True","KO WAI MĀTOU I ABOUT SKY Welcome to Rangiata | Sky, the ultimate entertainment destination that connects Kiwis to the stories and sport they love, in ways tailored just for them. Our Sky crew work tirelessly to ensure we are Aotearoa's most engaging and essential media company. We're incredibly proud of our achievements, but that doesn't mean we're satisfied. As innovators, we constantly challenge ourselves to redefine boundaries and adapt to the ever-changing marketplace. Come be your authentic self at Sky, where we actively listen and adapt to create shared experiences that excite both our customers and our crew. As an Equal Employment Opportunity (EEO) employer, we proudly champion inclusivity and diversity as our core values, and we are open to applicants of all gender identities to feel welcomed and supported during the application process. We have included a ""Diverse"" category for those who identify as non-binary, genderqueer, genderfluid, or any other gender identity outside the binary system of male/female. TE TŪRANGA I THE ROLE: Do you get excited about unlocking the power of data to drive better decisions? We're on the lookout for a Senior Analytics Engineer to join our growing team and help shape how data is transformed, modelled, and shared across Sky. Sitting within the Analytics Engineering team and reporting to the Lead Analytics Engineer, you'll play a key role in building clean, secure, scalable data models using dbt and Snowflake, making sure our enterprise-wide analytics solutions are reliable and ready to support business decision-making. You’ll work closely with stakeholders across Sky to ensure our data is structured and surfaced in a way that’s useful, usable, and impactful. This is a great opportunity to work in a fast-moving and forward-thinking team that’s helping to evolve Sky’s data capabilities and deliver value to our customers, our people, and our business. Key responsibilities will include: Designing, transforming and deploying high-quality data sets using dbt and Snowflake Collaborating with business units to understand and meet their data needs Creating scalable, secure, and maintainable data models Managing version control, CI/CD pipelines, and maintaining data documentation Enabling robust and governed access to reliable and actionable data NGĀ PŪKENGA ME NGĀ WHEAKO I WHAT YOU WILL BRING You’re an innovative thinker with a passion for data and a deep understanding of analytics engineering best practice. You love solving complex problems with simple, elegant solutions and thrive in a collaborative, fast-paced environment. You’ll bring technical depth, curiosity, and a user-first mindset – keen to ensure Sky’s data is working hard to power decisions, insights and strategy. ESSENTIAL: Proven experience with dbt (or similar tools) and cloud data platforms (e.g. Snowflake) Strong SQL skills and understanding of data transformation principles Experience designing and maintaining scalable, reusable data models Familiarity with version control (e.g. Git) and CI/CD in a data environment Ability to translate business requirements into technical data solutions PREFERRED: Experience with data governance practices Familiarity with data visualisation tools such as Power BI or Looker Exposure to software engineering best practices in a data context Experience with data analysis and insights Strong communication and stakeholder engagement skills KO NGĀ HUANGA I WHAT’S IN IT FOR YOU? Discounted Southern Cross health insurance Discounted Gym Membership at the YMCA Free parking onsite (Mt Wellington office) Amazing career development opportunities Free Sky, Sky Sport Now and Neon ME PĒHEA TE TONO I HOW TO APPLY If this opportunity excites you and you want to join us in sharing stories, possibilities, and joy, apply online today. Please note that you must have the right to work in New Zealand or hold a valid work visa for at least 12 months from joining Sky. We’re reviewing applications as they come in, and once we’ve found the right addition to our Sky Crew, we’ll blow the final whistle on this ad. If you’re ready to get off the sidelines and into the action, apply now. At Sky, we understand that conventional recruitment processes may limit some individuals. That's why we strive to support all candidates, offering reasonable adjustments throughout the recruitment process. If you need any accommodations, please reach out to us at Sky jobs, <EMAIL>. RECRUITMENT AGENCIES: Sky has established partnerships with a small number of preferred recruitment agencies, and we will not engage with unsolicited communication outside of this pool."
52,"IT Application Specialist","salt","East Tamaki; Auckland","Globally renowned FMCG brand with a very strong NZ presence","85425456","https://www.seek.co.nz/job/85425456?ref=recom-homepage&pos=72#sol=001fe61e671038e9c8f11091f2f20e1dfa054659","True","As you've likely ascertained, this is an autonomous IT role where you will be the sole IT Applications Specialist in Auckland (however, working with an INCREDIBLE team - great culture with people who value respect, a laugh and hard work - the perfect balance!) wearing multiple hats. A great brand, FREE coffee, loads of growth, you won't want to miss out! DUTIES INCLUDE:Connecting with IT Manager to ensure that the Application Support Services meet Business expectations, always ensuring systematic feedback from/to the local business & stakeholders is regularly collected and providedYou'll be the key liaison across the four business units, the IT teams, and external IT partners (excellent communication skills are key)Ensure Audit/Risk/Security Compliance (including licensing) of Applications is managed at all timesResponsible for budget control and reporting of Applications (actuals, forecast, AOP submissions) towards Cluster ITMThis is going to be a varied role where no two days will be the same!WHAT'S IN IT FOR ME & WHO AM I?A role to make your own and gain that foot in the door to further develop your IT skillsIncredibly strong global FMCG brand (rare to gain FMCG experience without already having it!)Secure industry, accessible location and incredible team culture - this business is incredibly people-centricCompetitive salary to provide security in inflated marketYou will ideally have 3 years IT experience (a foundation to build on)Ability to work autonomously without local supervisionYou'll have innate Data Analytics skills, advantage to have Power BI, Power Automate and Salesforce experience Interested? Apply now!"
53,"Software Implementation Trainer (Retail Point of Sale)","Megabus Software Pty Ltd","Auckland CBD; Auckland","Play a vital role in helping businesses maximise their software investment","********","https://www.seek.co.nz/job/********?ref=recom-homepage&pos=73#sol=68fd8efffd9054268bba860ca0024614ebdfdfeb","True","About the CompanyMegabus Software, an Australian business with a New Zealand entity, is a leading provider of enterprise software solutions for the Automotive Aftermarket industry. For over 40 years, we’ve been delivering comprehensive business management, accounting, and hardware solutions to independent, multi-company, and multi-location businesses across Australia, New Zealand, Southeast Asia, and North America.We’re seeking an experienced and enthusiastic trainer to join our Professional Services team—someone who can translate software functionality into clear, engaging learning experiences that drive real results for our clients.The ideal candidate will play a key role in onboarding and upskilling users, helping clients maximise the value of our solutions, and supporting our strategic growth in the New Zealand market.Position OverviewAs a Professional Services Consultant with a focus on training, you will design, develop, and deliver tailored learning programs that ensure users are confident and proficient in utilising our software. Your work will help clients achieve their business goals and maximise their return on investment in Megabus solutions.Key ResponsibilitiesDesign and deliver tailored training programs for various client roles and business contexts.Facilitate in-person and virtual training sessions, adapting to different learning styles and skill levels.Develop engaging training materials such as manuals, tutorials, and interactive user guides.Provide ongoing support, including refresher training and strategies to encourage user adoption.Collaborate with internal teams to remain up to date on product features and integrate them into training.What You BringProven experience in training, instructional design, or education—preferably within a software or IT environment.Excellent communication and presentation skills, with a knack for simplifying technical concepts.Strong understanding of adult learning principles and various training methodologies.Confidence in delivering both face-to-face and virtual training to individuals and groups.A genuine passion for teaching, client success, and continuous improvement."
55,"Graduate Business Analyst - Operations","Private Advertiser","Mount Wellington; Auckland","11d ago","85177623","https://www.seek.co.nz/job/85177623?ref=recom-homepage&pos=77#sol=f812a4a740b715ae107296d8da36189925d86cbc","True","About the RoleIs Math's your thing? Do you love data analysis problem solving?Well we have the role for you!We are seeking a talented and enthusiastic Graduate Business Analyst - Operations to join our Finance team. This is a full-time position where you will play a crucial role in analysing our business operations and implementing operational improvements. As a Graduate Business Analyst - Operations, you will have the opportunity to develop your skills and expertise and learn about operations, transport and manufacturing.What You'll Be DoingWorking alongside the operations side of our business including transport/warehousing/manufacturing – so you will need to have a genuine interest in these areas and a willingness to learn.Analysing financial data, trends and performance to provide insights into business operations and identify areas for improvement.Helping analyse the numbers, providing detailed analysis to the management team and explaining what the numbers mean.Problem solving - identifying and defining business problems and then recommending solutions to address themDeveloping and using financial models to support business cases, evaluate potential investments, and forecast future performance. Learning how the business works - spending time in other departments helping analyse and continuously seeking opportunities to enhance efficiency and drive innovation within the organisation.What We're Looking ForA degree in Business, Mathematics, Finance or Accounting.Strong analytical and problem-solving skills with the ability to think critically and present information in an easy to understand way.Excellent communication and interpersonal abilities to effectively collaborate with other teams.Ability in business analysis techniques, data analysis and ""plain English"" explanations of the numbers.Passion for continuous learning and a desire to contribute to the growth and success of the company.What We OfferWe have a young, committed team and as a Graduate Business Analyst - Operations, you will have access to ample opportunities for professional development, including training programs, mentorship and the chance to work on cross-functional projects. We also offer a competitive salary, a comprehensive benefits package and a supportive, collaborative work environment. We offer life and trauma insurance, a wellness bonus and family day.About UsWe are a well-established 100% NZ owned company with over 380 staff and a turnover of over $300 million - we are located in multiple locations over NZ and Australia. We are operating in a growing marketplace and have ample opportunities for the right person. If you are excited about the prospect of joining our team as a Graduate Business Analyst - Operations, we encourage you to apply now!"
56,"Senior Node.js Developer","Socialite Recruitment Ltd.","Location not found","Work with a team of experts and work with modern tech","85444835","https://www.seek.co.nz/job/85444835?ref=recom-homepage&pos=78#sol=417ab8b4e2ee27b59dccbe1049213e8936bf26f3","True","We’re looking for x2 Senior Node.js Developers who have extensive experience leading commercial microservices and API development. We will also consider a good intermediate who has a genuine passion for fintech.What you’ll bring: Proven leadership in commercial software development with Microservices and APIs Deep expertise in Node.js frameworks like Fastify, Express.js, or Molecular Strong background in API development and integration (REST/JSON, NATS, Kafka) In-depth knowledge of database systems, preferably MongoDB Hands-on experience with CI/CD pipelines and automation tools Solid DevOps skills including Jenkins, GitLab CI, Docker, Kubernetes and OpenShift Experience with application monitoring and logging (OpenSearch, Splunk) Cloud computing expertise, especially in Google Cloud Platform (GCR, GKE, CloudBuild) Experience designing, developing, testing and deploying microservices in cloud environments. Why Join? Lead exciting projects in a collaborative environment Influence the future of the enterprise software solutions Competitive compensation and benefits Grow your skills with cutting-edge technologies. Ready to lead innovation? Apply today and be a key contributor to the mission of delivering high-quality microservice solutions!"
57,"Senior Software Engineer","Tether","Auckland CBD; Auckland","Friendly; motivated team","85248767","https://www.seek.co.nz/job/85248767?ref=recom-homepage&pos=79#sol=fef02f213e6347c00279edf5cf362dcf67b9dbc1","True","Senior Software Engineer – Backend / Full Stack📍 Auckland (NZ), Full-time (Hybrid)Join us for your next adventure! We’re looking for a senior engineer who wants more than just clean tickets, someone who’s keen to build ambitious tech, own real decisions, and help shape the future of how buildings work.🌍 Why We ExistAt Tether, we're out to do our part to build a better world. We're heavily involved in carbon reduction and renewable energy projects for some big names as we re-imagine building performance. From energy and air quality to occupancy and compliance, our platform brings real-time visibility into how buildings are used, where they're wasting resources, and how they can improve. We’re a small team doing big things and we're looking for someone like you to join our crew!🧠 The RoleThis isn’t a “just ship the feature” gig.You’ll be the glue holding our back-end together and the force that helps pull it into the future. We need someone to run point on a hybrid monolith-to-microservices architecture that powers everything from IoT ingestion to data analysis, insights and property certification workflows.We’re not blind, we know there’s legacy code. We’re peeling bits off and updating them as needs dictate and time allows but there’s plenty of greenfield work coming through, and with new IoT devices joining the family all the time there’s no shortage of interesting challenges just around the corner.You’ll work closely with the Head of Engineering and have major input on architectural decisions, roadmap priorities, and technical direction.🔧 The StackWe’re pragmatic, not purists. Our tech choices are grounded in what's proven and what's next.Backend: Node.js (Express), TypeScript, Sequelize, DockerFrontend: React, Chakra UI, TypeScriptData & Infra: AWS (Lambda, S3, DynamoDB, SQS), PostgreSQL, MySQL, Serverless Framework, TerraformDev Tools: Mocha, Chai, Storybook, ESLint, Zod, YupOther: JWT, OCR + AI integrations on the horizon🧩 What You’ll Be DoingOwn and evolve our backend systems, from core APIs to micro-servicesArchitect and build new features tied deeply to our data ecosystemKeep legacy systems humming while charting the path to future stateLead backend design on new modulesHelp scale infrastructure, improve CI/CD, and keep us secure and performantCollaborate across product, engineering, and data teams to ship smart solutionsMentor and uplift the growing team through code, review, and conversation🏆 What You Bring5+ years of backend or full stack engineering experienceDeep experience with TypeScript, Node.js, Docker, AWS or similar technologiesComfort navigating and refactoring a maturing codebaseAn instinct for pragmatic trade-offs — you know when to rebuild and when to patchBonus: exposure to IoT or real-time data systems💚 Why Join UsMake it yours – You’ll own major decisions and shape architecture from day oneDo real good – Your work helps cut carbon and improve building healthAvoid the BS – We’re low on politics, high on impactWork-life balance – Flexible hours, hybrid options, and a culture that gets itReady to roll up your sleeves?If this sounds like you, get in touch. We are looking forward to hearing from you!"
59,"Principal Front End Engineer","Comspek International Limited","Auckland CBD; Auckland","NZ Owned / Agile / Hybrid Role / Principal Level","85537483","https://www.seek.co.nz/job/85537483?ref=recom-homepage&pos=82#sol=f36c9851f4b568b47031ed8ee0e657a4f2f60ba9","True","We’re looking for a Principal Front-End Engineer to lead the design and development of our clients user-facing products and front-end platform. In this senior-level role you’ll drive architectural decisions, champion front-end best practices, and play a key part in delivering our technical road-map. You’ll work closely with cross-functional teams, influencing both technical and strategic direction while staying hands-on in coding, mentoring, and innovating. What You’ll DoLead front-end architecture and system design for scalable, maintainable applicationsDrive best practices in performance, testing, accessibility, and developer experienceCollaborate across product, design, and engineering to bring world-class experiences to lifeMentor and guide engineers, fostering growth and technical excellenceInfluence and contribute to foundational work on our component library and design systemWhat You BringBachelor’s or Master’s in Computer Science, Engineering, or related fieldDeep expertise in JavaScript/TypeScript, ReactJS, NextJS, and modern front-end testing tools like Vitest and PlaywrightStrong understanding of build tools such as Webpack, Babel, or esbuildSolid exposure to .NET technologies (e.g. .NET Core, .NET 7, C#, SQL Server, DynamoDB)Experience with CI/CD pipelines (GitHub Actions, GitLab CI/CD, or Jenkins)Familiarity with AWS or Azure, and containerization tools like Docker or KubernetesExperience working with component libraries and design systems (e.g. Backlight, Storybook)A strong foundation in leading technical discussions and influencing key decisionsPassion for developer experience, product quality, and building technical foundations Why Apply? This is your chance to lead the front-end strategy of a growing tech-driven company with the support of a collaborative, high-performing team. You’ll have autonomy, influence, and the chance to shape the future of our platform.Kia ora, Comspek and our clients fully support and encourage diverse hiring and inclusive recruitment processes. Don’t meet every single requirement of this job description? That’s ok - You do not need to tick every box or have expertise in the full JD. Comspek is dedicated to building diverse, inclusive and authentic workplaces based on different clients’ needs. So, if you’re excited about this role, we encourage you to apply."
60,"Senior IT Analyst","Robert Half Technology","Manurewa East; Auckland","24d ago","84894624","https://www.seek.co.nz/job/84894624?ref=recom-homepage&pos=83#sol=fd5cbb36beb4d1fb9ed6e1607d96f2fed5ea2066","True","The CompanyA global organisation at the forefront of their industry is seeking a talented Senior IT Analyst to join its innovative and collaborative team. With a mission to positively impact the health of livestock and all animals sustainably, the company provides advanced pharmaceutical solutions for both farm and companion animals. Their operations span six sites across New Zealand, the United States, Scotland, Spain, and Germany, employing nearly 500 employees globally. Originally founded in 2006 by a New Zealand chemist, this organisation is deeply rooted in entrepreneurship, innovation, and a passion for making a meaningful impact. Now is your chance to be part of a dynamic company that values your contributions and supports your growth.The Role As a Senior IT Analyst, you will play a crucial role in supporting IT operations at a local site while contributing to the global IT initiatives of a fast-paced and rapidly growing company. This hands-on position includes troubleshooting daily technology issues, managing user requests, and enhancing the company's infrastructure, security, and business systems. You will act as a trusted technical expert and partner to end-users, ensuring an optimal IT landscape that aligns with global standards and enables business success.In addition to day-to-day support, you'll have opportunities to assist with exciting IT projects, make process improvements, and adopt new technologies. If you enjoy balancing technical support with project work in a collaborative environment, this role presents an excellent opportunity to elevate your career.ResponsibilitiesProvide day-to-day IT support across devices, systems, and networks.Troubleshoot technical issues and manage user requests in a timely manner.Maintain and enhance infrastructure, security protocols, and business systems.Manage service desk processes and utilise ticketing tools effectively.Collaborate with end-users to deliver tailored IT solutions.Support LAN/WAN networking, virtualisation systems, and hardware across environments.Contribute to global IT projects and implement process improvements.* Assist in ensuring compliance with cybersecurity best practices.Your profile: You are a proactive, tech-savvy problem solver with a passion for improving systems and delivering high-quality IT service. You excel at balancing diverse tasks and thrive in a collaborative, fast-moving environment. You communicate clearly, work well under pressure, and take ownership of tasks with a solutions-focused mindset.Qualifications and Experience:Tertiary qualification in an IT-related field.Experience with IT service methodologies such as ITIL.Progress toward certifications in specialised technologies is a plus.Strong troubleshooting skills across Windows, Microsoft 365, and common business applications.Familiar with tools and protocols like ADUC, DNS, DHCP, file and print services.Solid understanding of LAN/WAN networking, virtualisation, and hardware support.Experience supporting mobile devices across Windows, iOS, and Android platforms.Exposure to infrastructure, cybersecurity, and business applications environments.Excellent time management and organisational skills.Ability to prioritise tasks and drive results independently.About You:You have outstanding communication skills-both written and verbal.You thrive on solving problems and delivering excellent customer service.You embrace learning new technologies with curiosity and enthusiasm.You're highly organised and can manage multiple priorities effectively.You maintain a solutions-focused mindset and a collaborative approach to teamwork.You possess the valid right to work in New Zealand.If you're ready to take your career to the next level and contribute to meaningful global initiatives, don't miss this exciting opportunity to join a passionate, innovative team that makes a difference in animal health every day. Apply today!How to applyPlease send your resume in Microsoft Word format by clicking on the apply button or for further information, Stanley Onacha in our Auckland office on 09-915-6700.Job Reference No: 06900-0013242059By clicking 'apply', you give your express consent that Robert Half may use your personal information to process your job application and to contact you from time to time for future employment opportunities. For further information on how Robert Half processes your personal information and how to access and correct your information, please read the Robert Half privacy notice: https://www.roberthalf.co.nz/privacy-statement. Please do not submit any sensitive personal data to us in your resume (such as government ID numbers, ethnicity, gender, religion, marital status or trade union membership) as we do not collect your sensitive personal data at this time."
62,"Lecturer - Masters in Data Analytics","New Zealand Skills and Education Group","Auckland CBD; Auckland","Full-time; Permanent I Brand new Auckland CBD Campus","85445435","https://www.seek.co.nz/job/85445435?ref=recom-homepage&pos=85#sol=5364c01792e8a1d6aea298848c5557be24f877f0","True","Ko wai mātou | Who are weNew Zealand Skills and Education College (NZSE) has been transforming lives through education across Aotearoa and globally for over 20 years. As an accredited provider, we offer innovative, industry-relevant programmes from foundation to Masters level, supporting both domestic and international learners in their academic and career goals.We provide a holistic educational experience—combining personalised academic support, strong pastoral care, and inclusive learning environments. Our campuses foster diversity, collaboration, and real-world skill development, helping learners thrive in university or the workforce.To learn more about us, please visit our websites: www.nzse.ac.nz, www.nzseg.com, www.edvance.ac.nz, www.seafiled.ac.nz Mō tēnei tūranga mahi | The opportunityWe are seeking a passionate, learner-centred experienced Lecturer to deliver and develop our Master of Data Analytics (Level 9) programme. You will be responsible for developing the programme, teaching and supervising postgraduate students in our Master of Data Analytics. This role involves curriculum delivery and development, pastoral care, research engagement, and collaboration with industry.Key Responsibilities:• Deliver engaging, inclusive postgraduate-level content through face-to-face and online delivery (via Canvas LMS and MS Teams)• Design, develop, update, and assess assessments aligned with NZQA standards • Supervise student research and industry capstone projects• Incorporate te reo me ngā tikanga Māori, Pasifika values, and principles of Mana Ōrite into your teaching• Engage in scholarly research and industry-connected projects• Facilitate guest lectures, field trips, and build industry connections to support real-world learningKo koe tēnei | About youYou are a reflective educator with a deep commitment to learner success. You bring a strong academic background, industry-relevant experience, and a passion for innovative pedagogy in business intelligence.You will have:• A PhD Business, Technology or similar + active involvement in research,• Have taught papers in Big Data, AWS, Machine Learning or Computer Science• Minimum tertiary-level teaching experience (Level 9 or above) in New Zealand• Strong understanding of Te Tiriti o Waitangi and ability to embed its principles in your teaching• Experience with NZQA programme design, assessment, and moderation• Diploma (Level 6) or NZ Certificate in Adult Tertiary Teaching (Level 5)or willingness to work towards one• Industry experience working as Business Analyst or similarNgā painga | What we offerAt NZSE, we value our kaimahi and provide a supportive, culturally rich environment. You’ll benefit from:• Competitive salary and opportunities for academic progression• Annual professional development budget• Free flu vaccination and birthday leave after 12 months of service• Staff referral bonuses• Opportunities to influence curriculum and shape strategic direction• A strong organisational commitment to equity, diversity, biculturalism, and Māori and Pasifika successMe pēhea te tuku tono | How to applyClick APPLY and submit your CV and cover letter explaining why you are the ideal candidate. We are reviewing applications as they are received, so early applications are encouraged.At NZSE, we value and welcome diversity. We encourage applications from all backgrounds, especially Māori, Pasifika, and individuals committed to equity in education. If you’re excited about the role but unsure if you meet every requirement—apply anyway. We’d love to hear from you."
